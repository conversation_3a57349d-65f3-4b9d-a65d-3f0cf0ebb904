#ifndef COMPETITION_SELECTION_VIEW_H
#define COMPETITION_SELECTION_VIEW_H

#include <QtWidgets>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QListWidget>
#include <QPushButton>
#include <QLabel>
#include <QJsonArray>
#include <QJsonObject>
#include <QProgressBar>
#include <QTimer>

/**
 * @brief 赛事选择界面组件
 * 
 * 提供用户友好的比赛列表显示和选择功能，支持从API获取比赛数据
 * 并允许用户选择要管理的比赛。
 */
class CompetitionSelectionView : public QWidget
{
    Q_OBJECT

public:
    explicit CompetitionSelectionView(QWidget *parent = nullptr);
    ~CompetitionSelectionView();

    /**
     * @brief 加载比赛列表数据
     * @param competitions JSON数组包含比赛信息
     */
    void loadCompetitions(const QJsonArray &competitions);
    
    /**
     * @brief 获取当前选中的比赛ID
     * @return 比赛ID，如果没有选中则返回-1
     */
    int selectedCompetitionId() const;
    
    /**
     * @brief 清空比赛列表
     */
    void clearCompetitions();

    /**
     * @brief 显示加载状态
     * @param loading 是否正在加载
     * @param message 加载消息
     */
    void showLoadingState(bool loading, const QString &message = QString());

    /**
     * @brief 显示错误信息
     * @param errorMessage 错误消息
     * @param allowRetry 是否允许重试
     */
    void showError(const QString &errorMessage, bool allowRetry = true);

    /**
     * @brief 显示离线模式提示
     */
    void showOfflineMode();

signals:
    /**
     * @brief 用户选择比赛后发出的信号
     * @param competitionId 选中的比赛ID
     */
    void competitionSelected(int competitionId);
    
    /**
     * @brief 用户取消选择发出的信号
     */
    void selectionCancelled();

    /**
     * @brief 请求重新加载比赛列表的信号
     */
    void retryRequested();

private slots:
    void onSelectButtonClicked();
    void onCancelButtonClicked();
    void onListItemDoubleClicked();
    void onListSelectionChanged();
    void onRetryButtonClicked();

private:
    void setupUI();
    void connectSignals();
    void updateButtonStates();
    QString formatCompetitionItem(const QJsonObject &competition) const;
    
    // UI组件
    QVBoxLayout *m_mainLayout;
    QLabel *m_titleLabel;
    QListWidget *m_competitionsList;
    QHBoxLayout *m_buttonLayout;
    QPushButton *m_selectButton;
    QPushButton *m_cancelButton;
    QPushButton *m_retryButton;

    // 状态显示组件
    QProgressBar *m_progressBar;
    QLabel *m_statusLabel;
    QLabel *m_errorLabel;

    // 数据
    QJsonArray m_competitions;
    int m_selectedId;
    bool m_isLoading;
};

#endif // COMPETITION_SELECTION_VIEW_H