#include "database_manager.h"

#include <QCoreApplication>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QSqlDriver>
#include <QMutexLocker>

// Static member initialization
DatabaseManager* DatabaseManager::s_instance = nullptr;
const QString DatabaseManager::DATABASE_CONNECTION_NAME = "HighJumpScorerDB";

DatabaseManager* DatabaseManager::instance()
{
    if (s_instance == nullptr) {
        s_instance = new DatabaseManager();
    }
    return s_instance;
}

DatabaseManager::DatabaseManager(QObject *parent)
    : QObject(parent)
    , m_isInitialized(false)
{
    // Constructor implementation
}

DatabaseManager::~DatabaseManager()
{
    close();
}

bool DatabaseManager::initialize()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_isInitialized) {
        return true;
    }
    
    qDebug() << "Initializing DatabaseManager...";
    
    // Create database file path
    if (!createDatabaseFile()) {
        qCritical() << "Failed to create database file";
        return false;
    }
    
    // Connect to database
    if (!connectToDatabase()) {
        qCritical() << "Failed to connect to database";
        return false;
    }
    
    // Setup error handling
    // NOTE: Advanced error handling planned for v1.1
    // setupErrorHandling(); // Basic error handling is already implemented
    
    // Create tables if they don't exist
    if (!createTables()) {
        qCritical() << "Failed to create database tables";
        return false;
    }
    
    // Create indexes for performance
    if (!createIndexes()) {
        qWarning() << "Failed to create some database indexes";
        // Continue anyway as indexes are for performance
    }
    
    // Migrate schema if needed
    // NOTE: Schema migration planned for v1.1
    // if (!migrateSchema()) {
    //     qCritical() << "Failed to migrate database schema";
    //     return false;
    // }
    
    m_isInitialized = true;
    qDebug() << "DatabaseManager initialized successfully";
    qDebug() << "Database path:" << m_databasePath;
    
    return true;
}

bool DatabaseManager::createDatabaseFile()
{
    // Get application data directory
    QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    
    // Create directory if it doesn't exist
    QDir dir;
    if (!dir.mkpath(dataDir)) {
        qCritical() << "Failed to create data directory:" << dataDir;
        return false;
    }
    
    // Set database file path
    m_databasePath = QDir(dataDir).filePath("competition_data.sqlite");
    
    qDebug() << "Database file path:" << m_databasePath;
    return true;
}

bool DatabaseManager::connectToDatabase()
{
    // Add SQLite database
    m_database = QSqlDatabase::addDatabase("QSQLITE", DATABASE_CONNECTION_NAME);
    m_database.setDatabaseName(m_databasePath);
    
    // Open database connection
    if (!m_database.open()) {
        m_lastError = QString("Failed to open database: %1").arg(m_database.lastError().text());
        qCritical() << m_lastError;
        return false;
    }
    
    // Enable foreign key constraints
    QSqlQuery query(m_database);
    if (!query.exec("PRAGMA foreign_keys = ON")) {
        logDatabaseError("Enable foreign keys", query.lastError());
        return false;
    }
    
    // Set encoding to UTF-8
    if (!query.exec("PRAGMA encoding = 'UTF-8'")) {
        logDatabaseError("Set UTF-8 encoding", query.lastError());
        return false;
    }
    
    return true;
}

void DatabaseManager::setupErrorHandling()
{
    // Connect database error signals
    if (m_database.driver()) {
        connect(m_database.driver(), &QSqlDriver::notification,
                this, &DatabaseManager::onDatabaseError);
    }
}

bool DatabaseManager::createTables()
{
    bool success = true;
    
    success &= createSchemaVersionTable();
    success &= createCompetitionsTable();
    success &= createAthletesTable();
    success &= createHeightSettingsTable();
    success &= createAttemptRecordsTable();
    success &= createSyncQueueTable();
    
    return success;
}

bool DatabaseManager::createSchemaVersionTable()
{
    QSqlQuery query(m_database);
    
    QString sql = R"(
        CREATE TABLE IF NOT EXISTS schema_version (
            version INTEGER PRIMARY KEY,
            applied_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            description TEXT
        )
    )";
    
    if (!query.exec(sql)) {
        logDatabaseError("Create schema_version table", query.lastError());
        return false;
    }
    
    // Insert initial version if table is empty
    if (!query.exec("SELECT COUNT(*) FROM schema_version")) {
        logDatabaseError("Check schema_version count", query.lastError());
        return false;
    }
    
    query.next();
    if (query.value(0).toInt() == 0) {
        QString insertSql = "INSERT INTO schema_version (version, description) VALUES (?, ?)";
        query.prepare(insertSql);
        query.addBindValue(CURRENT_SCHEMA_VERSION);
        query.addBindValue("Initial schema with basic competition management");
        
        if (!query.exec()) {
            logDatabaseError("Insert initial schema version", query.lastError());
            return false;
        }
    }
    
    return true;
}

bool DatabaseManager::createCompetitionsTable()
{
    QSqlQuery query(m_database);
    
    QString sql = R"(
        CREATE TABLE IF NOT EXISTS competitions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            date TEXT NOT NULL,
            venue TEXT,
            starting_height INTEGER NOT NULL,
            height_increment INTEGER NOT NULL DEFAULT 3,
            status TEXT NOT NULL DEFAULT 'not_started',
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            
            CHECK (starting_height > 0),
            CHECK (height_increment > 0),
            CHECK (status IN ('not_started', 'in_progress', 'paused', 'finished'))
        )
    )";
    
    if (!query.exec(sql)) {
        logDatabaseError("Create competitions table", query.lastError());
        return false;
    }
    
    return true;
}

bool DatabaseManager::createAthletesTable()
{
    QSqlQuery query(m_database);
    
    QString sql = R"(
        CREATE TABLE IF NOT EXISTS athletes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            competition_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            number INTEGER NOT NULL,
            team TEXT,
            personal_best INTEGER,
            season_best INTEGER,
            status TEXT NOT NULL DEFAULT 'active',
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (competition_id) REFERENCES competitions(id) ON DELETE CASCADE,
            UNIQUE (competition_id, number),
            CHECK (number > 0),
            CHECK (personal_best IS NULL OR personal_best > 0),
            CHECK (season_best IS NULL OR season_best > 0),
            CHECK (status IN ('active', 'eliminated', 'retired', 'finished'))
        )
    )";
    
    if (!query.exec(sql)) {
        logDatabaseError("Create athletes table", query.lastError());
        return false;
    }
    
    return true;
}

bool DatabaseManager::createHeightSettingsTable()
{
    QSqlQuery query(m_database);
    
    QString sql = R"(
        CREATE TABLE IF NOT EXISTS height_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            competition_id INTEGER NOT NULL,
            height INTEGER NOT NULL,
            sequence_order INTEGER NOT NULL,
            is_current BOOLEAN NOT NULL DEFAULT 0,
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (competition_id) REFERENCES competitions(id) ON DELETE CASCADE,
            UNIQUE (competition_id, height),
            UNIQUE (competition_id, sequence_order),
            CHECK (height > 0),
            CHECK (sequence_order >= 0)
        )
    )";
    
    if (!query.exec(sql)) {
        logDatabaseError("Create height_settings table", query.lastError());
        return false;
    }
    
    return true;
}

bool DatabaseManager::createAttemptRecordsTable()
{
    QSqlQuery query(m_database);
    
    QString sql = R"(
        CREATE TABLE IF NOT EXISTS attempt_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            athlete_id INTEGER NOT NULL,
            height INTEGER NOT NULL,
            attempt_number INTEGER NOT NULL,
            result TEXT NOT NULL,
            timestamp TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (athlete_id) REFERENCES athletes(id) ON DELETE CASCADE,
            UNIQUE (athlete_id, height, attempt_number),
            CHECK (attempt_number BETWEEN 1 AND 3),
            CHECK (height > 0),
            CHECK (result IN ('success', 'failure', 'pass', 'retire'))
        )
    )";
    
    if (!query.exec(sql)) {
        logDatabaseError("Create attempt_records table", query.lastError());
        return false;
    }
    
    return true;
}

bool DatabaseManager::createSyncQueueTable()
{
    QSqlQuery query(m_database);
    
    QString sql = R"(
        CREATE TABLE IF NOT EXISTS sync_queue (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            operation_type TEXT NOT NULL,
            data_json TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'pending',
            retry_count INTEGER NOT NULL DEFAULT 0,
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            last_attempt_at TEXT,
            completed_at TEXT,
            error_message TEXT,
            
            CHECK (operation_type IN ('create_attempt', 'update_athlete', 'update_competition')),
            CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
            CHECK (retry_count >= 0)
        )
    )";
    
    if (!query.exec(sql)) {
        logDatabaseError("Create sync_queue table", query.lastError());
        return false;
    }
    
    return true;
}

bool DatabaseManager::createIndexes()
{
    QSqlQuery query(m_database);
    bool success = true;
    
    // Index definitions
    QStringList indexes = {
        "CREATE INDEX IF NOT EXISTS idx_competitions_date ON competitions(date)",
        "CREATE INDEX IF NOT EXISTS idx_competitions_status ON competitions(status)",
        "CREATE INDEX IF NOT EXISTS idx_athletes_competition ON athletes(competition_id)",
        "CREATE INDEX IF NOT EXISTS idx_athletes_number ON athletes(competition_id, number)",
        "CREATE INDEX IF NOT EXISTS idx_athletes_status ON athletes(status)",
        "CREATE INDEX IF NOT EXISTS idx_height_settings_competition ON height_settings(competition_id)",
        "CREATE INDEX IF NOT EXISTS idx_height_settings_order ON height_settings(competition_id, sequence_order)",
        "CREATE INDEX IF NOT EXISTS idx_height_settings_current ON height_settings(is_current)",
        "CREATE INDEX IF NOT EXISTS idx_attempt_records_athlete ON attempt_records(athlete_id)",
        "CREATE INDEX IF NOT EXISTS idx_attempt_records_height ON attempt_records(height)",
        "CREATE INDEX IF NOT EXISTS idx_attempt_records_timestamp ON attempt_records(timestamp)",
        "CREATE INDEX IF NOT EXISTS idx_attempt_records_result ON attempt_records(result)",
        "CREATE INDEX IF NOT EXISTS idx_sync_queue_status ON sync_queue(status)",
        "CREATE INDEX IF NOT EXISTS idx_sync_queue_created ON sync_queue(created_at)",
        "CREATE INDEX IF NOT EXISTS idx_sync_queue_operation ON sync_queue(operation_type)"
    };
    
    for (const QString &indexSql : indexes) {
        if (!query.exec(indexSql)) {
            logDatabaseError("Create index", query.lastError());
            success = false;
        }
    }
    
    return success;
}

bool DatabaseManager::isConnected() const
{
    return m_database.isOpen() && m_database.isValid();
}

QString DatabaseManager::getDatabasePath() const
{
    return m_databasePath;
}

void DatabaseManager::close()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_database.isOpen()) {
        m_database.close();
    }
    
    QSqlDatabase::removeDatabase(DATABASE_CONNECTION_NAME);
    m_isInitialized = false;
}

int DatabaseManager::getCurrentSchemaVersion()
{
    QSqlQuery query(m_database);
    
    if (!query.exec("SELECT MAX(version) FROM schema_version")) {
        logDatabaseError("Get current schema version", query.lastError());
        return -1;
    }
    
    if (query.next()) {
        return query.value(0).toInt();
    }
    
    return -1;
}

bool DatabaseManager::migrateSchema()
{
    int currentVersion = getCurrentSchemaVersion();
    
    if (currentVersion == -1) {
        qCritical() << "Failed to get current schema version";
        return false;
    }
    
    if (currentVersion == CURRENT_SCHEMA_VERSION) {
        qDebug() << "Database schema is up to date (version" << currentVersion << ")";
        return true;
    }
    
    if (currentVersion > CURRENT_SCHEMA_VERSION) {
        qWarning() << "Database schema version" << currentVersion 
                   << "is newer than expected" << CURRENT_SCHEMA_VERSION;
        return true; // Allow newer versions
    }
    
    // Future migration logic would go here
    qDebug() << "Schema migration not needed";
    return true;
}

bool DatabaseManager::beginTransaction()
{
    if (!isConnected()) {
        m_lastError = "Database not connected";
        return false;
    }
    
    if (!m_database.transaction()) {
        m_lastError = QString("Failed to begin transaction: %1")
                     .arg(m_database.lastError().text());
        return false;
    }
    
    return true;
}

bool DatabaseManager::commitTransaction()
{
    if (!m_database.commit()) {
        m_lastError = QString("Failed to commit transaction: %1")
                     .arg(m_database.lastError().text());
        return false;
    }
    
    return true;
}

bool DatabaseManager::rollbackTransaction()
{
    if (!m_database.rollback()) {
        m_lastError = QString("Failed to rollback transaction: %1")
                     .arg(m_database.lastError().text());
        return false;
    }
    
    return true;
}

QString DatabaseManager::lastError() const
{
    return m_lastError;
}

void DatabaseManager::logDatabaseError(const QString &operation, const QSqlError &error)
{
    QString errorMsg = QString("%1 failed: %2").arg(operation, error.text());
    m_lastError = errorMsg;
    qCritical() << errorMsg;
    
    emit databaseError(errorMsg);
}

void DatabaseManager::onDatabaseError(const QString &error)
{
    qWarning() << "Database notification:" << error;
}

bool DatabaseManager::tableExists(const QString &tableName)
{
    if (!m_database.isOpen()) {
        return false;
    }

    QStringList tables = m_database.tables();
    return tables.contains(tableName);
}

// Attempt record operations
bool DatabaseManager::recordAttempt(int athleteId, int height, int attemptNumber, const QString &result)
{
    QMutexLocker locker(&m_mutex);

    if (!m_database.isOpen()) {
        m_lastError = "Database not connected";
        return false;
    }

    if (!beginTransaction()) {
        return false;
    }

    QSqlQuery query(m_database);
    query.prepare(R"(
        INSERT INTO attempt_records (athlete_id, height, attempt_number, result, timestamp, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
    )");

    QString timestamp = QDateTime::currentDateTime().toString(Qt::ISODate);
    query.addBindValue(athleteId);
    query.addBindValue(height);
    query.addBindValue(attemptNumber);
    query.addBindValue(result);
    query.addBindValue(timestamp);
    query.addBindValue(timestamp);

    if (!query.exec()) {
        logDatabaseError("Record attempt", query.lastError());
        rollbackTransaction();
        return false;
    }

    if (!commitTransaction()) {
        return false;
    }

    qDebug() << "DatabaseManager::recordAttempt: Recorded attempt for athlete" << athleteId
             << "height" << height << "attempt" << attemptNumber << "result" << result;

    return true;
}

// Sync queue operations
bool DatabaseManager::insertSyncQueueEntry(const QString &operationType, const QString &dataJson,
                                          const QString &status, int retryCount, const QString &createdAt)
{
    QMutexLocker locker(&m_mutex);

    if (!m_database.isOpen()) {
        m_lastError = "Database not connected";
        return false;
    }

    QSqlQuery query(m_database);
    query.prepare(R"(
        INSERT INTO sync_queue (operation_type, data_json, status, retry_count, created_at)
        VALUES (?, ?, ?, ?, ?)
    )");

    query.addBindValue(operationType);
    query.addBindValue(dataJson);
    query.addBindValue(status);
    query.addBindValue(retryCount);
    query.addBindValue(createdAt);

    if (!query.exec()) {
        logDatabaseError("Insert sync queue entry", query.lastError());
        return false;
    }

    return true;
}

bool DatabaseManager::updateSyncQueueEntry(int id, const QString &status, const QString &lastAttemptAt,
                                          const QString &completedAt, const QString &errorMessage)
{
    QMutexLocker locker(&m_mutex);

    if (!m_database.isOpen()) {
        m_lastError = "Database not connected";
        return false;
    }

    QSqlQuery query(m_database);
    QString sql = "UPDATE sync_queue SET status = ?, last_attempt_at = ?";
    QVariantList params;
    params << status << lastAttemptAt;

    if (!completedAt.isEmpty()) {
        sql += ", completed_at = ?";
        params << completedAt;
    }

    if (!errorMessage.isEmpty()) {
        sql += ", error_message = ?, retry_count = retry_count + 1";
        params << errorMessage;
    }

    sql += " WHERE id = ?";
    params << id;

    query.prepare(sql);
    for (const QVariant &param : params) {
        query.addBindValue(param);
    }

    if (!query.exec()) {
        logDatabaseError("Update sync queue entry", query.lastError());
        return false;
    }

    return true;
}

QSqlQuery DatabaseManager::executeSyncQueueQuery(const QString &sql, const QVariantList &params)
{
    QMutexLocker locker(&m_mutex);

    QSqlQuery query(m_database);
    query.prepare(sql);

    for (const QVariant &param : params) {
        query.addBindValue(param);
    }

    if (!query.exec()) {
        logDatabaseError("Execute sync queue query", query.lastError());
    }

    return query;
}