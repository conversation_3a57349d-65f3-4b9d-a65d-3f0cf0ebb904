// This file is autogenerated. Changes will be overwritten.
#include <PA7CP7W2FZ/moc_api_client.cpp>
#include <PRMOGMWJPH/moc_jump_manager.cpp>
#include <M4YTXQ7V2H/moc_athlete.cpp>
#include <M4YTXQ7V2H/moc_athlete_table_model.cpp>
#include <M4YTXQ7V2H/moc_competition.cpp>
#include <M4YTXQ7V2H/moc_competition_state.cpp>
#include <M4YTXQ7V2H/moc_jump_attempt.cpp>
#include <M4YTXQ7V2H/moc_ranking_calculator.cpp>
#include <43FNQ76O6F/moc_database_manager.cpp>
#include <YPKJ5OE7LN/moc_athlete_delegate.cpp>
#include <YPKJ5OE7LN/moc_athlete_dialog.cpp>
#include <YPKJ5OE7LN/moc_competition_selection_view.cpp>
#include <YPKJ5OE7LN/moc_main_window.cpp>
#include <YPKJ5OE7LN/moc_report_dialog.cpp>
#include <YPKJ5OE7LN/moc_scoring_view.cpp>
#include <YPKJ5OE7LN/moc_shortcut_manager.cpp>
#include <YPKJ5OE7LN/moc_theme_manager.cpp>
#include <YPKJ5OE7LN/moc_view_manager.cpp>
#include <VSCBVMNR7M/moc_config_manager.cpp>
#include <VSCBVMNR7M/moc_performance_monitor.cpp>
#include <VSCBVMNR7M/moc_report_generator.cpp>
