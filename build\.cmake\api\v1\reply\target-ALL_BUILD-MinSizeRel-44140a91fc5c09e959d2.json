{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "test_athlete_delegate::@a44f0ac069e85531cdee"}, {"id": "test_application_startup::@a44f0ac069e85531cdee"}, {"id": "test_api_client::@a44f0ac069e85531cdee"}, {"id": "test_competition_selection_view::@a44f0ac069e85531cdee"}, {"id": "test_config_manager::@a44f0ac069e85531cdee"}, {"id": "test_scoring_view::@a44f0ac069e85531cdee"}, {"id": "high-jump-scorer::@6890427a1f51a3e7e1df"}, {"id": "test_e2e_simple::@a44f0ac069e85531cdee"}, {"id": "test_database_manager::@a44f0ac069e85531cdee"}, {"id": "test_athlete_table_model::@a44f0ac069e85531cdee"}, {"id": "test_shortcut_manager::@a44f0ac069e85531cdee"}, {"id": "test_large_scale_performance::@a44f0ac069e85531cdee"}, {"id": "test_ui_ux_usability::@a44f0ac069e85531cdee"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "folder": {"name": "CMakePredefinedTargets"}, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "isGeneratorProvided": true, "name": "ALL_BUILD", "paths": {"build": ".", "source": "."}, "sources": [], "type": "UTILITY"}