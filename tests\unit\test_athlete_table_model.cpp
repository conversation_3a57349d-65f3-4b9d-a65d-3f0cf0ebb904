#include <QtTest/QtTest>
#include <QSignalSpy>
#include <QStandardPaths>
#include <QDir>

#include "models/athlete_table_model.h"
#include "models/athlete.h"
#include "models/competition.h"
#include "models/jump_attempt.h"
#include "persistence/database_manager.h"

/**
 * @brief Unit tests for AthleteTableModel
 * 
 * Tests the core functionality of the athlete table model including:
 * - Data model interface implementation
 * - Dynamic column management for heights
 * - Attempt recording and retrieval
 * - Ranking calculation
 * - Data update signals
 */
class TestAthleteTableModel : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void init();
    void cleanup();
    void cleanupTestCase();
    
    // Basic model interface tests
    void testModelInterface();
    void testEmptyModel();
    void testColumnCount();
    void testRowCount();
    
    // Data retrieval tests
    void testDataRetrieval();
    void testHeaderData();
    void testCustomDataRoles();
    
    // Height management tests
    void testHeightProgression();
    void testDynamicColumnManagement();
    void testHeightColumnMapping();
    
    // Attempt recording tests
    void testAttemptRecording();
    void testAttemptRetrieval();
    void testInvalidAttempts();
    
    // Ranking calculation tests
    void testBasicRanking();
    void testRankingWithTies();
    void testRankingWithFailures();
    void testRankingUpdate();
    
    // Signal emission tests
    void testDataUpdateSignals();
    void testRankingSignals();
    void testAttemptSignals();

private:
    AthleteTableModel *m_model;
    Competition *m_competition;
    QList<Athlete*> m_athletes;
    QString m_testDbPath;
    
    void createTestAthletes();
    void createTestCompetition();
    Athlete* createAthlete(int id, const QString &firstName, const QString &lastName, int number);
};

void TestAthleteTableModel::initTestCase()
{
    // Setup test database
    QString tempDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
    m_testDbPath = QDir(tempDir).filePath("test_athlete_table_model.db");
    
    // Remove existing test database
    if (QFile::exists(m_testDbPath)) {
        QFile::remove(m_testDbPath);
    }
    
    // Initialize database manager with test database
    DatabaseManager::instance()->initialize(m_testDbPath);
}

void TestAthleteTableModel::init()
{
    // Create fresh model for each test
    m_model = new AthleteTableModel(this);
    
    // Create test data
    createTestAthletes();
    createTestCompetition();
}

void TestAthleteTableModel::cleanup()
{
    // Clean up test data
    delete m_model;
    m_model = nullptr;
    
    delete m_competition;
    m_competition = nullptr;
    
    qDeleteAll(m_athletes);
    m_athletes.clear();
}

void TestAthleteTableModel::cleanupTestCase()
{
    // Clean up test database
    if (QFile::exists(m_testDbPath)) {
        QFile::remove(m_testDbPath);
    }
}

void TestAthleteTableModel::testModelInterface()
{
    // Test basic QAbstractTableModel interface
    QVERIFY(m_model != nullptr);
    
    // Test with empty model
    QCOMPARE(m_model->rowCount(), 0);
    QCOMPARE(m_model->columnCount(), 6); // Fixed columns only
    
    // Load competition
    m_model->loadCompetition(m_competition);
    
    // Test with loaded data
    QCOMPARE(m_model->rowCount(), m_athletes.size());
    QVERIFY(m_model->columnCount() > 6); // Fixed + height columns
}

void TestAthleteTableModel::testEmptyModel()
{
    // Test behavior with empty model
    QCOMPARE(m_model->rowCount(), 0);
    QCOMPARE(m_model->columnCount(), 6);
    
    // Test invalid index access
    QModelIndex invalidIndex = m_model->index(0, 0);
    QVERIFY(!invalidIndex.isValid());
    
    QVariant data = m_model->data(invalidIndex);
    QVERIFY(!data.isValid());
}

void TestAthleteTableModel::testColumnCount()
{
    // Test column count with different height progressions
    QList<int> heights = {150, 153, 156, 159, 162};
    m_model->setHeightProgression(heights);
    
    int expectedColumns = 6 + heights.size(); // Fixed + height columns
    QCOMPARE(m_model->columnCount(), expectedColumns);
    
    // Add another height
    m_model->addHeight(165);
    QCOMPARE(m_model->columnCount(), expectedColumns + 1);
}

void TestAthleteTableModel::testRowCount()
{
    // Test row count with competition
    m_model->loadCompetition(m_competition);
    QCOMPARE(m_model->rowCount(), m_athletes.size());
    
    // Test with empty competition
    Competition emptyCompetition;
    m_model->loadCompetition(&emptyCompetition);
    QCOMPARE(m_model->rowCount(), 0);
}

void TestAthleteTableModel::testDataRetrieval()
{
    m_model->loadCompetition(m_competition);
    
    // Test athlete data retrieval
    for (int row = 0; row < m_model->rowCount(); ++row) {
        // Test rank column
        QModelIndex rankIndex = m_model->index(row, AthleteTableModel::RankColumn);
        QVariant rankData = m_model->data(rankIndex);
        QVERIFY(rankData.isValid());
        QVERIFY(rankData.toInt() >= 0);
        
        // Test name column
        QModelIndex nameIndex = m_model->index(row, AthleteTableModel::NameColumn);
        QVariant nameData = m_model->data(nameIndex);
        QVERIFY(nameData.isValid());
        QVERIFY(!nameData.toString().isEmpty());
        
        // Test number column
        QModelIndex numberIndex = m_model->index(row, AthleteTableModel::NumberColumn);
        QVariant numberData = m_model->data(numberIndex);
        QVERIFY(numberData.isValid());
        QVERIFY(numberData.toInt() > 0);
    }
}

void TestAthleteTableModel::testHeaderData()
{
    m_model->loadCompetition(m_competition);
    
    // Test fixed column headers
    QCOMPARE(m_model->headerData(AthleteTableModel::RankColumn, Qt::Horizontal).toString(), tr("排名"));
    QCOMPARE(m_model->headerData(AthleteTableModel::NumberColumn, Qt::Horizontal).toString(), tr("号码"));
    QCOMPARE(m_model->headerData(AthleteTableModel::NameColumn, Qt::Horizontal).toString(), tr("姓名"));
    
    // Test height column headers
    QList<int> heights = m_competition->getHeightProgression();
    for (int i = 0; i < heights.size(); ++i) {
        int column = AthleteTableModel::FirstHeightColumn + i;
        QString expectedHeader = QString("%1cm").arg(heights[i]);
        QCOMPARE(m_model->headerData(column, Qt::Horizontal).toString(), expectedHeader);
    }
}

void TestAthleteTableModel::testCustomDataRoles()
{
    m_model->loadCompetition(m_competition);
    
    if (m_model->rowCount() > 0) {
        QModelIndex index = m_model->index(0, AthleteTableModel::NameColumn);
        
        // Test athlete ID role
        QVariant athleteIdData = m_model->data(index, AthleteTableModel::AthleteIdRole);
        QVERIFY(athleteIdData.isValid());
        QVERIFY(athleteIdData.toInt() > 0);
        
        // Test ranking role
        QVariant rankingData = m_model->data(index, AthleteTableModel::RankingRole);
        QVERIFY(rankingData.isValid());
        QVERIFY(rankingData.toInt() >= 0);
    }
}

void TestAthleteTableModel::testHeightProgression()
{
    QList<int> heights = {150, 153, 156, 159, 162};
    m_model->setHeightProgression(heights);
    
    QCOMPARE(m_model->getHeightProgression(), heights);
    
    // Test adding height
    m_model->addHeight(165);
    QList<int> expectedHeights = heights;
    expectedHeights.append(165);
    std::sort(expectedHeights.begin(), expectedHeights.end());
    
    QCOMPARE(m_model->getHeightProgression(), expectedHeights);
}

void TestAthleteTableModel::testDynamicColumnManagement()
{
    // Test column count changes with height progression
    int initialColumns = m_model->columnCount();
    
    QList<int> heights = {150, 153, 156};
    m_model->setHeightProgression(heights);
    
    QCOMPARE(m_model->columnCount(), initialColumns + heights.size());
}

void TestAthleteTableModel::testHeightColumnMapping()
{
    QList<int> heights = {150, 153, 156, 159};
    m_model->setHeightProgression(heights);
    
    // Test height to column mapping
    for (int i = 0; i < heights.size(); ++i) {
        int height = heights[i];
        int expectedColumn = AthleteTableModel::FirstHeightColumn + i;
        
        QCOMPARE(m_model->getColumnForHeight(height), expectedColumn);
        QCOMPARE(m_model->getHeightForColumn(expectedColumn), height);
    }
    
    // Test invalid mappings
    QCOMPARE(m_model->getColumnForHeight(999), -1);
    QCOMPARE(m_model->getHeightForColumn(999), -1);
}

void TestAthleteTableModel::createTestAthletes()
{
    m_athletes.clear();
    m_athletes.append(createAthlete(1, "张", "三", 1));
    m_athletes.append(createAthlete(2, "李", "四", 2));
    m_athletes.append(createAthlete(3, "王", "五", 3));
}

void TestAthleteTableModel::createTestCompetition()
{
    m_competition = new Competition(this);
    m_competition->setId(1);
    m_competition->setName("测试比赛");
    m_competition->setVenue("测试场地");
    
    // Set height progression
    QList<int> heights = {150, 153, 156, 159, 162};
    m_competition->setHeightProgression(heights);
    
    // Add athletes to competition
    for (Athlete *athlete : m_athletes) {
        m_competition->addAthlete(athlete);
    }
}

Athlete* TestAthleteTableModel::createAthlete(int id, const QString &firstName, const QString &lastName, int number)
{
    Athlete *athlete = new Athlete(this);
    athlete->setId(id);
    athlete->setFirstName(firstName);
    athlete->setLastName(lastName);
    athlete->setStartNumber(number);
    athlete->setClub("测试队伍");
    return athlete;
}

QTEST_MAIN(TestAthleteTableModel)
#include "test_athlete_table_model.moc"
