#ifndef COMPETITION_DTO_H
#define COMPETITION_DTO_H

#include <QString>
#include <QJsonObject>
#include <QJsonArray>
#include <QDateTime>

class Competition;

/**
 * @brief Data Transfer Object for Competition API communication
 * 
 * This class provides a structured representation of competition data
 * for API communication. It handles serialization/deserialization
 * between JSON and internal Competition objects.
 */
struct CompetitionDto
{
    // Basic competition information
    int id;                     ///< Competition unique identifier
    QString name;               ///< Competition name
    QString date;               ///< Competition date in ISO format
    QString venue;              ///< Competition venue/location
    QString status;             ///< Competition status (not_started, in_progress, paused, finished)
    int startingHeight;         ///< Starting height in cm
    int heightIncrement;        ///< Height increment between levels in cm
    
    // Additional optional fields
    QJsonArray athletes;        ///< Array of participating athletes
    QJsonArray heightSequence; ///< Sequence of competition heights
    
    /**
     * @brief Default constructor
     */
    CompetitionDto();
    
    /**
     * @brief Constructs CompetitionDto from JSON object
     * @param json JSON object containing competition data
     */
    CompetitionDto(const QJsonObject &json);
    
    /**
     * @brief Creates CompetitionDto from JSON object
     * @param json JSON object from API response
     * @return CompetitionDto instance
     */
    static CompetitionDto fromJson(const QJsonObject &json);
    
    /**
     * @brief Converts CompetitionDto to JSON object
     * @return QJsonObject for API requests
     */
    QJsonObject toJson() const;
    
    /**
     * @brief Converts CompetitionDto to internal Competition model
     * @return Pointer to Competition object
     * 
     * @note Caller is responsible for memory management
     */
    Competition* toModel() const;
    
    /**
     * @brief Creates CompetitionDto from internal Competition model
     * @param competition Competition model to convert
     * @return CompetitionDto instance
     */
    static CompetitionDto fromModel(const Competition &competition);
    
    /**
     * @brief Validates the competition data
     * @return true if all required fields are valid
     */
    bool isValid() const;
    
    /**
     * @brief Gets formatted date for display
     * @return Formatted date string
     */
    QString getFormattedDate() const;
    
    /**
     * @brief Gets localized status string
     * @return Translated status string
     */
    QString getLocalizedStatus() const;
    
private:
    void populateFromJson(const QJsonObject &json);
};

/**
 * @brief List of competition DTOs with utility methods
 */
class CompetitionDtoList
{
public:
    /**
     * @brief Creates list from JSON array
     * @param jsonArray JSON array of competitions
     * @return CompetitionDtoList instance
     */
    static QList<CompetitionDto> fromJsonArray(const QJsonArray &jsonArray);
    
    /**
     * @brief Converts list to JSON array
     * @param competitions List of competition DTOs
     * @return QJsonArray for API communication
     */
    static QJsonArray toJsonArray(const QList<CompetitionDto> &competitions);
    
    /**
     * @brief Filters competitions by status
     * @param competitions List of competitions to filter
     * @param status Status to filter by
     * @return Filtered list of competitions
     */
    static QList<CompetitionDto> filterByStatus(const QList<CompetitionDto> &competitions, 
                                               const QString &status);
    
    /**
     * @brief Sorts competitions by date
     * @param competitions List of competitions to sort
     * @param ascending Sort order (true for ascending, false for descending)
     * @return Sorted list of competitions
     */
    static QList<CompetitionDto> sortByDate(const QList<CompetitionDto> &competitions, 
                                           bool ascending = true);
};

#endif // COMPETITION_DTO_H