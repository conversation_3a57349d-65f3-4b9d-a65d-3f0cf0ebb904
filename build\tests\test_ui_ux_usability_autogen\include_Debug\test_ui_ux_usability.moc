/****************************************************************************
** Meta object code from reading C++ file 'test_ui_ux_usability.cpp'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'test_ui_ux_usability.cpp' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN17TestUIUXUsabilityE_t {};
} // unnamed namespace

template <> constexpr inline auto TestUIUXUsability::qt_create_metaobjectdata<qt_meta_tag_ZN17TestUIUXUsabilityE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "TestUIUXUsability",
        "initTestCase",
        "",
        "init",
        "cleanup",
        "cleanupTestCase",
        "testLayoutResponsiveness",
        "testComponentVisibility",
        "testProportionalLayout",
        "testMouseInteraction",
        "testKeyboardNavigation",
        "testContextMenuUsability",
        "testShortcutAccessibility",
        "testVisualFeedback",
        "testColorCoding",
        "testFocusIndicators",
        "testStatusIndicators",
        "testDataReadability",
        "testErrorHandling",
        "testUserGuidance",
        "testPerformanceFeedback",
        "testAccessibilitySupport",
        "testKeyboardOnlyNavigation",
        "testScreenReaderSupport"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'initTestCase'
        QtMocHelpers::SlotData<void()>(1, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'init'
        QtMocHelpers::SlotData<void()>(3, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'cleanup'
        QtMocHelpers::SlotData<void()>(4, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'cleanupTestCase'
        QtMocHelpers::SlotData<void()>(5, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testLayoutResponsiveness'
        QtMocHelpers::SlotData<void()>(6, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testComponentVisibility'
        QtMocHelpers::SlotData<void()>(7, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testProportionalLayout'
        QtMocHelpers::SlotData<void()>(8, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testMouseInteraction'
        QtMocHelpers::SlotData<void()>(9, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testKeyboardNavigation'
        QtMocHelpers::SlotData<void()>(10, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testContextMenuUsability'
        QtMocHelpers::SlotData<void()>(11, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testShortcutAccessibility'
        QtMocHelpers::SlotData<void()>(12, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testVisualFeedback'
        QtMocHelpers::SlotData<void()>(13, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testColorCoding'
        QtMocHelpers::SlotData<void()>(14, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testFocusIndicators'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testStatusIndicators'
        QtMocHelpers::SlotData<void()>(16, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testDataReadability'
        QtMocHelpers::SlotData<void()>(17, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testErrorHandling'
        QtMocHelpers::SlotData<void()>(18, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testUserGuidance'
        QtMocHelpers::SlotData<void()>(19, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testPerformanceFeedback'
        QtMocHelpers::SlotData<void()>(20, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testAccessibilitySupport'
        QtMocHelpers::SlotData<void()>(21, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testKeyboardOnlyNavigation'
        QtMocHelpers::SlotData<void()>(22, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testScreenReaderSupport'
        QtMocHelpers::SlotData<void()>(23, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<TestUIUXUsability, qt_meta_tag_ZN17TestUIUXUsabilityE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject TestUIUXUsability::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17TestUIUXUsabilityE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17TestUIUXUsabilityE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN17TestUIUXUsabilityE_t>.metaTypes,
    nullptr
} };

void TestUIUXUsability::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<TestUIUXUsability *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->initTestCase(); break;
        case 1: _t->init(); break;
        case 2: _t->cleanup(); break;
        case 3: _t->cleanupTestCase(); break;
        case 4: _t->testLayoutResponsiveness(); break;
        case 5: _t->testComponentVisibility(); break;
        case 6: _t->testProportionalLayout(); break;
        case 7: _t->testMouseInteraction(); break;
        case 8: _t->testKeyboardNavigation(); break;
        case 9: _t->testContextMenuUsability(); break;
        case 10: _t->testShortcutAccessibility(); break;
        case 11: _t->testVisualFeedback(); break;
        case 12: _t->testColorCoding(); break;
        case 13: _t->testFocusIndicators(); break;
        case 14: _t->testStatusIndicators(); break;
        case 15: _t->testDataReadability(); break;
        case 16: _t->testErrorHandling(); break;
        case 17: _t->testUserGuidance(); break;
        case 18: _t->testPerformanceFeedback(); break;
        case 19: _t->testAccessibilitySupport(); break;
        case 20: _t->testKeyboardOnlyNavigation(); break;
        case 21: _t->testScreenReaderSupport(); break;
        default: ;
        }
    }
    (void)_a;
}

const QMetaObject *TestUIUXUsability::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TestUIUXUsability::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17TestUIUXUsabilityE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int TestUIUXUsability::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 22)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 22;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 22)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 22;
    }
    return _id;
}
QT_WARNING_POP
