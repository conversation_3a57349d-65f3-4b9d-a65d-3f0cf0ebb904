#ifndef VIEW_MANAGER_H
#define VIEW_MANAGER_H

#include <QObject>
#include <QStackedWidget>
#include <QHash>
#include <QWidget>

/**
 * @brief 视图管理器
 * 
 * 负责管理主窗口内不同视图的切换，使用QStackedWidget实现
 * 视图间的平滑过渡和状态管理。
 */
class ViewManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 视图类型枚举
     */
    enum ViewType {
        CompetitionSelection,    ///< 赛事选择界面
        MainScoring,            ///< 主计分界面
        ResultsView             ///< 结果查看界面
    };

    /**
     * @brief 构造函数
     * @param container QStackedWidget容器用于视图切换
     * @param parent 父对象
     */
    explicit ViewManager(QStackedWidget *container, QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~ViewManager();

    /**
     * @brief 切换到指定视图
     * @param viewType 要切换到的视图类型
     * @return 切换成功返回true，否则返回false
     */
    bool switchToView(ViewType viewType);

    /**
     * @brief 注册视图组件
     * @param viewType 视图类型
     * @param widget 视图组件指针
     * @return 注册成功返回true，否则返回false
     */
    bool registerView(ViewType viewType, QWidget *widget);

    /**
     * @brief 获取当前活动视图类型
     * @return 当前视图类型
     */
    ViewType currentViewType() const;

    /**
     * @brief 获取指定类型的视图组件
     * @param viewType 视图类型
     * @return 视图组件指针，如果不存在则返回nullptr
     */
    QWidget* getView(ViewType viewType) const;

    /**
     * @brief 检查视图是否已注册
     * @param viewType 视图类型
     * @return 已注册返回true，否则返回false
     */
    bool isViewRegistered(ViewType viewType) const;

signals:
    /**
     * @brief 视图切换前信号
     * @param fromType 当前视图类型
     * @param toType 目标视图类型
     */
    void viewChangeRequested(ViewType fromType, ViewType toType);

    /**
     * @brief 视图切换完成信号
     * @param viewType 新的当前视图类型
     */
    void viewChanged(ViewType viewType);

    /**
     * @brief 视图切换失败信号
     * @param viewType 尝试切换到的视图类型
     * @param errorMessage 错误信息
     */
    void viewChangeFailed(ViewType viewType, const QString &errorMessage);

private slots:
    void onViewWidgetDestroyed(QObject *obj);

private:
    void connectViewSignals(QWidget *widget);
    void disconnectViewSignals(QWidget *widget);
    QString viewTypeToString(ViewType viewType) const;
    
    // 成员变量
    QStackedWidget *m_container;
    QHash<ViewType, QWidget*> m_views;
    ViewType m_currentViewType;
    bool m_isTransitioning;
};

Q_DECLARE_METATYPE(ViewManager::ViewType)

#endif // VIEW_MANAGER_H