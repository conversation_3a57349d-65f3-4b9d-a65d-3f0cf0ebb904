#include "sync_queue_manager.h"
#include "database_manager.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QJsonDocument>
#include <QDebug>
#include <QMutexLocker>

SyncQueueManager* SyncQueueManager::s_instance = nullptr;

SyncQueueManager* SyncQueueManager::instance()
{
    if (!s_instance) {
        s_instance = new SyncQueueManager();
    }
    return s_instance;
}

SyncQueueManager::SyncQueueManager(QObject *parent)
    : QObject(parent)
    , m_dbManager(DatabaseManager::instance())
{
    // Connect to database error signals
    connect(m_dbManager, &DatabaseManager::databaseError,
            this, &SyncQueueManager::onDatabaseError);
    
    qDebug() << "SyncQueueManager: Initialized";
}

SyncQueueManager::~SyncQueueManager()
{
    qDebug() << "SyncQueueManager: Destroyed";
}

int SyncQueueManager::addOperation(OperationType operationType, const QJsonObject &data)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_dbManager->isConnected()) {
        qWarning() << "SyncQueueManager::addOperation: Database not connected";
        return -1;
    }
    
    // Create sync queue entry
    SyncQueueEntry entry;
    entry.operationType = operationType;
    entry.data = data;
    entry.status = Pending;
    entry.retryCount = 0;
    entry.createdAt = QDateTime::currentDateTime();
    
    // Insert into database using DatabaseManager
    if (!m_dbManager->insertSyncQueueEntry(
            operationTypeToString(operationType),
            QJsonDocument(data).toJson(QJsonDocument::Compact),
            syncStatusToString(Pending),
            0,
            entry.createdAt.toString(Qt::ISODate))) {
        qWarning() << "SyncQueueManager::addOperation: Failed to insert sync queue entry:"
                   << m_dbManager->lastError();
        return -1;
    }

    // Get the last inserted ID
    QSqlQuery query = m_dbManager->executeSyncQueueQuery("SELECT last_insert_rowid()");
    int entryId = -1;
    if (query.next()) {
        entryId = query.value(0).toInt();
    }
    entry.id = entryId;
    
    emit operationAdded(entry);
    emit queueStatusChanged(pendingCount(), failedCount());
    
    qDebug() << "SyncQueueManager::addOperation: Added operation" << entryId
             << "type:" << operationTypeToString(operationType);
    
    return entryId;
}

QList<SyncQueueManager::SyncQueueEntry> SyncQueueManager::getPendingOperations()
{
    QMutexLocker locker(&m_mutex);
    QList<SyncQueueEntry> entries;
    
    if (!m_dbManager->isConnected()) {
        qWarning() << "SyncQueueManager::getPendingOperations: Database not connected";
        return entries;
    }
    
    QSqlQuery query = m_dbManager->executeSyncQueueQuery(R"(
        SELECT id, operation_type, data_json, status, retry_count,
               created_at, last_attempt_at, completed_at, error_message
        FROM sync_queue
        WHERE status = ?
        ORDER BY created_at ASC
    )", {syncStatusToString(Pending)});

    if (!query.isActive()) {
        qWarning() << "SyncQueueManager::getPendingOperations: Query failed:"
                   << m_dbManager->lastError();
        return entries;
    }
    
    while (query.next()) {
        SyncQueueEntry entry;
        entry.id = query.value("id").toInt();
        entry.operationType = stringToOperationType(query.value("operation_type").toString());
        
        QJsonDocument doc = QJsonDocument::fromJson(query.value("data_json").toByteArray());
        entry.data = doc.object();
        
        entry.status = stringToSyncStatus(query.value("status").toString());
        entry.retryCount = query.value("retry_count").toInt();
        entry.createdAt = QDateTime::fromString(query.value("created_at").toString(), Qt::ISODate);
        
        if (!query.value("last_attempt_at").isNull()) {
            entry.lastAttemptAt = QDateTime::fromString(query.value("last_attempt_at").toString(), Qt::ISODate);
        }
        if (!query.value("completed_at").isNull()) {
            entry.completedAt = QDateTime::fromString(query.value("completed_at").toString(), Qt::ISODate);
        }
        entry.errorMessage = query.value("error_message").toString();
        
        entries.append(entry);
    }
    
    qDebug() << "SyncQueueManager::getPendingOperations: Found" << entries.size() << "pending operations";
    return entries;
}

bool SyncQueueManager::markAsCompleted(int entryId)
{
    return updateStatus(entryId, Completed);
}

bool SyncQueueManager::markAsFailed(int entryId, const QString &errorMessage)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_dbManager->isConnected()) {
        qWarning() << "SyncQueueManager::markAsFailed: Database not connected";
        return false;
    }
    
    if (!m_dbManager->updateSyncQueueEntry(
            entryId,
            syncStatusToString(Failed),
            QDateTime::currentDateTime().toString(Qt::ISODate),
            QString(), // no completed_at
            errorMessage)) {
        qWarning() << "SyncQueueManager::markAsFailed: Update failed:"
                   << m_dbManager->lastError();
        return false;
    }
    
    emit operationFailed(entryId, errorMessage);
    emit queueStatusChanged(pendingCount(), failedCount());
    
    qDebug() << "SyncQueueManager::markAsFailed: Marked operation" << entryId << "as failed:" << errorMessage;
    return true;
}

bool SyncQueueManager::updateStatus(int entryId, SyncStatus status)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_dbManager->isConnected()) {
        qWarning() << "SyncQueueManager::updateStatus: Database not connected";
        return false;
    }
    
    QString completedAt = (status == Completed) ? QDateTime::currentDateTime().toString(Qt::ISODate) : QString();

    if (!m_dbManager->updateSyncQueueEntry(
            entryId,
            syncStatusToString(status),
            QDateTime::currentDateTime().toString(Qt::ISODate),
            completedAt)) {
        qWarning() << "SyncQueueManager::updateStatus: Update failed:"
                   << m_dbManager->lastError();
        return false;
    }
    
    if (status == Completed) {
        emit operationCompleted(entryId);
    }
    
    emit queueStatusChanged(pendingCount(), failedCount());
    
    qDebug() << "SyncQueueManager::updateStatus: Updated operation" << entryId 
             << "to status:" << syncStatusToString(status);
    return true;
}

int SyncQueueManager::pendingCount() const
{
    if (!m_dbManager->isConnected()) {
        return 0;
    }
    
    QSqlQuery query = m_dbManager->executeSyncQueueQuery(
        "SELECT COUNT(*) FROM sync_queue WHERE status = ?",
        {syncStatusToString(Pending)});

    if (query.isActive() && query.next()) {
        return query.value(0).toInt();
    }
    
    return 0;
}

int SyncQueueManager::failedCount() const
{
    if (!m_dbManager->isConnected()) {
        return 0;
    }
    
    QSqlQuery query = m_dbManager->executeSyncQueueQuery(
        "SELECT COUNT(*) FROM sync_queue WHERE status = ?",
        {syncStatusToString(Failed)});

    if (query.isActive() && query.next()) {
        return query.value(0).toInt();
    }
    
    return 0;
}

QDateTime SyncQueueManager::lastSyncTime() const
{
    if (!m_dbManager->isConnected()) {
        return QDateTime();
    }
    
    QSqlQuery query = m_dbManager->executeSyncQueueQuery(
        "SELECT MAX(completed_at) FROM sync_queue WHERE status = ?",
        {syncStatusToString(Completed)});

    if (query.isActive() && query.next() && !query.value(0).isNull()) {
        return QDateTime::fromString(query.value(0).toString(), Qt::ISODate);
    }
    
    return QDateTime();
}

void SyncQueueManager::onDatabaseError(const QString &error)
{
    qWarning() << "SyncQueueManager::onDatabaseError:" << error;
}

// Utility methods
QString SyncQueueManager::operationTypeToString(OperationType type) const
{
    switch (type) {
        case CreateAttempt: return "create_attempt";
        case UpdateAthlete: return "update_athlete";
        case UpdateCompetition: return "update_competition";
        default: return "unknown";
    }
}

SyncQueueManager::OperationType SyncQueueManager::stringToOperationType(const QString &str) const
{
    if (str == "create_attempt") return CreateAttempt;
    if (str == "update_athlete") return UpdateAthlete;
    if (str == "update_competition") return UpdateCompetition;
    return CreateAttempt; // Default
}

QString SyncQueueManager::syncStatusToString(SyncStatus status) const
{
    switch (status) {
        case Pending: return "pending";
        case InProgress: return "in_progress";
        case Completed: return "completed";
        case Failed: return "failed";
        default: return "pending";
    }
}

SyncQueueManager::SyncStatus SyncQueueManager::stringToSyncStatus(const QString &str) const
{
    if (str == "pending") return Pending;
    if (str == "in_progress") return InProgress;
    if (str == "completed") return Completed;
    if (str == "failed") return Failed;
    return Pending; // Default
}
