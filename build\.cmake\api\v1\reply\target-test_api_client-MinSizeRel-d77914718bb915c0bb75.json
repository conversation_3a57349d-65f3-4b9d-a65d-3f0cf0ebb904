{"artifacts": [{"path": "bin/MinSizeRel/test_api_client.exe"}, {"path": "bin/MinSizeRel/test_api_client.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "include_directories"], "files": ["C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "tests/CMakeLists.txt", "C:/Qt/install-x64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Config.cmake", "CMakeLists.txt", "C:/Qt/install-x64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 18, "parent": 0}, {"command": 2, "file": 0, "line": 935, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 690, "parent": 3}, {"command": 4, "file": 1, "line": 52, "parent": 0}, {"file": 5}, {"command": 7, "file": 5, "line": 14, "parent": 6}, {"file": 4, "parent": 7}, {"command": 7, "file": 4, "line": 218, "parent": 8}, {"file": 3, "parent": 9}, {"command": 6, "file": 3, "line": 55, "parent": 10}, {"file": 2, "parent": 11}, {"command": 5, "file": 2, "line": 61, "parent": 12}, {"command": 7, "file": 4, "line": 218, "parent": 8}, {"file": 7, "parent": 14}, {"command": 6, "file": 7, "line": 55, "parent": 15}, {"file": 6, "parent": 16}, {"command": 5, "file": 6, "line": 61, "parent": 17}, {"command": 6, "file": 7, "line": 43, "parent": 15}, {"file": 12, "parent": 19}, {"command": 9, "file": 12, "line": 45, "parent": 20}, {"command": 8, "file": 11, "line": 137, "parent": 21}, {"command": 7, "file": 10, "line": 76, "parent": 22}, {"file": 9, "parent": 23}, {"command": 6, "file": 9, "line": 55, "parent": 24}, {"file": 8, "parent": 25}, {"command": 5, "file": 8, "line": 61, "parent": 26}, {"command": 4, "file": 0, "line": 640, "parent": 2}, {"command": 7, "file": 4, "line": 218, "parent": 8}, {"file": 14, "parent": 29}, {"command": 6, "file": 14, "line": 57, "parent": 30}, {"file": 13, "parent": 31}, {"command": 5, "file": 13, "line": 61, "parent": 32}, {"command": 10, "file": 5, "line": 27, "parent": 6}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O1 /Ob1 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 28, "fragment": "-Zc:__cplusplus"}, {"backtrace": 28, "fragment": "-permissive-"}, {"backtrace": 28, "fragment": "-utf-8"}], "defines": [{"backtrace": 28, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_NETWORK_LIB"}, {"backtrace": 28, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_TESTCASE_BUILDDIR=\"C:/PROJECT/HighJump/build/tests\""}, {"backtrace": 5, "define": "QT_TESTCASE_SOURCEDIR=\"C:/PROJECT/HighJump/tests\""}, {"backtrace": 5, "define": "QT_TESTLIB_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 28, "define": "UNICODE"}, {"backtrace": 28, "define": "WIN32"}, {"backtrace": 28, "define": "WIN64"}, {"backtrace": 28, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 28, "define": "_UNICODE"}, {"backtrace": 28, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/PROJECT/HighJump/build/tests/test_api_client_autogen/include_MinSizeRel"}, {"backtrace": 34, "path": "C:/PROJECT/HighJump/src"}, {"backtrace": 28, "isSystem": true, "path": "C:/Qt/install-x64/include/QtCore"}, {"backtrace": 28, "isSystem": true, "path": "C:/Qt/install-x64/include"}, {"backtrace": 28, "isSystem": true, "path": "C:/Qt/install-x64/mkspecs/win32-arm64-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtNetwork"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtWidgets"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtTest"}], "language": "CXX", "languageStandard": {"backtraces": [28, 28], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "test_api_client::@a44f0ac069e85531cdee", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O1 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /subsystem:console", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Network.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Widgets.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Test.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Gui.lib", "role": "libraries"}, {"backtrace": 27, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 27, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 27, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 27, "fragment": "d3d12.lib", "role": "libraries"}, {"backtrace": 28, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Core.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "userenv.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "test_api_client", "nameOnDisk": "test_api_client.exe", "paths": {"build": "tests", "source": "tests"}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1, 2, 3]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/tests/test_api_client_autogen/mocs_compilation_MinSizeRel.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "tests/unit/test_api_client.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/api/api_client.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/utils/config_manager.cpp", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}