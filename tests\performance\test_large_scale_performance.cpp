#include <QtTest/QtTest>
#include <QApplication>
#include <QElapsedTimer>
#include <QTableView>
#include <QScrollBar>

#include "models/athlete_table_model.h"
#include "ui/athlete_delegate.h"
#include "ui/scoring_view.h"
#include "models/ranking_calculator.h"
#include "models/competition_state.h"
#include "models/athlete.h"
#include "models/competition.h"

/**
 * @brief 大规模性能测试 - 验证100+运动员场景
 * 
 * 测试系统在大规模数据下的性能表现：
 * - 100+ 运动员的数据加载性能
 * - 表格渲染和滚动性能
 * - 排名计算性能
 * - 内存使用情况
 * - UI响应性能
 */
class TestLargeScalePerformance : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void init();
    void cleanup();
    void cleanupTestCase();
    
    // 数据加载性能测试
    void testLargeDatasetLoading();
    void testModelPerformance();
    void testRankingCalculationPerformance();
    
    // UI性能测试
    void testTableRenderingPerformance();
    void testScrollingPerformance();
    void testDelegateRenderingPerformance();
    
    // 内存性能测试
    void testMemoryUsage();
    void testMemoryLeaks();
    
    // 交互性能测试
    void testAttemptRecordingPerformance();
    void testRealTimeUpdatePerformance();
    void testKeyboardShortcutPerformance();
    
    // 综合性能测试
    void testFullWorkflowPerformance();

private:
    // 测试数据创建
    void createLargeDataset(int athleteCount = 150);
    void createMassiveDataset(int athleteCount = 500);
    Athlete* createTestAthlete(int id, const QString &name, int number);
    void populateAttemptData(int athleteCount);
    
    // 性能测量工具
    void measureExecutionTime(const QString &testName, std::function<void()> testFunction);
    void measureMemoryUsage(const QString &testName);
    qint64 getCurrentMemoryUsage();
    
    // 测试组件
    AthleteTableModel *m_model;
    AthleteDelegate *m_delegate;
    ScoringView *m_scoringView;
    RankingCalculator *m_rankingCalculator;
    CompetitionState *m_competitionState;
    QTableView *m_tableView;
    
    // 测试数据
    Competition *m_competition;
    QList<Athlete*> m_athletes;
    QList<int> m_heightProgression;
    
    // 性能基准
    static const int TARGET_ATHLETE_COUNT = 150;
    static const int MASSIVE_ATHLETE_COUNT = 500;
    static const int MAX_LOADING_TIME_MS = 1000;      // 1秒内加载完成
    static const int MAX_RANKING_TIME_MS = 500;       // 500ms内完成排名计算
    static const int MAX_RENDER_TIME_MS = 100;        // 100ms内完成渲染
    static const int MAX_SCROLL_TIME_MS = 50;         // 50ms内完成滚动
    static const qint64 MAX_MEMORY_MB = 100;          // 最大100MB内存使用
};

void TestLargeScalePerformance::initTestCase()
{
    // 确保QApplication存在
    if (!QApplication::instance()) {
        int argc = 0;
        char **argv = nullptr;
        new QApplication(argc, argv);
    }
    
    qDebug() << "=== Large Scale Performance Test Suite ===";
    qDebug() << "Target athlete count:" << TARGET_ATHLETE_COUNT;
    qDebug() << "Massive athlete count:" << MASSIVE_ATHLETE_COUNT;
    qDebug() << "Performance thresholds:";
    qDebug() << "  - Loading time:" << MAX_LOADING_TIME_MS << "ms";
    qDebug() << "  - Ranking calculation:" << MAX_RANKING_TIME_MS << "ms";
    qDebug() << "  - Rendering time:" << MAX_RENDER_TIME_MS << "ms";
    qDebug() << "  - Memory usage:" << MAX_MEMORY_MB << "MB";
}

void TestLargeScalePerformance::init()
{
    // 创建测试组件
    m_model = new AthleteTableModel(this);
    m_delegate = new AthleteDelegate(this);
    m_scoringView = new ScoringView(this);
    m_rankingCalculator = new RankingCalculator(this);
    m_competitionState = new CompetitionState(this);
    m_tableView = new QTableView(this);
    
    // 配置组件
    m_tableView->setModel(m_model);
    m_tableView->setItemDelegate(m_delegate);
    m_delegate->setAthleteTableModel(m_model);
    
    // 创建高度序列
    m_heightProgression = {140, 145, 150, 155, 160, 165, 170, 175, 180, 185, 190, 195, 200, 205, 210, 215, 220};
    
    // 测量初始内存使用
    measureMemoryUsage("Initial state");
}

void TestLargeScalePerformance::cleanup()
{
    delete m_scoringView;
    delete m_tableView;
    delete m_competitionState;
    delete m_rankingCalculator;
    delete m_delegate;
    delete m_model;
    delete m_competition;
    qDeleteAll(m_athletes);
    
    m_scoringView = nullptr;
    m_tableView = nullptr;
    m_competitionState = nullptr;
    m_rankingCalculator = nullptr;
    m_delegate = nullptr;
    m_model = nullptr;
    m_competition = nullptr;
    m_athletes.clear();
}

void TestLargeScalePerformance::cleanupTestCase()
{
    qDebug() << "=== Performance Test Suite Completed ===";
}

void TestLargeScalePerformance::testLargeDatasetLoading()
{
    qDebug() << "\n--- Testing Large Dataset Loading Performance ---";
    
    measureExecutionTime("Create 150 athletes", [this]() {
        createLargeDataset(TARGET_ATHLETE_COUNT);
    });
    
    QCOMPARE(m_athletes.size(), TARGET_ATHLETE_COUNT);
    
    measureExecutionTime("Load competition with 150 athletes", [this]() {
        m_model->loadCompetition(m_competition);
    });
    
    QCOMPARE(m_model->rowCount(), TARGET_ATHLETE_COUNT);
    QVERIFY(m_model->columnCount() > 6); // Fixed columns + height columns
    
    measureMemoryUsage("After loading 150 athletes");
}

void TestLargeScalePerformance::testModelPerformance()
{
    qDebug() << "\n--- Testing Model Performance ---";
    
    createLargeDataset(TARGET_ATHLETE_COUNT);
    m_model->loadCompetition(m_competition);
    
    // 测试数据访问性能
    QElapsedTimer timer;
    timer.start();
    
    // 访问所有单元格数据
    for (int row = 0; row < m_model->rowCount(); ++row) {
        for (int col = 0; col < m_model->columnCount(); ++col) {
            QModelIndex index = m_model->index(row, col);
            m_model->data(index, Qt::DisplayRole);
        }
    }
    
    qint64 dataAccessTime = timer.elapsed();
    qDebug() << "Data access time for" << (m_model->rowCount() * m_model->columnCount()) 
             << "cells:" << dataAccessTime << "ms";
    
    QVERIFY2(dataAccessTime < MAX_RENDER_TIME_MS * 10, 
             QString("Data access too slow: %1ms").arg(dataAccessTime).toLocal8Bit());
}

void TestLargeScalePerformance::testRankingCalculationPerformance()
{
    qDebug() << "\n--- Testing Ranking Calculation Performance ---";
    
    createLargeDataset(TARGET_ATHLETE_COUNT);
    populateAttemptData(TARGET_ATHLETE_COUNT);
    
    measureExecutionTime("Ranking calculation for 150 athletes", [this]() {
        auto result = m_rankingCalculator->calculateRankings(m_athletes, {}, m_heightProgression);
        QCOMPARE(result.totalAthletes, TARGET_ATHLETE_COUNT);
    });
    
    // 测试大规模数据
    createMassiveDataset(MASSIVE_ATHLETE_COUNT);
    populateAttemptData(MASSIVE_ATHLETE_COUNT);
    
    QElapsedTimer timer;
    timer.start();
    auto result = m_rankingCalculator->calculateRankings(m_athletes, {}, m_heightProgression);
    qint64 massiveRankingTime = timer.elapsed();
    
    qDebug() << "Massive ranking calculation (" << MASSIVE_ATHLETE_COUNT << "athletes):" 
             << massiveRankingTime << "ms";
    
    QCOMPARE(result.totalAthletes, MASSIVE_ATHLETE_COUNT);
    QVERIFY2(massiveRankingTime < MAX_RANKING_TIME_MS * 5, 
             QString("Massive ranking calculation too slow: %1ms").arg(massiveRankingTime).toLocal8Bit());
}

void TestLargeScalePerformance::testTableRenderingPerformance()
{
    qDebug() << "\n--- Testing Table Rendering Performance ---";
    
    createLargeDataset(TARGET_ATHLETE_COUNT);
    m_model->loadCompetition(m_competition);
    
    // 显示表格
    m_tableView->show();
    m_tableView->resize(1200, 800);
    
    measureExecutionTime("Initial table rendering", [this]() {
        QApplication::processEvents();
        m_tableView->viewport()->update();
        QApplication::processEvents();
    });
    
    measureMemoryUsage("After table rendering");
}

void TestLargeScalePerformance::testScrollingPerformance()
{
    qDebug() << "\n--- Testing Scrolling Performance ---";
    
    createLargeDataset(TARGET_ATHLETE_COUNT);
    m_model->loadCompetition(m_competition);
    m_tableView->show();
    
    QScrollBar *vScrollBar = m_tableView->verticalScrollBar();
    
    measureExecutionTime("Scroll to bottom", [this, vScrollBar]() {
        vScrollBar->setValue(vScrollBar->maximum());
        QApplication::processEvents();
    });
    
    measureExecutionTime("Scroll to top", [this, vScrollBar]() {
        vScrollBar->setValue(0);
        QApplication::processEvents();
    });
    
    // 测试快速滚动
    QElapsedTimer timer;
    timer.start();
    
    for (int i = 0; i < 10; ++i) {
        vScrollBar->setValue(i * vScrollBar->maximum() / 10);
        QApplication::processEvents();
    }
    
    qint64 fastScrollTime = timer.elapsed();
    qDebug() << "Fast scroll test (10 positions):" << fastScrollTime << "ms";
    
    QVERIFY2(fastScrollTime < MAX_SCROLL_TIME_MS * 10, 
             QString("Fast scrolling too slow: %1ms").arg(fastScrollTime).toLocal8Bit());
}

void TestLargeScalePerformance::testDelegateRenderingPerformance()
{
    qDebug() << "\n--- Testing Delegate Rendering Performance ---";
    
    createLargeDataset(TARGET_ATHLETE_COUNT);
    m_model->loadCompetition(m_competition);
    m_tableView->show();
    
    // 强制重绘所有可见单元格
    measureExecutionTime("Delegate rendering for visible cells", [this]() {
        m_tableView->viewport()->update();
        QApplication::processEvents();
    });
}

void TestLargeScalePerformance::testMemoryUsage()
{
    qDebug() << "\n--- Testing Memory Usage ---";
    
    qint64 initialMemory = getCurrentMemoryUsage();
    
    createLargeDataset(TARGET_ATHLETE_COUNT);
    qint64 afterDataCreation = getCurrentMemoryUsage();
    
    m_model->loadCompetition(m_competition);
    qint64 afterModelLoading = getCurrentMemoryUsage();
    
    m_tableView->show();
    QApplication::processEvents();
    qint64 afterRendering = getCurrentMemoryUsage();
    
    qDebug() << "Memory usage progression:";
    qDebug() << "  Initial:" << initialMemory / 1024 / 1024 << "MB";
    qDebug() << "  After data creation:" << afterDataCreation / 1024 / 1024 << "MB";
    qDebug() << "  After model loading:" << afterModelLoading / 1024 / 1024 << "MB";
    qDebug() << "  After rendering:" << afterRendering / 1024 / 1024 << "MB";
    
    qint64 totalMemoryIncrease = afterRendering - initialMemory;
    qDebug() << "Total memory increase:" << totalMemoryIncrease / 1024 / 1024 << "MB";
    
    QVERIFY2(totalMemoryIncrease < MAX_MEMORY_MB * 1024 * 1024, 
             QString("Memory usage too high: %1MB").arg(totalMemoryIncrease / 1024 / 1024).toLocal8Bit());
}

void TestLargeScalePerformance::testMemoryLeaks()
{
    qDebug() << "\n--- Testing Memory Leaks ---";
    
    qint64 baselineMemory = getCurrentMemoryUsage();
    
    // 执行多次加载/卸载循环
    for (int i = 0; i < 5; ++i) {
        createLargeDataset(50);
        m_model->loadCompetition(m_competition);
        m_model->clearData();
        
        delete m_competition;
        qDeleteAll(m_athletes);
        m_competition = nullptr;
        m_athletes.clear();
        
        QApplication::processEvents();
    }
    
    qint64 finalMemory = getCurrentMemoryUsage();
    qint64 memoryLeak = finalMemory - baselineMemory;
    
    qDebug() << "Memory leak test:";
    qDebug() << "  Baseline:" << baselineMemory / 1024 / 1024 << "MB";
    qDebug() << "  Final:" << finalMemory / 1024 / 1024 << "MB";
    qDebug() << "  Potential leak:" << memoryLeak / 1024 / 1024 << "MB";
    
    // 允许小量内存增长（由于Qt内部缓存等）
    QVERIFY2(memoryLeak < 10 * 1024 * 1024, 
             QString("Potential memory leak detected: %1MB").arg(memoryLeak / 1024 / 1024).toLocal8Bit());
}

void TestLargeScalePerformance::createLargeDataset(int athleteCount)
{
    qDeleteAll(m_athletes);
    m_athletes.clear();
    delete m_competition;
    
    // 创建运动员
    for (int i = 1; i <= athleteCount; ++i) {
        QString name = QString("运动员%1").arg(i, 3, 10, QChar('0'));
        m_athletes.append(createTestAthlete(i, name, i));
    }
    
    // 创建比赛
    m_competition = new Competition(this);
    m_competition->setId(1);
    m_competition->setName(QString("大规模测试比赛 (%1人)").arg(athleteCount));
    m_competition->setVenue("测试场地");
    m_competition->setHeightProgression(m_heightProgression);
    
    for (Athlete *athlete : m_athletes) {
        m_competition->addAthlete(athlete);
    }
}

void TestLargeScalePerformance::createMassiveDataset(int athleteCount)
{
    createLargeDataset(athleteCount);
}

Athlete* TestLargeScalePerformance::createTestAthlete(int id, const QString &name, int number)
{
    Athlete *athlete = new Athlete(this);
    athlete->setId(id);
    athlete->setFirstName(name.split(' ').first());
    athlete->setLastName(name.split(' ').last());
    athlete->setStartNumber(number);
    athlete->setClub(QString("队伍%1").arg((id % 10) + 1));
    return athlete;
}

void TestLargeScalePerformance::populateAttemptData(int athleteCount)
{
    // 为性能测试创建一些示例试跳数据
    // 这里只是创建数据结构，实际的试跳数据会在真实使用中填充
    Q_UNUSED(athleteCount)
}

void TestLargeScalePerformance::measureExecutionTime(const QString &testName, std::function<void()> testFunction)
{
    QElapsedTimer timer;
    timer.start();

    testFunction();

    qint64 elapsed = timer.elapsed();
    qDebug() << QString("  %1: %2ms").arg(testName, -40).arg(elapsed);
}

void TestLargeScalePerformance::measureMemoryUsage(const QString &testName)
{
    qint64 memory = getCurrentMemoryUsage();
    qDebug() << QString("  %1: %2MB").arg(testName, -40).arg(memory / 1024 / 1024);
}

qint64 TestLargeScalePerformance::getCurrentMemoryUsage()
{
    // 简化的内存使用测量
    // 在实际实现中，可以使用平台特定的API获取更准确的内存使用情况
    return QApplication::instance()->property("memoryUsage").toLongLong();
}

void TestLargeScalePerformance::testAttemptRecordingPerformance()
{
    qDebug() << "\n--- Testing Attempt Recording Performance ---";

    createLargeDataset(TARGET_ATHLETE_COUNT);
    m_model->loadCompetition(m_competition);

    // 测试批量试跳记录性能
    measureExecutionTime("Record 1000 attempts", [this]() {
        for (int i = 0; i < 1000; ++i) {
            int athleteId = (i % TARGET_ATHLETE_COUNT) + 1;
            int height = m_heightProgression[i % m_heightProgression.size()];
            int attemptNumber = (i % 3) + 1;

            m_model->recordAttempt(athleteId, height, attemptNumber,
                                 static_cast<JumpAttempt::AttemptResult>(i % 4));
        }
    });
}

void TestLargeScalePerformance::testRealTimeUpdatePerformance()
{
    qDebug() << "\n--- Testing Real-time Update Performance ---";

    createLargeDataset(TARGET_ATHLETE_COUNT);
    m_model->loadCompetition(m_competition);
    m_tableView->show();

    // 测试实时更新性能
    measureExecutionTime("100 real-time updates", [this]() {
        for (int i = 0; i < 100; ++i) {
            int athleteId = (i % TARGET_ATHLETE_COUNT) + 1;
            int height = m_heightProgression[i % m_heightProgression.size()];

            m_model->recordAttempt(athleteId, height, 1, JumpAttempt::Pass);
            QApplication::processEvents();
        }
    });
}

void TestLargeScalePerformance::testKeyboardShortcutPerformance()
{
    qDebug() << "\n--- Testing Keyboard Shortcut Performance ---";

    createLargeDataset(TARGET_ATHLETE_COUNT);
    m_model->loadCompetition(m_competition);

    // 测试键盘快捷键响应性能
    measureExecutionTime("100 keyboard shortcuts", [this]() {
        for (int i = 0; i < 100; ++i) {
            // 模拟键盘快捷键操作
            QApplication::processEvents();
        }
    });
}

void TestLargeScalePerformance::testFullWorkflowPerformance()
{
    qDebug() << "\n--- Testing Full Workflow Performance ---";

    measureExecutionTime("Complete workflow simulation", [this]() {
        // 1. 创建大规模数据
        createLargeDataset(TARGET_ATHLETE_COUNT);

        // 2. 加载到模型
        m_model->loadCompetition(m_competition);

        // 3. 显示界面
        m_tableView->show();
        QApplication::processEvents();

        // 4. 模拟试跳记录
        for (int i = 0; i < 50; ++i) {
            int athleteId = (i % TARGET_ATHLETE_COUNT) + 1;
            int height = m_heightProgression[0];
            m_model->recordAttempt(athleteId, height, 1, JumpAttempt::Pass);
        }

        // 5. 计算排名
        m_model->updateRankings();

        // 6. 更新界面
        QApplication::processEvents();
    });

    measureMemoryUsage("After full workflow");
}

QTEST_MAIN(TestLargeScalePerformance)
#include "test_large_scale_performance.moc"
