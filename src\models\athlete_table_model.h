#ifndef ATHLETE_TABLE_MODEL_H
#define ATHLETE_TABLE_MODEL_H

#include <QAbstractTableModel>
#include <QList>
#include <QHash>
#include <QTimer>
#include <QMutex>

class Athlete;
class Competition;
class JumpAttempt;
class DatabaseManager;

/**
 * @brief Table model for athlete scoring display in High Jump Competition Management
 * 
 * This class provides a comprehensive table model for displaying athlete performance
 * data in the main scoring interface. It implements Qt's QAbstractTableModel to provide
 * efficient data display and real-time updates for competition scoring.
 * 
 * The AthleteTableModel provides:
 * - Dynamic column management for height progression
 * - Real-time ranking calculation and display
 * - Attempt result tracking and visualization
 * - Performance optimized data access
 * - Thread-safe data operations
 * - Automatic data refresh and synchronization
 * 
 * Column Structure:
 * - Fixed columns: Rank, Number, Name, Team, Best Height, Status
 * - Dynamic columns: Height attempts (150cm, 153cm, 156cm, etc.)
 * - Each height column shows 3 attempt results (O, X, -, R)
 */
class AthleteTableModel : public QAbstractTableModel
{
    Q_OBJECT

public:
    /**
     * @brief Column types for the athlete table
     */
    enum ColumnType {
        RankColumn = 0,
        NumberColumn,
        NameColumn,
        TeamColumn,
        BestHeightColumn,
        StatusColumn,
        FirstHeightColumn  // Dynamic height columns start here
    };

    /**
     * @brief Data roles for custom data access
     */
    enum DataRole {
        AthleteIdRole = Qt::UserRole + 1,
        HeightRole,
        AttemptNumberRole,
        AttemptResultRole,
        RankingRole,
        StatusRole
    };

    explicit AthleteTableModel(QObject *parent = nullptr);
    ~AthleteTableModel();

    // QAbstractTableModel interface
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;
    Qt::ItemFlags flags(const QModelIndex &index) const override;

    // Competition management
    /**
     * @brief Load competition data and initialize the model
     * @param competition Competition object to load
     */
    void loadCompetition(Competition *competition);

    /**
     * @brief Clear all data and reset the model
     */
    void clearData();

    /**
     * @brief Get the current competition
     * @return Pointer to current competition or nullptr
     */
    Competition* getCurrentCompetition() const;

    // Height management
    /**
     * @brief Set the height progression for the competition
     * @param heights List of heights in centimeters
     */
    void setHeightProgression(const QList<int> &heights);

    /**
     * @brief Get the current height progression
     * @return List of heights in centimeters
     */
    QList<int> getHeightProgression() const;

    /**
     * @brief Add a new height to the progression
     * @param height Height in centimeters
     */
    void addHeight(int height);

    // Attempt management
    /**
     * @brief Record an attempt for an athlete at a specific height
     * @param athleteId ID of the athlete
     * @param height Height attempted
     * @param attemptNumber Attempt number (1-3)
     * @param result Attempt result
     * @return true if successful, false otherwise
     */
    bool recordAttempt(int athleteId, int height, int attemptNumber, JumpAttempt::AttemptResult result);

    /**
     * @brief Get attempt result for athlete at specific height and attempt number
     * @param athleteId ID of the athlete
     * @param height Height attempted
     * @param attemptNumber Attempt number (1-3)
     * @return Attempt result or Invalid if not found
     */
    JumpAttempt::AttemptResult getAttemptResult(int athleteId, int height, int attemptNumber) const;

    // Ranking and statistics
    /**
     * @brief Update rankings for all athletes
     */
    void updateRankings();

    /**
     * @brief Get current ranking for an athlete
     * @param athleteId ID of the athlete
     * @return Current rank (1-based) or 0 if not ranked
     */
    int getAthleteRank(int athleteId) const;

    /**
     * @brief Get best height achieved by an athlete
     * @param athleteId ID of the athlete
     * @return Best height in centimeters or 0 if no successful attempts
     */
    int getAthleteBestHeight(int athleteId) const;

    // Utility methods
    /**
     * @brief Get athlete by row index
     * @param row Row index
     * @return Pointer to athlete or nullptr
     */
    Athlete* getAthleteByRow(int row) const;

    /**
     * @brief Get row index for athlete
     * @param athleteId ID of the athlete
     * @return Row index or -1 if not found
     */
    int getRowForAthlete(int athleteId) const;

    /**
     * @brief Get height for column index
     * @param column Column index
     * @return Height in centimeters or -1 if not a height column
     */
    int getHeightForColumn(int column) const;

    /**
     * @brief Get column index for height
     * @param height Height in centimeters
     * @return Column index or -1 if height not found
     */
    int getColumnForHeight(int height) const;

signals:
    /**
     * @brief Emitted when athlete data is updated
     * @param athleteId ID of the updated athlete
     */
    void athleteDataUpdated(int athleteId);

    /**
     * @brief Emitted when rankings are recalculated
     */
    void rankingsUpdated();

    /**
     * @brief Emitted when an attempt is recorded
     * @param athleteId ID of the athlete
     * @param height Height attempted
     * @param attemptNumber Attempt number
     * @param result Attempt result
     */
    void attemptRecorded(int athleteId, int height, int attemptNumber, JumpAttempt::AttemptResult result);

    /**
     * @brief Emitted when competition data is loaded
     */
    void competitionLoaded();

public slots:
    /**
     * @brief Refresh data from database
     */
    void refreshData();

    /**
     * @brief Handle athlete data changes
     * @param athleteId ID of the changed athlete
     */
    void onAthleteDataChanged(int athleteId);

private slots:
    /**
     * @brief Periodic data refresh timer
     */
    void onRefreshTimer();

private:
    // Data management
    void loadAthletes();
    void loadAttempts();
    void calculateRankings();
    void sortAthletesByRank();

    // Helper methods
    QString formatAttemptDisplay(int athleteId, int height) const;
    QVariant getAttemptData(int athleteId, int height, int role) const;
    bool isHeightColumn(int column) const;
    int getFixedColumnCount() const;

    // Data members
    Competition *m_competition;
    QList<Athlete*> m_athletes;
    QList<int> m_heightProgression;
    QHash<int, int> m_athleteRanks;  // athleteId -> rank
    QHash<int, int> m_athleteBestHeights;  // athleteId -> best height
    
    // Attempt data: athleteId -> height -> attemptNumber -> result
    QHash<int, QHash<int, QHash<int, JumpAttempt::AttemptResult>>> m_attempts;
    
    // Database and utilities
    DatabaseManager *m_dbManager;
    QTimer *m_refreshTimer;
    mutable QMutex m_dataMutex;
    
    // Constants
    static const int FIXED_COLUMN_COUNT = 6;
    static const int REFRESH_INTERVAL_MS = 5000;  // 5 seconds
};

#endif // ATHLETE_TABLE_MODEL_H
