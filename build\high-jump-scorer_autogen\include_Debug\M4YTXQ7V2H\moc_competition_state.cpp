/****************************************************************************
** Meta object code from reading C++ file 'competition_state.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/models/competition_state.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'competition_state.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN16CompetitionStateE_t {};
} // unnamed namespace

template <> constexpr inline auto CompetitionState::qt_create_metaobjectdata<qt_meta_tag_ZN16CompetitionStateE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "CompetitionState",
        "phaseChanged",
        "",
        "CompetitionPhase",
        "phase",
        "currentHeightChanged",
        "height",
        "athleteStatusChanged",
        "athleteId",
        "AthleteStatus",
        "status",
        "statisticsUpdated",
        "totalAthletes",
        "activeAthletes",
        "completedAthletes",
        "retiredAthletes",
        "stateSaved",
        "success",
        "stateLoaded",
        "competitionShouldFinish",
        "refreshState",
        "onAttemptResultUpdated",
        "result",
        "onRankingsUpdated",
        "onAutoSaveTimer",
        "onStateUpdateTimer"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'phaseChanged'
        QtMocHelpers::SignalData<void(CompetitionPhase)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 },
        }}),
        // Signal 'currentHeightChanged'
        QtMocHelpers::SignalData<void(int)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 6 },
        }}),
        // Signal 'athleteStatusChanged'
        QtMocHelpers::SignalData<void(int, AthleteStatus)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 8 }, { 0x80000000 | 9, 10 },
        }}),
        // Signal 'statisticsUpdated'
        QtMocHelpers::SignalData<void(int, int, int, int)>(11, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 12 }, { QMetaType::Int, 13 }, { QMetaType::Int, 14 }, { QMetaType::Int, 15 },
        }}),
        // Signal 'stateSaved'
        QtMocHelpers::SignalData<void(bool)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 17 },
        }}),
        // Signal 'stateLoaded'
        QtMocHelpers::SignalData<void(bool)>(18, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 17 },
        }}),
        // Signal 'competitionShouldFinish'
        QtMocHelpers::SignalData<void()>(19, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'refreshState'
        QtMocHelpers::SlotData<void()>(20, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'onAttemptResultUpdated'
        QtMocHelpers::SlotData<void(int, int, int)>(21, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 8 }, { QMetaType::Int, 6 }, { QMetaType::Int, 22 },
        }}),
        // Slot 'onRankingsUpdated'
        QtMocHelpers::SlotData<void()>(23, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'onAutoSaveTimer'
        QtMocHelpers::SlotData<void()>(24, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onStateUpdateTimer'
        QtMocHelpers::SlotData<void()>(25, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<CompetitionState, qt_meta_tag_ZN16CompetitionStateE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject CompetitionState::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16CompetitionStateE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16CompetitionStateE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN16CompetitionStateE_t>.metaTypes,
    nullptr
} };

void CompetitionState::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<CompetitionState *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->phaseChanged((*reinterpret_cast< std::add_pointer_t<CompetitionPhase>>(_a[1]))); break;
        case 1: _t->currentHeightChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 2: _t->athleteStatusChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<AthleteStatus>>(_a[2]))); break;
        case 3: _t->statisticsUpdated((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[4]))); break;
        case 4: _t->stateSaved((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 5: _t->stateLoaded((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 6: _t->competitionShouldFinish(); break;
        case 7: _t->refreshState(); break;
        case 8: _t->onAttemptResultUpdated((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 9: _t->onRankingsUpdated(); break;
        case 10: _t->onAutoSaveTimer(); break;
        case 11: _t->onStateUpdateTimer(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (CompetitionState::*)(CompetitionPhase )>(_a, &CompetitionState::phaseChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (CompetitionState::*)(int )>(_a, &CompetitionState::currentHeightChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (CompetitionState::*)(int , AthleteStatus )>(_a, &CompetitionState::athleteStatusChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (CompetitionState::*)(int , int , int , int )>(_a, &CompetitionState::statisticsUpdated, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (CompetitionState::*)(bool )>(_a, &CompetitionState::stateSaved, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (CompetitionState::*)(bool )>(_a, &CompetitionState::stateLoaded, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (CompetitionState::*)()>(_a, &CompetitionState::competitionShouldFinish, 6))
            return;
    }
}

const QMetaObject *CompetitionState::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CompetitionState::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16CompetitionStateE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int CompetitionState::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void CompetitionState::phaseChanged(CompetitionPhase _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void CompetitionState::currentHeightChanged(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void CompetitionState::athleteStatusChanged(int _t1, AthleteStatus _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1, _t2);
}

// SIGNAL 3
void CompetitionState::statisticsUpdated(int _t1, int _t2, int _t3, int _t4)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1, _t2, _t3, _t4);
}

// SIGNAL 4
void CompetitionState::stateSaved(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void CompetitionState::stateLoaded(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}

// SIGNAL 6
void CompetitionState::competitionShouldFinish()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}
QT_WARNING_POP
