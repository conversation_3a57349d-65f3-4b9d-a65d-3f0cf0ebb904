#ifndef ATHLETE_DELEGATE_H
#define ATHLETE_DELEGATE_H

#include <QStyledItemDelegate>
#include <QMenu>
#include <QAction>
#include <QTimer>

class AthleteTableModel;

/**
 * @brief Custom delegate for athlete table cells in High Jump Competition Management
 * 
 * This class provides specialized rendering and editing capabilities for the athlete
 * scoring table. It handles the visual representation of jump attempts, provides
 * interactive editing through context menus, and optimizes performance for large
 * datasets.
 * 
 * The AthleteDelegate provides:
 * - Custom rendering for jump attempt results (O, X, -, R)
 * - Color-coded background for different attempt outcomes
 * - Interactive context menu for attempt result selection
 * - Keyboard navigation and focus management
 * - Performance optimized painting for smooth scrolling
 * - Accessibility support for screen readers
 * 
 * Visual Design:
 * - Success (O): Green background with bold text
 * - Failure (X): Red background with bold text
 * - Skip (-): Yellow background with normal text
 * - Retire (R): Grey background with italic text
 * - Current selection: Blue border with highlight
 */
class AthleteDelegate : public QStyledItemDelegate
{
    Q_OBJECT

public:
    /**
     * @brief Attempt result types for visual representation
     */
    enum AttemptResult {
        Success = 0,  // O - Successful jump
        Failure = 1,  // X - Failed jump
        Skip = 2,     // - - Skipped attempt
        Retire = 3,   // R - Athlete retired
        Empty = 4     // No attempt recorded
    };

    explicit AthleteDelegate(QObject *parent = nullptr);
    ~AthleteDelegate();

    // QStyledItemDelegate interface
    void paint(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const override;
    QSize sizeHint(const QStyleOptionViewItem &option, const QModelIndex &index) const override;
    QWidget *createEditor(QWidget *parent, const QStyleOptionViewItem &option, const QModelIndex &index) const override;
    void setEditorData(QWidget *editor, const QModelIndex &index) const override;
    void setModelData(QWidget *editor, QAbstractItemModel *model, const QModelIndex &index) const override;
    void updateEditorGeometry(QWidget *editor, const QStyleOptionViewItem &option, const QModelIndex &index) const override;

    // Event handling
    bool editorEvent(QEvent *event, QAbstractItemModel *model, const QStyleOptionViewItem &option, const QModelIndex &index) override;

    // Configuration
    /**
     * @brief Set the athlete table model for data access
     * @param model Pointer to the athlete table model
     */
    void setAthleteTableModel(AthleteTableModel *model);

    /**
     * @brief Enable or disable context menu
     * @param enabled Whether context menu should be shown
     */
    void setContextMenuEnabled(bool enabled);

    /**
     * @brief Set custom colors for attempt results
     * @param result Attempt result type
     * @param backgroundColor Background color
     * @param textColor Text color
     */
    void setResultColors(AttemptResult result, const QColor &backgroundColor, const QColor &textColor);

    /**
     * @brief Enable or disable animations
     * @param enabled Whether animations should be used
     */
    void setAnimationsEnabled(bool enabled);

signals:
    /**
     * @brief Emitted when user requests to record an attempt
     * @param athleteId ID of the athlete
     * @param height Height being attempted
     * @param attemptNumber Attempt number (1-3)
     * @param result Attempt result
     */
    void attemptRequested(int athleteId, int height, int attemptNumber, AttemptResult result);

    /**
     * @brief Emitted when cell focus changes
     * @param athleteId ID of the athlete
     * @param height Height column
     */
    void cellFocusChanged(int athleteId, int height);

    /**
     * @brief Emitted when editing is requested
     * @param index Model index of the cell
     */
    void editingRequested(const QModelIndex &index);

protected:
    // Custom painting methods
    void paintFixedColumn(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const;
    void paintHeightColumn(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const;
    void paintAttemptResults(QPainter *painter, const QRect &rect, const QString &attempts, const QModelIndex &index) const;
    void paintBackground(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const;
    void paintFocusIndicator(QPainter *painter, const QStyleOptionViewItem &option) const;

    // Helper methods
    AttemptResult parseAttemptResult(const QChar &symbol) const;
    QChar getAttemptSymbol(AttemptResult result) const;
    QColor getBackgroundColor(AttemptResult result) const;
    QColor getTextColor(AttemptResult result) const;
    bool isHeightColumn(const QModelIndex &index) const;
    bool isEditableCell(const QModelIndex &index) const;

    // Context menu
    void showContextMenu(const QPoint &position, const QModelIndex &index);
    void createContextMenu();

private slots:
    void onContextMenuTriggered();
    void onAnimationTimer();

private:
    // Data access
    AthleteTableModel *m_model;
    
    // Context menu
    QMenu *m_contextMenu;
    QAction *m_successAction;
    QAction *m_failureAction;
    QAction *m_skipAction;
    QAction *m_retireAction;
    QAction *m_clearAction;
    QModelIndex m_contextMenuIndex;
    bool m_contextMenuEnabled;
    
    // Visual configuration
    QHash<AttemptResult, QColor> m_backgroundColors;
    QHash<AttemptResult, QColor> m_textColors;
    bool m_animationsEnabled;
    
    // Animation
    QTimer *m_animationTimer;
    qreal m_animationValue;
    QModelIndex m_animatedIndex;
    
    // Constants
    static const int CELL_PADDING = 4;
    static const int MIN_CELL_WIDTH = 60;
    static const int MIN_CELL_HEIGHT = 24;
    static const int FOCUS_BORDER_WIDTH = 2;
    static const int ANIMATION_DURATION_MS = 200;
    
    // Default colors
    static const QColor DEFAULT_SUCCESS_BG;
    static const QColor DEFAULT_FAILURE_BG;
    static const QColor DEFAULT_SKIP_BG;
    static const QColor DEFAULT_RETIRE_BG;
    static const QColor DEFAULT_EMPTY_BG;
    static const QColor DEFAULT_TEXT_COLOR;
    static const QColor DEFAULT_FOCUS_COLOR;
};

#endif // ATHLETE_DELEGATE_H
