﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\PROJECT\HighJump\build\high-jump-scorer_autogen\mocs_compilation_Debug.cpp">
      <Filter>Source Files\Generated</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\api\api_client.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\api\competition_dto.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\core\jump_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\models\athlete.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\models\athlete_table_model.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\models\competition.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\models\competition_state.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\models\jump_attempt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\models\ranking_calculator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\persistence\database_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\persistence\sync_queue_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\ui\athlete_delegate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\ui\athlete_dialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\ui\competition_selection_view.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\ui\main_window.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\ui\report_dialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\ui\scoring_view.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\ui\shortcut_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\ui\theme_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\ui\view_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\utils\config_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\utils\performance_monitor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\src\utils\report_generator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\build\high-jump-scorer_autogen\mocs_compilation_Release.cpp">
      <Filter>Source Files\Generated</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\build\high-jump-scorer_autogen\mocs_compilation_MinSizeRel.cpp">
      <Filter>Source Files\Generated</Filter>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\build\high-jump-scorer_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <Filter>Source Files\Generated</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\PROJECT\HighJump\src\api\api_client.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\api\competition_dto.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\core\jump_manager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\models\athlete.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\models\athlete_table_model.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\models\competition.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\models\competition_state.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\models\jump_attempt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\models\ranking_calculator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\persistence\database_manager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\persistence\sync_queue_manager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\ui\athlete_delegate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\ui\athlete_dialog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\ui\competition_selection_view.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\ui\main_window.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\ui\report_dialog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\ui\scoring_view.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\ui\shortcut_manager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\ui\theme_manager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\ui\view_manager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\utils\config_manager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\utils\performance_monitor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\PROJECT\HighJump\src\utils\report_generator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\PROJECT\HighJump\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{718AE6B4-87E6-31B2-85D2-91BCDDC6FE3E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{A7F01142-B173-3172-B4DC-C40C776519C4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Generated">
      <UniqueIdentifier>{43C1761A-FFC3-322F-8E1A-C25AA750CD96}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
