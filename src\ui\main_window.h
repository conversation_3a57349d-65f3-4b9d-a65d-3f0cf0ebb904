#ifndef MAIN_WINDOW_H
#define MAIN_WINDOW_H

#include <QMainWindow>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QSplitter>
#include <QTableWidget>
#include <QListWidget>
#include <QLabel>
#include <QPushButton>
#include <QSpinBox>
#include <QLineEdit>
#include <QTextEdit>
#include <QProgressBar>
#include <QAction>
#include <QActionGroup>
#include <QScopedPointer>
#include <QSharedPointer>
#include <QStackedWidget>

class Competition;
class Athlete;
class JumpAttempt;
class AthleteDialog;
class JumpManager;
class CompetitionSelectionView;
class ScoringView;
class ViewManager;

/**
 * @brief Main application window for High Jump Competition Management System
 * 
 * This class provides the primary user interface for managing high jump competitions,
 * including athlete management, jump recording, results tracking, and report generation.
 * The window is organized into three main panels:
 * - Left panel: Athlete list and management
 * - Center panel: Competition control and jump recording
 * - Right panel: Results table and competition status
 * 
 * The class implements a comprehensive menu system, toolbar actions, and status bar
 * for complete competition management functionality.
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief Constructs the main window
     * @param parent Parent widget (default: nullptr)
     * 
     * Initializes the main window with all UI components, menus, toolbars,
     * and connects all necessary signals and slots for competition management.
     */
    explicit MainWindow(QWidget *parent = nullptr);
    
    /**
     * @brief Destructor
     * 
     * Ensures proper cleanup of all UI components and data structures.
     * Smart pointers handle automatic memory management.
     */
    ~MainWindow();

protected:
    /**
     * @brief Handles window close events
     * @param event Close event information
     * 
     * Prompts user to save unsaved changes before closing the application.
     * Overrides QMainWindow::closeEvent().
     */
    void closeEvent(QCloseEvent *event) override;

private slots:
    // Competition selection slots
    /**
     * @brief Handles competition selection from competition selection view
     * @param competitionId The ID of the selected competition
     */
    void onCompetitionSelected(int competitionId);
    
    /**
     * @brief Handles competition selection cancellation
     */
    void onCompetitionSelectionCancelled();

    // API client slots
    /**
     * @brief Handles competitions received from API
     * @param competitions JSON array containing competition data
     */
    void onCompetitionsReceived(const QJsonArray &competitions);

    /**
     * @brief Handles network errors from API client
     * @param errorMessage Error message from network operation
     */
    void onNetworkError(const QString &errorMessage);

    /**
     * @brief Handles network status changes
     * @param isOnline Whether network is currently available
     */
    void onNetworkStatusChanged(bool isOnline);

    /**
     * @brief Handles retry requests from competition selection view
     */
    void onRetryRequested();
    
    // Menu actions
    /**
     * @brief Creates a new competition
     * 
     * Prompts user to save current competition if unsaved changes exist,
     * then creates a new empty competition with default settings.
     */
    void newCompetition();
    
    /**
     * @brief Opens an existing competition from file
     * 
     * Shows file dialog to select competition file and loads the competition data.
     * Validates file format and handles loading errors.
     */
    void openCompetition();
    
    /**
     * @brief Saves current competition to file
     * 
     * Saves competition data to the current file path. If no file path is set,
     * prompts user to select save location.
     */
    void saveCompetition();
    
    /**
     * @brief Saves competition to a new file
     * 
     * Shows save dialog to select new file location and saves competition data.
     * Updates current file path after successful save.
     */
    void saveCompetitionAs();
    
    /**
     * @brief Exports competition results
     * 
     * Generates and exports competition results in various formats (PDF, Excel, HTML).
     * Shows export dialog with format options and file location selection.
     */
    void exportResults();
    
    /**
     * @brief Prints competition results
     * 
     * Opens print dialog and prints current competition results.
     * Supports print preview and printer selection.
     */
    void printResults();
    
    /**
     * @brief Exits the application
     * 
     * Prompts user to save unsaved changes before closing the application.
     */
    void exitApplication();
    
    /**
     * @brief Adds a new athlete to the competition
     * 
     * Opens athlete dialog to input athlete information and adds the athlete
     * to the current competition's participant list.
     */
    void addAthlete();
    
    /**
     * @brief Edits the currently selected athlete
     * 
     * Opens athlete dialog with current athlete's information for editing.
     * Updates athlete data in the competition after successful edit.
     */
    void editAthlete();
    
    /**
     * @brief Removes the currently selected athlete
     * 
     * Prompts user for confirmation and removes the selected athlete
     * from the competition, including all associated jump records.
     */
    void removeAthlete();
    
    /**
     * @brief Clears all competition results
     * 
     * Prompts user for confirmation and clears all jump records and results
     * from the current competition while keeping athletes.
     */
    void clearResults();
    
    /**
     * @brief Starts the competition
     * 
     * Initializes competition state, sets up timing, and enables jump recording.
     * Updates UI to show competition is active.
     */
    void startCompetition();
    
    /**
     * @brief Pauses the competition
     * 
     * Temporarily stops competition timing and jump recording.
     * Competition can be resumed with startCompetition().
     */
    void pauseCompetition();
    
    /**
     * @brief Ends the competition
     * 
     * Finalizes competition state, stops timing, and generates final results.
     * Updates UI to show competition is completed.
     */
    void endCompetition();
    
    /**
     * @brief Moves to the next athlete in the rotation
     * 
     * Advances to the next athlete in the competition order.
     * Updates current athlete display and jump recording interface.
     */
    void nextAthlete();
    
    /**
     * @brief Moves to the previous athlete in the rotation
     * 
     * Returns to the previous athlete in the competition order.
     * Updates current athlete display and jump recording interface.
     */
    void previousAthlete();
    
    /**
     * @brief Sets jump height to 150cm
     * 
     * Quick height setting for common competition heights.
     * Updates height display and jump recording interface.
     */
    void setHeight150();
    
    /**
     * @brief Sets jump height to 153cm
     * 
     * Quick height setting for common competition heights.
     * Updates height display and jump recording interface.
     */
    void setHeight153();
    
    /**
     * @brief Sets jump height to 156cm
     * 
     * Quick height setting for common competition heights.
     * Updates height display and jump recording interface.
     */
    void setHeight156();
    
    /**
     * @brief Sets jump height to 159cm
     * 
     * Quick height setting for common competition heights.
     * Updates height display and jump recording interface.
     */
    void setHeight159();
    
    /**
     * @brief Opens dialog for custom height input
     * 
     * Shows height input dialog for setting non-standard jump heights.
     * Validates height input and updates competition settings.
     */
    void setCustomHeight();
    
    /**
     * @brief Advances to the next height increment
     * 
     * Increases current jump height by the configured increment (typically 3cm).
     * Updates height display and jump recording interface.
     */
    void advanceHeight();
    
    /**
     * @brief Shows about dialog
     * 
     * Displays application information, version, and credits.
     */
    void showAbout();
    
    /**
     * @brief Shows competition rules
     * 
     * Displays detailed high jump competition rules and regulations.
     */
    void showRules();
    
    /**
     * @brief Shows help documentation
     * 
     * Opens help system with user guide and feature documentation.
     */
    void showHelp();
    
    // Toolbar actions
    /**
     * @brief Records a successful jump attempt
     * 
     * Records a pass (successful jump) for the current athlete at the current height.
     * Updates athlete's jump history and results table.
     */
    void recordPass();
    
    /**
     * @brief Records a failed jump attempt
     * 
     * Records a fail (unsuccessful jump) for the current athlete at the current height.
     * Updates athlete's jump history and results table.
     */
    void recordFail();
    
    /**
     * @brief Records a skipped jump attempt
     * 
     * Records a skip (athlete chose not to attempt) for the current athlete at the current height.
     * Updates athlete's jump history and results table.
     */
    void recordSkip();
    
    // Widget interactions
    /**
     * @brief Handles athlete list selection changes
     * 
     * Updates current athlete when user selects different athlete in the list.
     * Updates jump recording interface and athlete information display.
     */
    void onAthleteSelectionChanged();
    
    /**
     * @brief Handles height spinbox value changes
     * 
     * Updates current jump height when user changes the height value.
     * Updates jump recording interface and height display.
     */
    void onHeightChanged();
    
    /**
     * @brief Updates current athlete display
     * 
     * Refreshes all UI elements related to the current athlete,
     * including jump history, attempt counts, and status information.
     */
    void updateCurrentAthlete();

private:
    /**
     * @brief Sets up the complete user interface
     * 
     * Initializes all UI components, layouts, and visual elements.
     * Called during constructor to create the main window layout.
     */
    void setupUI();
    
    /**
     * @brief Sets up menu system
     * 
     * Creates and configures all application menus including File, Athletes,
     * Competition, Height, and Help menus with appropriate actions.
     */
    void setupMenus();
    
    /**
     * @brief Sets up toolbar system
     * 
     * Creates and configures application toolbars with quick access buttons
     * for common actions like competition control and jump recording.
     */
    void setupToolbars();
    
    /**
     * @brief Sets up status bar
     * 
     * Creates status bar with competition information, athlete count,
     * timing display, and progress indicators.
     */
    void setupStatusBar();
    
    /**
     * @brief Sets up scoring view layout
     * 
     * Creates the main three-panel scoring layout with athlete list, competition control,
     * and results table panels.
     */
    void setupScoringView();
    
    /**
     * @brief Sets up athlete panel
     * 
     * Creates the athlete list panel with buttons for adding, editing, and removing athletes.
     */
    void setupAthletePanel();
    
    /**
     * @brief Sets up competition panel
     * 
     * Creates the competition control panel with jump recording interface.
     */
    void setupCompetitionPanel();
    
    /**
     * @brief Sets up results panel
     * 
     * Creates the results table panel showing competition standings.
     */
    void setupResultsPanel();
    
    /**
     * @brief Sets up signal-slot connections
     * 
     * Connects all UI signals to appropriate slot methods for event handling.
     * Ensures proper communication between UI components and business logic.
     */
    void setupConnections();
    
    /**
     * @brief Updates window title
     * 
     * Sets window title to reflect current competition name and file path.
     * Includes unsaved changes indicator when applicable.
     */
    void updateWindowTitle();
    
    /**
     * @brief Updates status bar information
     *
     * Refreshes status bar with current competition status, athlete count,
     * and other relevant information.
     */
    void updateStatusBar();

    // Competition data management
    /**
     * @brief Finds competition by ID in cached competition data
     * @param competitionId The ID of the competition to find
     * @return QJsonObject containing competition data, empty if not found
     */
    QJsonObject findCompetitionById(int competitionId) const;

    /**
     * @brief Loads competition data from JSON object
     * @param competitionData JSON object containing competition information
     * @return true if loading successful, false otherwise
     */
    bool loadCompetitionData(const QJsonObject &competitionData);
    
    /**
     * @brief Updates athlete list display
     * 
     * Refreshes the athlete list widget with current competition participants.
     * Updates athlete information and selection state.
     */
    void updateAthleteList();
    
    /**
     * @brief Updates results table display
     * 
     * Refreshes the results table with current competition results.
     * Shows athlete rankings, heights cleared, and attempt counts.
     */
    void updateResultsTable();
    
    /**
     * @brief Updates current display information
     * 
     * Refreshes all current athlete and competition information displays.
     * Updates jump recording interface and status indicators.
     */
    void updateCurrentDisplay();
    
    /**
     * @brief Generates HTML content for results
     * @return HTML string containing formatted competition results
     * 
     * Creates HTML-formatted competition results for export and printing.
     * Includes athlete rankings, heights, and detailed jump records.
     */
    QString generateResultsHtml();
    
    /**
     * @brief Prompts user to save unsaved changes
     * @return true if user chose to save or discard, false if cancelled
     * 
     * Shows dialog asking user whether to save, discard, or cancel when
     * unsaved changes are detected.
     */
    bool saveChanges();
    bool saveCompetitionToFile(const QString &fileName);
    
    /**
     * @brief Fetches available competitions from API
     * 
     * Triggers API call to retrieve competition list from server.
     */
    void fetchCompetitions();
    
    // UI Components - Using smart pointers for better memory management
    QScopedPointer<QStackedWidget> m_centralStack;
    QScopedPointer<ViewManager> m_viewManager;
    QScopedPointer<CompetitionSelectionView> m_selectionView;
    QScopedPointer<QWidget> m_scoringView;
    QScopedPointer<QSplitter> m_mainSplitter;
    
    // Left panel - Athletes
    QScopedPointer<QWidget> m_athletePanel;
    QScopedPointer<QListWidget> m_athleteList;
    QScopedPointer<QPushButton> m_addAthleteBtn;
    QScopedPointer<QPushButton> m_editAthleteBtn;
    QScopedPointer<QPushButton> m_removeAthleteBtn;
    
    // Center panel - Current competition
    QScopedPointer<QWidget> m_competitionPanel;
    QScopedPointer<QLabel> m_currentAthleteLabel;
    QScopedPointer<QLabel> m_currentHeightLabel;
    QScopedPointer<QSpinBox> m_heightSpinBox;
    QScopedPointer<QPushButton> m_passBtn;
    QScopedPointer<QPushButton> m_failBtn;
    QScopedPointer<QPushButton> m_skipBtn;
    QScopedPointer<QLabel> m_attemptCountLabel;
    QScopedPointer<QTextEdit> m_notesEdit;
    
    // Right panel - Results
    QScopedPointer<QWidget> m_resultsPanel;
    QScopedPointer<QTableWidget> m_resultsTable;
    QScopedPointer<QLabel> m_competitionStatusLabel;
    
    // Menus
    QScopedPointer<QMenu> m_fileMenu;
    QScopedPointer<QMenu> m_athletesMenu;
    QScopedPointer<QMenu> m_competitionMenu;
    QScopedPointer<QMenu> m_heightMenu;
    QScopedPointer<QMenu> m_helpMenu;
    
    // Toolbars
    QScopedPointer<QToolBar> m_mainToolBar;
    QScopedPointer<QToolBar> m_competitionToolBar;
    QScopedPointer<QToolBar> m_heightToolBar;
    
    // Actions
    QScopedPointer<QAction> m_newAction;
    QScopedPointer<QAction> m_openAction;
    QScopedPointer<QAction> m_saveAction;
    QScopedPointer<QAction> m_saveAsAction;
    QScopedPointer<QAction> m_exportAction;
    QScopedPointer<QAction> m_printAction;
    QScopedPointer<QAction> m_exitAction;
    
    QScopedPointer<QAction> m_addAthleteAction;
    QScopedPointer<QAction> m_editAthleteAction;
    QScopedPointer<QAction> m_removeAthleteAction;
    QScopedPointer<QAction> m_clearResultsAction;
    
    QScopedPointer<QAction> m_startCompetitionAction;
    QScopedPointer<QAction> m_pauseCompetitionAction;
    QScopedPointer<QAction> m_endCompetitionAction;
    QScopedPointer<QAction> m_nextAthleteAction;
    QScopedPointer<QAction> m_previousAthleteAction;
    
    QScopedPointer<QAction> m_height150Action;
    QScopedPointer<QAction> m_height153Action;
    QScopedPointer<QAction> m_height156Action;
    QScopedPointer<QAction> m_height159Action;
    QScopedPointer<QAction> m_customHeightAction;
    QScopedPointer<QAction> m_advanceHeightAction;
    
    QScopedPointer<QAction> m_aboutAction;
    QScopedPointer<QAction> m_rulesAction;
    QScopedPointer<QAction> m_helpAction;
    
    // Status bar widgets
    QScopedPointer<QLabel> m_statusLabel;
    QScopedPointer<QLabel> m_athleteCountLabel;
    QScopedPointer<QLabel> m_timeLabel;
    QScopedPointer<QProgressBar> m_progressBar;
    
    // Data - Using shared pointers for shared ownership
    QSharedPointer<Competition> m_currentCompetition;
    QSharedPointer<Athlete> m_currentAthlete;
    QString m_currentFilePath;
    bool m_hasUnsavedChanges;

    // Cached competition data from API
    QJsonArray m_cachedCompetitions;
};

#endif // MAIN_WINDOW_H