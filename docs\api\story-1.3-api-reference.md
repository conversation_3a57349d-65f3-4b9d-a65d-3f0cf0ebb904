# Story 1.3 API参考文档

## 概述
本文档提供Story 1.3 "主计分界面实现"中所有公共API的详细参考。

## 核心组件API

### AthleteTableModel

#### 概述
继承自QAbstractTableModel的数据模型，用于管理运动员成绩数据。

#### 公共方法

```cpp
// 构造函数
explicit AthleteTableModel(QObject *parent = nullptr);

// QAbstractTableModel接口
int rowCount(const QModelIndex &parent = QModelIndex()) const override;
int columnCount(const QModelIndex &parent = QModelIndex()) const override;
QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;

// 比赛管理
void loadCompetition(Competition *competition);
void clearData();
Competition* getCurrentCompetition() const;

// 高度管理
void setHeightProgression(const QList<int> &heights);
QList<int> getHeightProgression() const;
void addHeight(int height);

// 试跳记录
bool recordAttempt(int athleteId, int height, int attemptNumber, JumpAttempt::AttemptResult result);
JumpAttempt::AttemptResult getAttemptResult(int athleteId, int height, int attemptNumber) const;

// 排名和统计
void updateRankings();
int getAthleteRank(int athleteId) const;
int getAthleteBestHeight(int athleteId) const;

// 工具方法
Athlete* getAthleteByRow(int row) const;
int getRowForAthlete(int athleteId) const;
int getHeightForColumn(int column) const;
int getColumnForHeight(int height) const;
```

#### 信号

```cpp
// 数据更新信号
void athleteDataUpdated(int athleteId);
void rankingsUpdated();
void attemptRecorded(int athleteId, int height, int attemptNumber, JumpAttempt::AttemptResult result);
void competitionLoaded();
```

#### 数据角色

```cpp
enum DataRole {
    AthleteIdRole = Qt::UserRole + 1,
    HeightRole,
    AttemptNumberRole,
    AttemptResultRole,
    RankingRole,
    StatusRole
};
```

### AthleteDelegate

#### 概述
继承自QStyledItemDelegate的自定义委托，用于渲染运动员成绩表格。

#### 公共方法

```cpp
// 构造函数
explicit AthleteDelegate(QObject *parent = nullptr);

// QStyledItemDelegate接口
void paint(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const override;
QSize sizeHint(const QStyleOptionViewItem &option, const QModelIndex &index) const override;
bool editorEvent(QEvent *event, QAbstractItemModel *model, const QStyleOptionViewItem &option, const QModelIndex &index) override;

// 配置方法
void setAthleteTableModel(AthleteTableModel *model);
void setContextMenuEnabled(bool enabled);
void setResultColors(AttemptResult result, const QColor &backgroundColor, const QColor &textColor);
void setAnimationsEnabled(bool enabled);
```

#### 信号

```cpp
// 交互信号
void attemptRequested(int athleteId, int height, int attemptNumber, AttemptResult result);
void cellFocusChanged(int athleteId, int height);
void editingRequested(const QModelIndex &index);
```

#### 枚举

```cpp
enum AttemptResult {
    Success = 0,  // O - 成功
    Failure = 1,  // X - 失败
    Skip = 2,     // - - 免跳
    Retire = 3,   // R - 退赛
    Empty = 4     // 无记录
};
```

### ScoringView

#### 概述
主计分界面视图，集成所有计分相关组件。

#### 公共方法

```cpp
// 构造函数
explicit ScoringView(QWidget *parent = nullptr);

// 比赛管理
void loadCompetition(Competition *competition);
Competition* getCurrentCompetition() const;
void clearCompetition();

// 组件访问
QTableView* getAthleteTableView() const;
AthleteTableModel* getAthleteTableModel() const;

// 比赛状态
void setCurrentHeight(int height);
int getCurrentHeight() const;
void updateCompetitionProgress();
```

#### 信号

```cpp
// 比赛事件信号
void attemptRecorded(int athleteId, int height, int attemptNumber, int result);
void competitionStateChanged(int height, int activeAthletes);
void competitionFinished();
```

#### 槽函数

```cpp
// 事件处理槽
void onAttemptRequested(int athleteId, int height, int attemptNumber, int result);
void onRankingsUpdated();
void onCompetitionProgressChanged();
void onNextHeight();
void refreshDisplay();
```

### ShortcutManager

#### 概述
键盘快捷键管理器，提供完整的键盘交互支持。

#### 公共方法

```cpp
// 构造函数
explicit ShortcutManager(QWidget *parent = nullptr);

// 配置方法
void setTableView(QTableView *tableView);
void setTableModel(AthleteTableModel *model);
void setEnabled(bool enabled);
bool isEnabled() const;

// 撤销/重做
bool canUndo() const;
bool canRedo() const;
int undoStackSize() const;
void clearUndoStack();

// 快捷键配置
void setCustomShortcut(const QString &action, const QKeySequence &keySequence);
void resetToDefaults();
```

#### 信号

```cpp
// 操作请求信号
void attemptRecordRequested(int athleteId, int height, int attemptNumber, AttemptResult result);
void navigationRequested(int direction);
void confirmationRequested();
void cancellationRequested();

// 撤销/重做信号
void operationUndone(const OperationRecord &operation);
void operationRedone(const OperationRecord &operation);
void undoRedoStateChanged(bool canUndo, bool canRedo);
```

#### 槽函数

```cpp
// 撤销/重做操作
void undo();
void redo();
void recordOperation(const OperationRecord &operation);
```

### RankingCalculator

#### 概述
排名计算器，实现国际田联跳高排名规则。

#### 公共方法

```cpp
// 构造函数
explicit RankingCalculator(QObject *parent = nullptr);

// 排名计算
RankingResult calculateRankings(Competition *competition);
RankingResult calculateRankings(const QList<Athlete*> &athletes,
                               const QHash<int, QHash<int, QHash<int, int>>> &attempts,
                               const QList<int> &heightProgression);

// 单个运动员分析
AthleteRanking calculateAthleteRanking(int athleteId,
                                      const QHash<int, QHash<int, int>> &attempts,
                                      const QList<int> &heightProgression);

// 统计方法
int getBestHeight(int athleteId, const QHash<int, QHash<int, int>> &attempts);
int countFailures(const QHash<int, QHash<int, int>> &attempts, int height = -1);
bool isAthleteRetired(const QHash<int, QHash<int, int>> &attempts);

// 比赛状态分析
bool isCompetitionFinished(const RankingResult &rankings, int currentHeight);
int getActiveAthleteCount(const RankingResult &rankings);
int getNextSuggestedHeight(const RankingResult &rankings,
                          const QList<int> &heightProgression,
                          int currentHeight);

// 配置选项
void setStrictRankingMode(bool strict);
void setMaxTiedRanks(int maxTies);
void setConsiderAttemptTime(bool considerTime);
```

#### 数据结构

```cpp
struct AthleteRanking {
    int athleteId;
    int rank;
    int bestHeight;
    int failuresAtBest;
    int totalFailures;
    int totalAttempts;
    bool isRetired;
    bool hasStarted;
    QDateTime lastAttemptTime;
};

struct RankingResult {
    QList<AthleteRanking> rankings;
    QDateTime calculationTime;
    int totalAthletes;
    int activeAthletes;
    int retiredAthletes;
    int completedAthletes;
    bool isCompetitionFinished;
};
```

### CompetitionState

#### 概述
比赛状态管理器，跟踪和管理比赛的整体状态。

#### 公共方法

```cpp
// 构造函数
explicit CompetitionState(QObject *parent = nullptr);

// 比赛管理
void loadCompetition(Competition *competition);
void startCompetition();
void pauseCompetition();
void resumeCompetition();
void finishCompetition();
void cancelCompetition();

// 状态查询
CompetitionPhase getCurrentPhase() const;
int getCurrentHeight() const;
int getCurrentRound() const;
QDateTime getStartTime() const;
qint64 getElapsedTime() const;
AthleteStatus getAthleteStatus(int athleteId) const;
int getActiveAthleteCount() const;

// 高度管理
void setCurrentHeight(int height);
bool advanceToNextHeight();
int getNextHeight() const;

// 运动员状态管理
void updateAthleteStatus(int athleteId, AthleteStatus status);
void updateAthleteBestHeight(int athleteId, int height);
void updateAthleteRank(int athleteId, int rank);

// 数据持久化
bool saveState();
bool loadState();
void setAutoSave(bool enabled, int intervalSeconds = 30);

// 状态快照
StateSnapshot createSnapshot() const;
void restoreFromSnapshot(const StateSnapshot &snapshot);
```

#### 枚举

```cpp
enum CompetitionPhase {
    NotStarted = 0,
    InProgress = 1,
    Paused = 2,
    Finished = 3,
    Cancelled = 4
};

enum AthleteStatus {
    Waiting = 0,
    Active = 1,
    Passed = 2,
    Failed = 3,
    Retired = 4,
    Finished = 5
};
```

## 使用示例

### 基本使用流程

```cpp
// 1. 创建主要组件
ScoringView *scoringView = new ScoringView(parent);
AthleteTableModel *model = scoringView->getAthleteTableModel();

// 2. 加载比赛数据
Competition *competition = loadCompetitionFromDatabase();
scoringView->loadCompetition(competition);

// 3. 记录试跳结果
model->recordAttempt(athleteId, height, attemptNumber, JumpAttempt::Pass);

// 4. 更新排名
model->updateRankings();
```

### 快捷键配置

```cpp
// 获取快捷键管理器
ShortcutManager *shortcutManager = new ShortcutManager(parent);
shortcutManager->setTableView(tableView);
shortcutManager->setTableModel(model);

// 自定义快捷键
shortcutManager->setCustomShortcut("success", QKeySequence("Ctrl+O"));
shortcutManager->setCustomShortcut("failure", QKeySequence("Ctrl+X"));
```

### 排名计算

```cpp
// 创建排名计算器
RankingCalculator *calculator = new RankingCalculator(parent);

// 计算排名
RankingCalculator::RankingResult result = calculator->calculateRankings(competition);

// 获取排名信息
for (const auto &ranking : result.rankings) {
    qDebug() << "Athlete" << ranking.athleteId 
             << "Rank:" << ranking.rank 
             << "Best:" << ranking.bestHeight;
}
```

## 注意事项

1. **线程安全**: AthleteTableModel和CompetitionState使用了互斥锁，可以在多线程环境中安全使用
2. **内存管理**: 所有组件都使用Qt的父子对象系统进行内存管理
3. **信号槽**: 组件间通过信号槽进行通信，确保松耦合
4. **性能**: 大规模数据场景下已进行性能优化
5. **扩展性**: API设计支持未来功能扩展

---

**文档版本**: 1.0  
**最后更新**: 2024-12-19  
**适用版本**: Story 1.3 Complete Implementation
