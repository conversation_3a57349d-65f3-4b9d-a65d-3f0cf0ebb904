#include <QtTest/QtTest>
#include <QApplication>
#include <QSplitter>
#include <QLabel>
#include <QTableWidget>
#include "ui/scoring_view.h"

class TestScoringView : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void init();
    void cleanup();
    void cleanupTestCase();
    
    // 界面初始化测试
    void testUIInitialization();
    void testComponentCreation();
    void testLayoutSetup();
    
    // 响应式布局测试
    void testResponsiveLayout();
    void testResizeHandling();
    
    // 占位符组件测试
    void testAthleteTablePlaceholder();
    void testRankingPanelPlaceholder();
    
    // 基本功能测试
    void testGetMethods();
    void testMinimumSize();

private:
    ScoringView *m_scoringView;
};

void TestScoringView::initTestCase()
{
    // 整个测试类开始前的初始化
}

void TestScoringView::init()
{
    // 每个测试方法开始前的初始化
    m_scoringView = new ScoringView();
}

void TestScoringView::cleanup()
{
    // 每个测试方法结束后的清理
    delete m_scoringView;
    m_scoringView = nullptr;
}

void TestScoringView::cleanupTestCase()
{
    // 整个测试类结束后的清理
}

void TestScoringView::testUIInitialization()
{
    QVERIFY(m_scoringView != nullptr);
    
    // 检查最小尺寸设置
    QVERIFY(m_scoringView->minimumSize().width() >= 800);
    QVERIFY(m_scoringView->minimumSize().height() >= 600);
    
    // 检查布局存在
    QVERIFY(m_scoringView->layout() != nullptr);
}

void TestScoringView::testComponentCreation()
{
    // 检查主要组件是否创建
    AthleteTableWidget *athleteTable = m_scoringView->getAthleteTable();
    RankingPanel *rankingPanel = m_scoringView->getRankingPanel();
    
    QVERIFY(athleteTable != nullptr);
    QVERIFY(rankingPanel != nullptr);
}

void TestScoringView::testLayoutSetup()
{
    // 查找主分割器
    QSplitter *splitter = m_scoringView->findChild<QSplitter*>();
    QVERIFY(splitter != nullptr);
    
    // 检查分割器配置
    QCOMPARE(splitter->count(), 2); // 应该有两个子组件
    QVERIFY(!splitter->childrenCollapsible()); // 不允许折叠
    QCOMPARE(splitter->handleWidth(), 4); // 分割线宽度
}

void TestScoringView::testResponsiveLayout()
{
    QSplitter *splitter = m_scoringView->findChild<QSplitter*>();
    QVERIFY(splitter != nullptr);
    
    // 测试大屏幕（水平布局）
    m_scoringView->resize(1200, 800);
    QApplication::processEvents();
    QCOMPARE(splitter->orientation(), Qt::Horizontal);
    
    // 测试小屏幕（垂直布局）
    m_scoringView->resize(600, 800);
    QApplication::processEvents();
    QCOMPARE(splitter->orientation(), Qt::Vertical);
}

void TestScoringView::testResizeHandling()
{
    QSplitter *splitter = m_scoringView->findChild<QSplitter*>();
    QVERIFY(splitter != nullptr);
    
    // 测试尺寸变化处理
    QSize initialSize = m_scoringView->size();
    m_scoringView->resize(1000, 700);
    
    // 确保布局调整被触发
    QApplication::processEvents();
    
    // 验证新尺寸
    QCOMPARE(m_scoringView->size(), QSize(1000, 700));
}

void TestScoringView::testAthleteTablePlaceholder()
{
    AthleteTableWidget *athleteTable = m_scoringView->getAthleteTable();
    QVERIFY(athleteTable != nullptr);
    
    // 检查表格基本设置
    QVERIFY(athleteTable->rowCount() > 0);
    QVERIFY(athleteTable->columnCount() > 0);
    
    // 检查表格属性
    QVERIFY(athleteTable->alternatingRowColors());
    QCOMPARE(athleteTable->selectionBehavior(), QAbstractItemView::SelectRows);
    QCOMPARE(athleteTable->selectionMode(), QAbstractItemView::SingleSelection);
    
    // 检查最小高度
    QVERIFY(athleteTable->minimumHeight() >= 300);
}

void TestScoringView::testRankingPanelPlaceholder()
{
    RankingPanel *rankingPanel = m_scoringView->getRankingPanel();
    QVERIFY(rankingPanel != nullptr);
    
    // 检查最小宽度
    QVERIFY(rankingPanel->minimumWidth() >= 250);
    
    // 查找标题标签
    QLabel *titleLabel = rankingPanel->findChild<QLabel*>();
    QVERIFY(titleLabel != nullptr);
    
    // 查找排名表格
    QTableWidget *rankingTable = rankingPanel->findChild<QTableWidget*>();
    QVERIFY(rankingTable != nullptr);
    
    // 检查排名表格基本设置
    QVERIFY(rankingTable->rowCount() > 0);
    QCOMPARE(rankingTable->columnCount(), 3); // 排名、姓名、最佳成绩
    
    // 检查表格属性
    QVERIFY(rankingTable->alternatingRowColors());
    QCOMPARE(rankingTable->selectionMode(), QAbstractItemView::NoSelection);
    QVERIFY(!rankingTable->verticalHeader()->isVisible());
}

void TestScoringView::testGetMethods()
{
    // 测试获取方法返回非空指针
    QVERIFY(m_scoringView->getAthleteTable() != nullptr);
    QVERIFY(m_scoringView->getRankingPanel() != nullptr);
    
    // 测试返回的指针一致性
    AthleteTableWidget *table1 = m_scoringView->getAthleteTable();
    AthleteTableWidget *table2 = m_scoringView->getAthleteTable();
    QCOMPARE(table1, table2);
    
    RankingPanel *panel1 = m_scoringView->getRankingPanel();
    RankingPanel *panel2 = m_scoringView->getRankingPanel();
    QCOMPARE(panel1, panel2);
}

void TestScoringView::testMinimumSize()
{
    // 测试最小尺寸约束
    m_scoringView->resize(400, 300); // 尝试设置小于最小尺寸
    
    // 最小尺寸应该被保持
    QVERIFY(m_scoringView->size().width() >= m_scoringView->minimumSize().width());
    QVERIFY(m_scoringView->size().height() >= m_scoringView->minimumSize().height());
}

QTEST_MAIN(TestScoringView)
#include "test_scoring_view.moc"