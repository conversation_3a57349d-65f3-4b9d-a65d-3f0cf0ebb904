# CMake generated Testfile for 
# Source directory: C:/PROJECT/HighJump/tests
# Build directory: C:/PROJECT/HighJump/build/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[DatabaseManagerTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_database_manager.exe")
  set_tests_properties([=[DatabaseManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;115;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[DatabaseManagerTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_database_manager.exe")
  set_tests_properties([=[DatabaseManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;115;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[DatabaseManagerTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_database_manager.exe")
  set_tests_properties([=[DatabaseManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;115;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[DatabaseManagerTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_database_manager.exe")
  set_tests_properties([=[DatabaseManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;115;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[DatabaseManagerTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[ConfigManagerTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_config_manager.exe")
  set_tests_properties([=[ConfigManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;116;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[ConfigManagerTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_config_manager.exe")
  set_tests_properties([=[ConfigManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;116;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[ConfigManagerTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_config_manager.exe")
  set_tests_properties([=[ConfigManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;116;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[ConfigManagerTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_config_manager.exe")
  set_tests_properties([=[ConfigManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;116;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[ConfigManagerTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[ApiClientTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_api_client.exe")
  set_tests_properties([=[ApiClientTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;117;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[ApiClientTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_api_client.exe")
  set_tests_properties([=[ApiClientTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;117;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[ApiClientTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_api_client.exe")
  set_tests_properties([=[ApiClientTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;117;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[ApiClientTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_api_client.exe")
  set_tests_properties([=[ApiClientTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;117;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[ApiClientTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[CompetitionSelectionViewTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_competition_selection_view.exe")
  set_tests_properties([=[CompetitionSelectionViewTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;118;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[CompetitionSelectionViewTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_competition_selection_view.exe")
  set_tests_properties([=[CompetitionSelectionViewTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;118;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[CompetitionSelectionViewTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_competition_selection_view.exe")
  set_tests_properties([=[CompetitionSelectionViewTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;118;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[CompetitionSelectionViewTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_competition_selection_view.exe")
  set_tests_properties([=[CompetitionSelectionViewTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;118;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[CompetitionSelectionViewTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[ScoringViewTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_scoring_view.exe")
  set_tests_properties([=[ScoringViewTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;119;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[ScoringViewTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_scoring_view.exe")
  set_tests_properties([=[ScoringViewTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;119;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[ScoringViewTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_scoring_view.exe")
  set_tests_properties([=[ScoringViewTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;119;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[ScoringViewTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_scoring_view.exe")
  set_tests_properties([=[ScoringViewTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;119;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[ScoringViewTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[E2ESimpleTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_e2e_simple.exe")
  set_tests_properties([=[E2ESimpleTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;158;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[E2ESimpleTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_e2e_simple.exe")
  set_tests_properties([=[E2ESimpleTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;158;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[E2ESimpleTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_e2e_simple.exe")
  set_tests_properties([=[E2ESimpleTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;158;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[E2ESimpleTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_e2e_simple.exe")
  set_tests_properties([=[E2ESimpleTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;158;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[E2ESimpleTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[ApplicationStartupTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_application_startup.exe")
  set_tests_properties([=[ApplicationStartupTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;212;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[ApplicationStartupTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_application_startup.exe")
  set_tests_properties([=[ApplicationStartupTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;212;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[ApplicationStartupTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_application_startup.exe")
  set_tests_properties([=[ApplicationStartupTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;212;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[ApplicationStartupTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_application_startup.exe")
  set_tests_properties([=[ApplicationStartupTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;212;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[ApplicationStartupTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[AthleteTableModelTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_athlete_table_model.exe")
  set_tests_properties([=[AthleteTableModelTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;253;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[AthleteTableModelTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_athlete_table_model.exe")
  set_tests_properties([=[AthleteTableModelTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;253;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[AthleteTableModelTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_athlete_table_model.exe")
  set_tests_properties([=[AthleteTableModelTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;253;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[AthleteTableModelTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_athlete_table_model.exe")
  set_tests_properties([=[AthleteTableModelTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;253;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[AthleteTableModelTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[AthleteDelegateTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_athlete_delegate.exe")
  set_tests_properties([=[AthleteDelegateTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;295;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[AthleteDelegateTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_athlete_delegate.exe")
  set_tests_properties([=[AthleteDelegateTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;295;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[AthleteDelegateTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_athlete_delegate.exe")
  set_tests_properties([=[AthleteDelegateTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;295;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[AthleteDelegateTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_athlete_delegate.exe")
  set_tests_properties([=[AthleteDelegateTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;295;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[AthleteDelegateTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[ShortcutManagerTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_shortcut_manager.exe")
  set_tests_properties([=[ShortcutManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;337;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[ShortcutManagerTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_shortcut_manager.exe")
  set_tests_properties([=[ShortcutManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;337;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[ShortcutManagerTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_shortcut_manager.exe")
  set_tests_properties([=[ShortcutManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;337;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[ShortcutManagerTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_shortcut_manager.exe")
  set_tests_properties([=[ShortcutManagerTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;337;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[ShortcutManagerTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[LargeScalePerformanceTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_large_scale_performance.exe")
  set_tests_properties([=[LargeScalePerformanceTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;379;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[LargeScalePerformanceTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_large_scale_performance.exe")
  set_tests_properties([=[LargeScalePerformanceTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;379;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[LargeScalePerformanceTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_large_scale_performance.exe")
  set_tests_properties([=[LargeScalePerformanceTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;379;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[LargeScalePerformanceTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_large_scale_performance.exe")
  set_tests_properties([=[LargeScalePerformanceTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;379;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[LargeScalePerformanceTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[UIUXUsabilityTest]=] "C:/PROJECT/HighJump/build/bin/Debug/test_ui_ux_usability.exe")
  set_tests_properties([=[UIUXUsabilityTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;380;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[UIUXUsabilityTest]=] "C:/PROJECT/HighJump/build/bin/Release/test_ui_ux_usability.exe")
  set_tests_properties([=[UIUXUsabilityTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;380;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[UIUXUsabilityTest]=] "C:/PROJECT/HighJump/build/bin/MinSizeRel/test_ui_ux_usability.exe")
  set_tests_properties([=[UIUXUsabilityTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;380;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[UIUXUsabilityTest]=] "C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_ui_ux_usability.exe")
  set_tests_properties([=[UIUXUsabilityTest]=] PROPERTIES  _BACKTRACE_TRIPLES "C:/PROJECT/HighJump/tests/CMakeLists.txt;380;add_test;C:/PROJECT/HighJump/tests/CMakeLists.txt;0;")
else()
  add_test([=[UIUXUsabilityTest]=] NOT_AVAILABLE)
endif()
