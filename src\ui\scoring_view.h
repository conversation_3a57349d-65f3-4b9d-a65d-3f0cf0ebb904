#ifndef SCORING_VIEW_H
#define SCORING_VIEW_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QLabel>
#include <QTableView>
#include <QToolBar>
#include <QStatusBar>
#include <QProgressBar>
#include <QPushButton>
#include <QGroupBox>
#include <QGridLayout>
#include <QTimer>

class AthleteTableModel;
class AthleteDelegate;
class ShortcutManager;
class Competition;

/**
 * @brief 主计分界面 - 跳高比赛核心计分系统
 *
 * 这是跳高比赛管理系统的核心界面，提供完整的计分功能包括：
 * - 运动员成绩表格显示和编辑
 * - 实时排名计算和显示
 * - 比赛状态监控和进度跟踪
 * - 键盘快捷键支持
 * - 响应式布局适配
 *
 * 主要组件：
 * - AthleteTableView: 使用AthleteTableModel和AthleteDelegate的专业计分表格
 * - CompetitionStatusPanel: 显示当前比赛状态、高度、轮次等信息
 * - ToolBar: 提供常用操作按钮
 * - StatusBar: 显示系统状态和进度信息
 */
class ScoringView : public QWidget
{
    Q_OBJECT

public:
    explicit ScoringView(QWidget *parent = nullptr);
    ~ScoringView();

    // Competition management
    /**
     * @brief 加载比赛数据
     * @param competition 比赛对象
     */
    void loadCompetition(Competition *competition);

    /**
     * @brief 获取当前比赛
     * @return 当前比赛对象或nullptr
     */
    Competition* getCurrentCompetition() const;

    /**
     * @brief 清除当前比赛数据
     */
    void clearCompetition();

    // UI component access
    /**
     * @brief 获取运动员表格视图
     * @return 表格视图指针
     */
    QTableView* getAthleteTableView() const;

    /**
     * @brief 获取运动员表格模型
     * @return 表格模型指针
     */
    AthleteTableModel* getAthleteTableModel() const;

    // Competition state
    /**
     * @brief 设置当前比赛高度
     * @param height 高度（厘米）
     */
    void setCurrentHeight(int height);

    /**
     * @brief 获取当前比赛高度
     * @return 当前高度（厘米）
     */
    int getCurrentHeight() const;

    /**
     * @brief 更新比赛进度显示
     */
    void updateCompetitionProgress();

signals:
    /**
     * @brief 试跳结果记录请求
     * @param athleteId 运动员ID
     * @param height 高度
     * @param attemptNumber 试跳次数
     * @param result 试跳结果
     */
    void attemptRecorded(int athleteId, int height, int attemptNumber, int result);

    /**
     * @brief 比赛状态变化
     * @param height 当前高度
     * @param activeAthletes 活跃运动员数量
     */
    void competitionStateChanged(int height, int activeAthletes);

    /**
     * @brief 比赛结束
     */
    void competitionFinished();

public slots:
    /**
     * @brief 处理试跳请求
     * @param athleteId 运动员ID
     * @param height 高度
     * @param attemptNumber 试跳次数
     * @param result 试跳结果
     */
    void onAttemptRequested(int athleteId, int height, int attemptNumber, int result);

    /**
     * @brief 处理排名更新
     */
    void onRankingsUpdated();

    /**
     * @brief 处理比赛进度更新
     */
    void onCompetitionProgressChanged();

    /**
     * @brief 切换到下一高度
     */
    void onNextHeight();

    /**
     * @brief 刷新显示
     */
    void refreshDisplay();

protected:
    /**
     * @brief 处理窗口大小变化事件
     * @param event 大小变化事件
     */
    void resizeEvent(QResizeEvent *event) override;

    /**
     * @brief 处理键盘事件
     * @param event 键盘事件
     */
    void keyPressEvent(QKeyEvent *event) override;

private slots:
    /**
     * @brief 状态更新定时器
     */
    void onStatusUpdateTimer();

private:
    // Setup methods
    void setupUI();
    void setupTableView();
    void setupStatusPanel();
    void setupToolBar();
    void setupStatusBar();
    void setupConnections();
    void setupLayout();

    // Helper methods
    void updateStatusDisplay();
    void updateProgressBar();
    void adjustLayoutForSize();
    bool isCompetitionActive() const;
    int getActiveAthleteCount() const;

    // Core components
    AthleteTableModel *m_tableModel;
    AthleteDelegate *m_tableDelegate;
    ShortcutManager *m_shortcutManager;
    QTableView *m_tableView;

    // Layout
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_contentLayout;
    QSplitter *m_mainSplitter;

    // Status panel
    QGroupBox *m_statusGroup;
    QGridLayout *m_statusLayout;
    QLabel *m_competitionNameLabel;
    QLabel *m_currentHeightLabel;
    QLabel *m_activeAthletesLabel;
    QLabel *m_completedAthletesLabel;
    QProgressBar *m_progressBar;
    QPushButton *m_nextHeightButton;

    // Tool bar
    QToolBar *m_toolBar;
    QPushButton *m_refreshButton;
    QPushButton *m_exportButton;
    QPushButton *m_settingsButton;

    // Status bar
    QStatusBar *m_statusBar;
    QLabel *m_statusLabel;
    QLabel *m_timeLabel;

    // Data
    Competition *m_competition;
    int m_currentHeight;

    // Timers
    QTimer *m_statusUpdateTimer;
    QTimer *m_timeUpdateTimer;
};

#endif // SCORING_VIEW_H