#include "main_window.h"
#include "athlete_dialog.h"
#include "competition_selection_view.h"
#include "view_manager.h"
#include "../models/competition.h"
#include "../models/athlete.h"
#include "../models/jump_attempt.h"
#include "../core/jump_manager.h"
#include "../api/api_client.h"

#include <QApplication>
#include <QCloseEvent>
#include <QMessageBox>
#include <QFileDialog>
#include <QTimer>
#include <QHeaderView>
#include <QGroupBox>
#include <QGridLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QDebug>
#include <QFile>
#include <QTextStream>
#include <QStringConverter>
#include <QPrinter>
#include <QPrintDialog>
#include <QTextDocument>
#include <QInputDialog>
#include <QStandardPaths>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDate>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_hasUnsavedChanges(false)
    , m_currentFilePath("")
{
    setupUI();
    setupMenus();
    setupToolbars();
    setupStatusBar();
    setupConnections();
    
    updateWindowTitle();
    updateStatusBar();

    // Start with competition selection view
    // Delay API call to ensure all connections are established
    QTimer::singleShot(100, this, &MainWindow::fetchCompetitions);
}

MainWindow::~MainWindow()
{
    // Smart pointers handle cleanup automatically
}

void MainWindow::setupUI()
{
    setMinimumSize(1200, 800);
    resize(1400, 900);
    
    // Create main stacked widget for view management
    m_centralStack.reset(new QStackedWidget(this));
    setCentralWidget(m_centralStack.get());
    
    // Initialize ViewManager
    m_viewManager.reset(new ViewManager(m_centralStack.get(), this));
    
    // Create competition selection view
    m_selectionView.reset(new CompetitionSelectionView(this));
    m_viewManager->registerView(ViewManager::CompetitionSelection, m_selectionView.get());
    
    // Create scoring view (keep existing layout)
    setupScoringView();
    m_viewManager->registerView(ViewManager::MainScoring, m_scoringView.get());
    
    // Start with competition selection view
    m_viewManager->switchToView(ViewManager::CompetitionSelection);
}

void MainWindow::setupScoringView()
{
    // Create scoring view widget
    m_scoringView.reset(new QWidget());
    
    // Main horizontal splitter for scoring view
    m_mainSplitter.reset(new QSplitter(Qt::Horizontal, m_scoringView.get()));
    
    // Left panel - Athletes
    setupAthletePanel();
    
    // Center panel - Competition control
    setupCompetitionPanel();
    
    // Right panel - Results
    setupResultsPanel();
    
    m_mainSplitter->addWidget(m_athletePanel.get());
    m_mainSplitter->addWidget(m_competitionPanel.get());
    m_mainSplitter->addWidget(m_resultsPanel.get());
    
    // Set splitter sizes (30%, 40%, 30%)
    m_mainSplitter->setSizes({300, 400, 300});
    
    QHBoxLayout *layout = new QHBoxLayout(m_scoringView.get());
    layout->addWidget(m_mainSplitter.get());
    layout->setContentsMargins(5, 5, 5, 5);
}

void MainWindow::setupAthletePanel()
{
    m_athletePanel.reset(new QWidget());
    m_athletePanel->setMaximumWidth(350);
    
    QVBoxLayout *layout = new QVBoxLayout(m_athletePanel.get());
    
    // Title
    QLabel *title = new QLabel("Athletes");
    title->setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;");
    layout->addWidget(title);
    
    // Athlete list
    m_athleteList.reset(new QListWidget());
    m_athleteList->setAlternatingRowColors(true);
    layout->addWidget(m_athleteList.get());
    
    // Buttons
    QHBoxLayout *btnLayout = new QHBoxLayout();
    m_addAthleteBtn.reset(new QPushButton("Add"));
    m_editAthleteBtn.reset(new QPushButton("Edit"));
    m_removeAthleteBtn.reset(new QPushButton("Remove"));
    
    btnLayout->addWidget(m_addAthleteBtn.get());
    btnLayout->addWidget(m_editAthleteBtn.get());
    btnLayout->addWidget(m_removeAthleteBtn.get());
    layout->addLayout(btnLayout);
}

void MainWindow::setupCompetitionPanel()
{
    m_competitionPanel.reset(new QWidget());
    
    QVBoxLayout *layout = new QVBoxLayout(m_competitionPanel.get());
    
    // Competition title
    QLabel *title = new QLabel("Competition Control");
    title->setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;");
    layout->addWidget(title);
    
    // Current athlete
    QGroupBox *athleteGroup = new QGroupBox("Current Athlete");
    QVBoxLayout *athleteLayout = new QVBoxLayout(athleteGroup);
    
    m_currentAthleteLabel.reset(new QLabel("No athlete selected"));
    m_currentAthleteLabel->setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;");
    m_currentAthleteLabel->setAlignment(Qt::AlignCenter);
    athleteLayout->addWidget(m_currentAthleteLabel.get());
    
    layout->addWidget(athleteGroup);
    
    // Height control
    QGroupBox *heightGroup = new QGroupBox("Height Setting");
    QGridLayout *heightLayout = new QGridLayout(heightGroup);
    
    heightLayout->addWidget(new QLabel("Current Height:"), 0, 0);
    m_currentHeightLabel.reset(new QLabel("0 cm"));
    m_currentHeightLabel->setStyleSheet("font-weight: bold; color: blue;");
    heightLayout->addWidget(m_currentHeightLabel.get(), 0, 1);
    
    heightLayout->addWidget(new QLabel("Set Height:"), 1, 0);
    m_heightSpinBox.reset(new QSpinBox());
    m_heightSpinBox->setRange(100, 300);
    m_heightSpinBox->setSuffix(" cm");
    m_heightSpinBox->setValue(150);
    heightLayout->addWidget(m_heightSpinBox.get(), 1, 1);
    
    layout->addWidget(heightGroup);
    
    // Jump recording
    QGroupBox *jumpGroup = new QGroupBox("Record Jump");
    QGridLayout *jumpLayout = new QGridLayout(jumpGroup);
    
    m_passBtn.reset(new QPushButton("PASS (O)"));
    m_passBtn->setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 10px;");
    jumpLayout->addWidget(m_passBtn.get(), 0, 0);
    
    m_failBtn.reset(new QPushButton("FAIL (X)"));
    m_failBtn->setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 10px;");
    jumpLayout->addWidget(m_failBtn.get(), 0, 1);
    
    m_skipBtn.reset(new QPushButton("SKIP (-)"));
    m_skipBtn->setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 10px;");
    jumpLayout->addWidget(m_skipBtn.get(), 0, 2);
    
    m_attemptCountLabel.reset(new QLabel("Attempt: 1/3"));
    m_attemptCountLabel->setAlignment(Qt::AlignCenter);
    jumpLayout->addWidget(m_attemptCountLabel.get(), 1, 0, 1, 3);
    
    layout->addWidget(jumpGroup);
    
    // Notes
    QGroupBox *notesGroup = new QGroupBox("Notes");
    QVBoxLayout *notesLayout = new QVBoxLayout(notesGroup);
    
    m_notesEdit.reset(new QTextEdit());
    m_notesEdit->setMaximumHeight(100);
    m_notesEdit->setPlaceholderText("Add notes about the current attempt...");
    notesLayout->addWidget(m_notesEdit.get());
    
    layout->addWidget(notesGroup);
    
    layout->addStretch();
}

void MainWindow::setupResultsPanel()
{
    m_resultsPanel.reset(new QWidget());
    m_resultsPanel->setMaximumWidth(400);
    
    QVBoxLayout *layout = new QVBoxLayout(m_resultsPanel.get());
    
    // Title
    QLabel *title = new QLabel("Results");
    title->setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;");
    layout->addWidget(title);
    
    // Competition status
    m_competitionStatusLabel.reset(new QLabel("Competition not started"));
    m_competitionStatusLabel->setStyleSheet("padding: 5px; border: 1px solid gray; background-color: #f0f0f0;");
    layout->addWidget(m_competitionStatusLabel.get());
    
    // Results table
    m_resultsTable.reset(new QTableWidget());
    m_resultsTable->setAlternatingRowColors(true);
    m_resultsTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_resultsTable->horizontalHeader()->setStretchLastSection(true);
    layout->addWidget(m_resultsTable.get());
}

void MainWindow::setupMenus()
{
    // File Menu
    m_fileMenu.reset(menuBar()->addMenu("&File"));
    
    m_newAction.reset(new QAction("&New Competition", this));
    m_newAction->setShortcut(QKeySequence::New);
    m_newAction->setIcon(style()->standardIcon(QStyle::SP_FileIcon));
    m_fileMenu->addAction(m_newAction.get());
    
    m_openAction.reset(new QAction("&Open Competition", this));
    m_openAction->setShortcut(QKeySequence::Open);
    m_openAction->setIcon(style()->standardIcon(QStyle::SP_DirOpenIcon));
    m_fileMenu->addAction(m_openAction.get());
    
    m_fileMenu->addSeparator();
    
    m_saveAction.reset(new QAction("&Save", this));
    m_saveAction->setShortcut(QKeySequence::Save);
    m_saveAction->setIcon(style()->standardIcon(QStyle::SP_DialogSaveButton));
    m_fileMenu->addAction(m_saveAction.get());
    
    m_saveAsAction.reset(new QAction("Save &As...", this));
    m_saveAsAction->setShortcut(QKeySequence::SaveAs);
    m_fileMenu->addAction(m_saveAsAction.get());
    
    m_fileMenu->addSeparator();
    
    m_exportAction.reset(new QAction("&Export Results...", this));
    m_exportAction->setIcon(style()->standardIcon(QStyle::SP_DialogSaveButton));
    m_fileMenu->addAction(m_exportAction.get());
    
    m_printAction.reset(new QAction("&Print Results...", this));
    m_printAction->setShortcut(QKeySequence::Print);
    m_printAction->setIcon(style()->standardIcon(QStyle::SP_DialogSaveButton));
    m_fileMenu->addAction(m_printAction.get());
    
    m_fileMenu->addSeparator();
    
    m_exitAction.reset(new QAction("E&xit", this));
    m_exitAction->setShortcut(QKeySequence::Quit);
    m_fileMenu->addAction(m_exitAction.get());
    
    // Athletes Menu
    m_athletesMenu.reset(menuBar()->addMenu("&Athletes"));
    
    m_addAthleteAction.reset(new QAction("&Add Athlete...", this));
    m_addAthleteAction->setShortcut(QKeySequence("Ctrl+A"));
    m_athletesMenu->addAction(m_addAthleteAction.get());
    
    m_editAthleteAction.reset(new QAction("&Edit Athlete...", this));
    m_editAthleteAction->setShortcut(QKeySequence("Ctrl+E"));
    m_athletesMenu->addAction(m_editAthleteAction.get());
    
    m_removeAthleteAction.reset(new QAction("&Remove Athlete", this));
    m_removeAthleteAction->setShortcut(QKeySequence::Delete);
    m_athletesMenu->addAction(m_removeAthleteAction.get());
    
    m_athletesMenu->addSeparator();
    
    m_clearResultsAction.reset(new QAction("&Clear All Results", this));
    m_athletesMenu->addAction(m_clearResultsAction.get());
    
    // Competition Menu
    m_competitionMenu.reset(menuBar()->addMenu("&Competition"));
    
    m_startCompetitionAction.reset(new QAction("&Start Competition", this));
    m_startCompetitionAction->setShortcut(QKeySequence("F5"));
    m_competitionMenu->addAction(m_startCompetitionAction.get());
    
    m_pauseCompetitionAction.reset(new QAction("&Pause Competition", this));
    m_pauseCompetitionAction->setShortcut(QKeySequence("F6"));
    m_competitionMenu->addAction(m_pauseCompetitionAction.get());
    
    m_endCompetitionAction.reset(new QAction("&End Competition", this));
    m_endCompetitionAction->setShortcut(QKeySequence("F7"));
    m_competitionMenu->addAction(m_endCompetitionAction.get());
    
    m_competitionMenu->addSeparator();
    
    m_nextAthleteAction.reset(new QAction("&Next Athlete", this));
    m_nextAthleteAction->setShortcut(QKeySequence("Ctrl+Right"));
    m_competitionMenu->addAction(m_nextAthleteAction.get());
    
    m_previousAthleteAction.reset(new QAction("&Previous Athlete", this));
    m_previousAthleteAction->setShortcut(QKeySequence("Ctrl+Left"));
    m_competitionMenu->addAction(m_previousAthleteAction.get());
    
    // Height Menu
    m_heightMenu.reset(menuBar()->addMenu("&Height"));
    
    m_height150Action.reset(new QAction("150 cm", this));
    m_height150Action->setShortcut(QKeySequence("Ctrl+1"));
    m_heightMenu->addAction(m_height150Action.get());
    
    m_height153Action.reset(new QAction("153 cm", this));
    m_height153Action->setShortcut(QKeySequence("Ctrl+2"));
    m_heightMenu->addAction(m_height153Action.get());
    
    m_height156Action.reset(new QAction("156 cm", this));
    m_height156Action->setShortcut(QKeySequence("Ctrl+3"));
    m_heightMenu->addAction(m_height156Action.get());
    
    m_height159Action.reset(new QAction("159 cm", this));
    m_height159Action->setShortcut(QKeySequence("Ctrl+4"));
    m_heightMenu->addAction(m_height159Action.get());
    
    m_heightMenu->addSeparator();
    
    m_customHeightAction.reset(new QAction("Custom Height...", this));
    m_customHeightAction->setShortcut(QKeySequence("Ctrl+H"));
    m_heightMenu->addAction(m_customHeightAction.get());
    
    m_advanceHeightAction.reset(new QAction("Advance to Next Height", this));
    m_advanceHeightAction->setShortcut(QKeySequence("Ctrl+Up"));
    m_heightMenu->addAction(m_advanceHeightAction.get());
    
    // Help Menu
    m_helpMenu.reset(menuBar()->addMenu("&Help"));
    
    m_rulesAction.reset(new QAction("Competition &Rules", this));
    m_rulesAction->setShortcut(QKeySequence::HelpContents);
    m_helpMenu->addAction(m_rulesAction.get());
    
    m_helpAction.reset(new QAction("User &Guide", this));
    m_helpMenu->addAction(m_helpAction.get());
    
    m_helpMenu->addSeparator();
    
    m_aboutAction.reset(new QAction("&About", this));
    m_helpMenu->addAction(m_aboutAction.get());
}

void MainWindow::setupToolbars()
{
    // Main toolbar
    m_mainToolBar.reset(addToolBar("Main"));
    m_mainToolBar->addAction(m_newAction.get());
    m_mainToolBar->addAction(m_openAction.get());
    m_mainToolBar->addAction(m_saveAction.get());
    m_mainToolBar->addSeparator();
    m_mainToolBar->addAction(m_addAthleteAction.get());
    m_mainToolBar->addAction(m_editAthleteAction.get());
    m_mainToolBar->addAction(m_removeAthleteAction.get());
    
    // Competition toolbar
    m_competitionToolBar.reset(addToolBar("Competition"));
    m_competitionToolBar->addAction(m_startCompetitionAction.get());
    m_competitionToolBar->addAction(m_pauseCompetitionAction.get());
    m_competitionToolBar->addAction(m_endCompetitionAction.get());
    m_competitionToolBar->addSeparator();
    m_competitionToolBar->addAction(m_previousAthleteAction.get());
    m_competitionToolBar->addAction(m_nextAthleteAction.get());
    
    // Height toolbar
    m_heightToolBar.reset(addToolBar("Heights"));
    m_heightToolBar->addAction(m_height150Action.get());
    m_heightToolBar->addAction(m_height153Action.get());
    m_heightToolBar->addAction(m_height156Action.get());
    m_heightToolBar->addAction(m_height159Action.get());
    m_heightToolBar->addSeparator();
    m_heightToolBar->addAction(m_advanceHeightAction.get());
}

void MainWindow::setupStatusBar()
{
    m_statusLabel.reset(new QLabel("Ready"));
    statusBar()->addWidget(m_statusLabel.get());
    
    statusBar()->addPermanentWidget(new QLabel("|"));
    
    m_athleteCountLabel.reset(new QLabel("Athletes: 0"));
    statusBar()->addPermanentWidget(m_athleteCountLabel.get());
    
    statusBar()->addPermanentWidget(new QLabel("|"));
    
    m_timeLabel.reset(new QLabel());
    statusBar()->addPermanentWidget(m_timeLabel.get());
    
    // Update time every second
    QTimer *timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, [this]() {
        m_timeLabel->setText(QDateTime::currentDateTime().toString("hh:mm:ss"));
    });
    timer->start(1000);
    m_timeLabel->setText(QDateTime::currentDateTime().toString("hh:mm:ss"));
}

void MainWindow::setupConnections()
{
    // Competition selection connections
    connect(m_selectionView.get(), &CompetitionSelectionView::competitionSelected,
            this, &MainWindow::onCompetitionSelected);
    connect(m_selectionView.get(), &CompetitionSelectionView::selectionCancelled,
            this, &MainWindow::onCompetitionSelectionCancelled);
    
    // API client connections
    connect(ApiClient::instance(), &ApiClient::competitionsReceived,
            this, &MainWindow::onCompetitionsReceived);
    connect(ApiClient::instance(), &ApiClient::networkError,
            this, &MainWindow::onNetworkError);
    connect(ApiClient::instance(), &ApiClient::networkStatusChanged,
            this, &MainWindow::onNetworkStatusChanged);

    // Competition selection view connections
    connect(m_selectionView.get(), &CompetitionSelectionView::retryRequested,
            this, &MainWindow::onRetryRequested);
    
    // File menu connections
    connect(m_newAction.get(), &QAction::triggered, this, &MainWindow::newCompetition);
    connect(m_openAction.get(), &QAction::triggered, this, &MainWindow::openCompetition);
    connect(m_saveAction.get(), &QAction::triggered, this, &MainWindow::saveCompetition);
    connect(m_saveAsAction.get(), &QAction::triggered, this, &MainWindow::saveCompetitionAs);
    connect(m_exportAction.get(), &QAction::triggered, this, &MainWindow::exportResults);
    connect(m_printAction.get(), &QAction::triggered, this, &MainWindow::printResults);
    connect(m_exitAction.get(), &QAction::triggered, this, &MainWindow::exitApplication);
    
    // Athletes menu connections
    connect(m_addAthleteAction.get(), &QAction::triggered, this, &MainWindow::addAthlete);
    connect(m_editAthleteAction.get(), &QAction::triggered, this, &MainWindow::editAthlete);
    connect(m_removeAthleteAction.get(), &QAction::triggered, this, &MainWindow::removeAthlete);
    connect(m_clearResultsAction.get(), &QAction::triggered, this, &MainWindow::clearResults);
    
    // Competition menu connections
    connect(m_startCompetitionAction.get(), &QAction::triggered, this, &MainWindow::startCompetition);
    connect(m_pauseCompetitionAction.get(), &QAction::triggered, this, &MainWindow::pauseCompetition);
    connect(m_endCompetitionAction.get(), &QAction::triggered, this, &MainWindow::endCompetition);
    connect(m_nextAthleteAction.get(), &QAction::triggered, this, &MainWindow::nextAthlete);
    connect(m_previousAthleteAction.get(), &QAction::triggered, this, &MainWindow::previousAthlete);
    
    // Height menu connections
    connect(m_height150Action.get(), &QAction::triggered, this, &MainWindow::setHeight150);
    connect(m_height153Action.get(), &QAction::triggered, this, &MainWindow::setHeight153);
    connect(m_height156Action.get(), &QAction::triggered, this, &MainWindow::setHeight156);
    connect(m_height159Action.get(), &QAction::triggered, this, &MainWindow::setHeight159);
    connect(m_customHeightAction.get(), &QAction::triggered, this, &MainWindow::setCustomHeight);
    connect(m_advanceHeightAction.get(), &QAction::triggered, this, &MainWindow::advanceHeight);
    
    // Help menu connections
    connect(m_aboutAction.get(), &QAction::triggered, this, &MainWindow::showAbout);
    connect(m_rulesAction.get(), &QAction::triggered, this, &MainWindow::showRules);
    connect(m_helpAction.get(), &QAction::triggered, this, &MainWindow::showHelp);
    
    // Button connections
    connect(m_addAthleteBtn.get(), &QPushButton::clicked, this, &MainWindow::addAthlete);
    connect(m_editAthleteBtn.get(), &QPushButton::clicked, this, &MainWindow::editAthlete);
    connect(m_removeAthleteBtn.get(), &QPushButton::clicked, this, &MainWindow::removeAthlete);
    
    connect(m_passBtn.get(), &QPushButton::clicked, this, &MainWindow::recordPass);
    connect(m_failBtn.get(), &QPushButton::clicked, this, &MainWindow::recordFail);
    connect(m_skipBtn.get(), &QPushButton::clicked, this, &MainWindow::recordSkip);
    
    // Widget connections
    connect(m_athleteList, &QListWidget::itemSelectionChanged, this, &MainWindow::onAthleteSelectionChanged);
    connect(m_heightSpinBox.get(), QOverload<int>::of(&QSpinBox::valueChanged), this, &MainWindow::onHeightChanged);
    
    // Jump manager connections
    JumpManager *jumpManager = JumpManager::instance();
    connect(jumpManager, &JumpManager::jumpRecorded, this, &MainWindow::updateResultsTable);
    connect(jumpManager, &JumpManager::athleteEliminated, this, [this](Athlete *athlete, Competition *competition) {
        if (competition == m_currentCompetition) {
            updateResultsTable();
            updateCurrentDisplay();
            QMessageBox::information(this, "Athlete Eliminated", 
                QString("Athlete %1 has been eliminated after 3 consecutive failures.").arg(athlete->displayName()));
        }
    });
    connect(jumpManager, &JumpManager::heightCompleted, this, [this](int height, Competition *competition) {
        if (competition == m_currentCompetition) {
            updateResultsTable();
            QMessageBox::information(this, "Height Completed", 
                QString("All athletes have completed height %1 cm.").arg(height));
        }
    });
}

// Action implementations (placeholder for now)
void MainWindow::newCompetition()
{
    m_currentCompetition.reset(new Competition(this));
    m_currentCompetition->setName("New Competition");
    m_currentCompetition->setDate(QDateTime::currentDateTime());
    m_currentCompetition->setVenue("Competition Venue");
    
    // Set default height progression
    QList<int> defaultHeights = {150, 153, 156, 159, 162, 165, 168, 171, 174, 177, 180, 183, 186, 189, 192, 195, 198, 201, 204, 207, 210};
    m_currentCompetition->setHeightProgression(defaultHeights);
    
    updateWindowTitle();
    updateAthleteList();
    updateResultsTable();
    updateCurrentDisplay();
    
    m_statusLabel->setText(tr("New competition created"));
}

void MainWindow::openCompetition() 
{ 
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "Open Competition",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        "Competition Files (*.json);;All Files (*)"
    );
    
    if (fileName.isEmpty()) {
        return;
    }
    
    QFile file(fileName);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::critical(this, "Open Error", 
            QString("Could not open file '%1' for reading.").arg(fileName));
        return;
    }
    
    QByteArray data = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);
    
    if (doc.isNull()) {
        QMessageBox::critical(this, "Parse Error", 
            QString("Could not parse file '%1' as JSON.").arg(fileName));
        return;
    }
    
    QJsonObject root = doc.object();
    
    // Create new competition
    m_currentCompetition.reset(new Competition(this));
    
    // Load competition data
    m_currentCompetition->setName(root["name"].toString());
    m_currentCompetition->setDate(QDateTime::fromString(root["date"].toString(), Qt::ISODate));
    m_currentCompetition->setVenue(root["venue"].toString());
    m_currentCompetition->setStatus(static_cast<Competition::Status>(root["status"].toInt()));
    
    // Load height progression
    QJsonArray heightArray = root["heightProgression"].toArray();
    QList<int> heights;
    for (const QJsonValue &value : heightArray) {
        heights.append(value.toInt());
    }
    m_currentCompetition->setHeightProgression(heights);
    
    // Load athletes
    QJsonArray athletesArray = root["athletes"].toArray();
    for (const QJsonValue &value : athletesArray) {
        QJsonObject athleteObj = value.toObject();
        
        Athlete *athlete = new Athlete(this);
        athlete->setStartNumber(athleteObj["startNumber"].toInt());
        athlete->setFirstName(athleteObj["firstName"].toString());
        athlete->setLastName(athleteObj["lastName"].toString());
        athlete->setCountry(athleteObj["country"].toString());
        athlete->setClub(athleteObj["club"].toString());
        athlete->setPersonalBest(athleteObj["personalBest"].toInt());
        athlete->setSeasonBest(athleteObj["seasonBest"].toInt());
        athlete->setDateOfBirth(QDate::fromString(athleteObj["dateOfBirth"].toString(), Qt::ISODate));
        athlete->setNotes(athleteObj["notes"].toString());
        
        m_currentCompetition->addAthlete(athlete);
    }
    
    // Load jump attempts
    JumpManager *jumpManager = JumpManager::instance();
    QJsonArray jumpsArray = root["jumpAttempts"].toArray();
    for (const QJsonValue &value : jumpsArray) {
        QJsonObject jumpObj = value.toObject();
        
        int athleteStartNumber = jumpObj["athleteStartNumber"].toInt();
        Athlete *athlete = nullptr;
        
        // Find athlete by start number
        for (Athlete *a : m_currentCompetition->athletes()) {
            if (a->startNumber() == athleteStartNumber) {
                athlete = a;
                break;
            }
        }
        
        if (athlete) {
            JumpAttempt *jump = new JumpAttempt(this);
            jump->setAthlete(athlete);
            jump->setCompetition(m_currentCompetition);
            jump->setHeight(jumpObj["height"].toInt());
            jump->setAttemptNumber(jumpObj["attemptNumber"].toInt());
            jump->setResult(static_cast<JumpAttempt::Result>(jumpObj["result"].toInt()));
            jump->setNotes(jumpObj["notes"].toString());
            jump->setTimestamp(QDateTime::fromString(jumpObj["timestamp"].toString(), Qt::ISODate));
            
            jumpManager->addJumpAttempt(jump);
        }
    }
    
    file.close();
    
    m_currentFilePath = fileName;
    updateWindowTitle();
    updateAthleteList();
    updateResultsTable();
    updateCurrentDisplay();
    
    m_statusLabel->setText(QString("Competition loaded from: %1").arg(fileName));
}

void MainWindow::saveCompetition() 
{ 
    if (!m_currentCompetition) {
        QMessageBox::information(this, "No Competition", "No competition to save.");
        return;
    }
    
    if (m_currentFilePath.isEmpty()) {
        saveCompetitionAs();
        return;
    }
    
    saveCompetitionToFile(m_currentFilePath);
}

void MainWindow::saveCompetitionAs() 
{ 
    if (!m_currentCompetition) {
        QMessageBox::information(this, "No Competition", "No competition to save.");
        return;
    }
    
    QString fileName = QFileDialog::getSaveFileName(
        this,
        "Save Competition As",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/" + m_currentCompetition->name() + ".json",
        "Competition Files (*.json);;All Files (*)"
    );
    
    if (fileName.isEmpty()) {
        return;
    }
    
    if (saveCompetitionToFile(fileName)) {
        m_currentFilePath = fileName;
        updateWindowTitle();
    }
}

bool MainWindow::saveCompetitionToFile(const QString &fileName)
{
    QJsonObject root;
    
    // Save competition data
    root["name"] = m_currentCompetition->name();
    root["date"] = m_currentCompetition->date().toString(Qt::ISODate);
    root["venue"] = m_currentCompetition->venue();
    root["status"] = static_cast<int>(m_currentCompetition->status());
    
    // Save height progression
    QJsonArray heightArray;
    for (int height : m_currentCompetition->heightProgression()) {
        heightArray.append(height);
    }
    root["heightProgression"] = heightArray;
    
    // Save athletes
    QJsonArray athletesArray;
    for (Athlete *athlete : m_currentCompetition->athletes()) {
        QJsonObject athleteObj;
        athleteObj["startNumber"] = athlete->startNumber();
        athleteObj["firstName"] = athlete->firstName();
        athleteObj["lastName"] = athlete->lastName();
        athleteObj["country"] = athlete->country();
        athleteObj["club"] = athlete->club();
        athleteObj["personalBest"] = athlete->personalBest();
        athleteObj["seasonBest"] = athlete->seasonBest();
        athleteObj["dateOfBirth"] = athlete->dateOfBirth().toString(Qt::ISODate);
        athleteObj["notes"] = athlete->notes();
        
        athletesArray.append(athleteObj);
    }
    root["athletes"] = athletesArray;
    
    // Save jump attempts
    JumpManager *jumpManager = JumpManager::instance();
    QJsonArray jumpsArray;
    
    for (Athlete *athlete : m_currentCompetition->athletes()) {
        QList<JumpAttempt*> jumps = jumpManager->getAthleteJumps(athlete, m_currentCompetition);
        for (JumpAttempt *jump : jumps) {
            QJsonObject jumpObj;
            jumpObj["athleteStartNumber"] = athlete->startNumber();
            jumpObj["height"] = jump->height();
            jumpObj["attemptNumber"] = jump->attemptNumber();
            jumpObj["result"] = static_cast<int>(jump->result());
            jumpObj["notes"] = jump->notes();
            jumpObj["timestamp"] = jump->timestamp().toString(Qt::ISODate);
            
            jumpsArray.append(jumpObj);
        }
    }
    root["jumpAttempts"] = jumpsArray;
    
    // Write to file
    QJsonDocument doc(root);
    QFile file(fileName);
    
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::critical(this, "Save Error", 
            QString("Could not open file '%1' for writing.").arg(fileName));
        return false;
    }
    
    file.write(doc.toJson(QJsonDocument::Indented));
    file.close();
    
    m_hasUnsavedChanges = false;
    m_statusLabel->setText(QString("Competition saved to: %1").arg(fileName));
    return true;
}

void MainWindow::exportResults() 
{ 
    if (!m_currentCompetition) {
        QMessageBox::information(this, "No Competition", "No competition to export results from.");
        return;
    }
    
    QString fileName = QFileDialog::getSaveFileName(
        this,
        "Export Competition Results",
        QString("%1_results.csv").arg(m_currentCompetition->name()),
        "CSV Files (*.csv);;All Files (*)"
    );
    
    if (fileName.isEmpty()) {
        return;
    }
    
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::critical(this, "Export Error", 
            QString("Could not open file '%1' for writing.").arg(fileName));
        return;
    }
    
    QTextStream out(&file);
    out.setEncoding(QStringConverter::Utf8);
    
    // Write header
    out << "Competition Results\n";
    out << QString("Competition: %1\n").arg(m_currentCompetition->name());
    out << QString("Date: %1\n").arg(m_currentCompetition->date().toString("yyyy-MM-dd"));
    out << QString("Venue: %1\n").arg(m_currentCompetition->venue());
    out << QString("Status: %1\n").arg(m_currentCompetition->statusString());
    out << "\n";
    
    // Write athlete results header
    out << "Start Number,Name,Country,Club,Personal Best,Season Best,Highest Cleared,Total Failures,Status\n";
    
    JumpManager *jumpManager = JumpManager::instance();
    const QList<Athlete*> &athletes = m_currentCompetition->athletes();
    
    for (Athlete *athlete : athletes) {
        int highestCleared = jumpManager->getAthleteHighestClearedHeight(athlete, m_currentCompetition.get());
        int totalFailures = jumpManager->getAthleteTotalFailures(athlete, m_currentCompetition.get());
        bool isEliminated = jumpManager->isAthleteEliminated(athlete, m_currentCompetition.get());
        
        QString status = isEliminated ? "Eliminated" : "Active";
        if (highestCleared > 0) {
            status = QString("Cleared %1cm").arg(highestCleared);
        }
        
        // Escape commas in text fields
        QString name = athlete->displayName().replace(",", ";");
        QString country = athlete->country().replace(",", ";");
        QString club = athlete->club().replace(",", ";");
        
        out << QString("%1,%2,%3,%4,%5,%6,%7,%8,%9\n")
            .arg(athlete->startNumber())
            .arg(name)
            .arg(country)
            .arg(club)
            .arg(athlete->personalBest())
            .arg(athlete->seasonBest())
            .arg(highestCleared)
            .arg(totalFailures)
            .arg(status);
    }
    
    out << "\n";
    
    // Write detailed jump attempts
    out << "Detailed Jump Attempts\n";
    out << "Start Number,Name,Height,Attempt,Result,Notes\n";
    
    for (Athlete *athlete : athletes) {
        QList<JumpAttempt*> jumps = jumpManager->getAthleteJumps(athlete, m_currentCompetition.get());
        for (JumpAttempt *jump : jumps) {
            QString name = athlete->displayName().replace(",", ";");
            QString notes = jump->notes().replace(",", ";");
            QString result;
            
            switch (jump->result()) {
                case JumpAttempt::Pass:
                    result = "Pass";
                    break;
                case JumpAttempt::Fail:
                    result = "Fail";
                    break;
                case JumpAttempt::Skip:
                    result = "Skip";
                    break;
            }
            
            out << QString("%1,%2,%3,%4,%5,%6\n")
                .arg(athlete->startNumber())
                .arg(name)
                .arg(jump->height())
                .arg(jump->attemptNumber())
                .arg(result)
                .arg(notes);
        }
    }
    
    file.close();
    
    m_statusLabel->setText(QString("Results exported to: %1").arg(fileName));
    QMessageBox::information(this, "Export Successful", 
        QString("Competition results have been exported to:\n%1").arg(fileName));
}

void MainWindow::printResults() 
{ 
    if (!m_currentCompetition) {
        QMessageBox::information(this, "No Competition", "No competition to print results from.");
        return;
    }
    
    QPrinter printer(QPrinter::HighResolution);
    QPrintDialog dialog(&printer, this);
    
    if (dialog.exec() != QDialog::Accepted) {
        return;
    }
    
    QTextDocument document;
    QString html = generateResultsHtml();
    document.setHtml(html);
    
    document.print(&printer);
    
    m_statusLabel->setText("Results printed successfully");
}

void MainWindow::exitApplication() 
{ 
    close(); 
}

void MainWindow::addAthlete() 
{ 
    AthleteDialog dialog(this);
    if (dialog.exec() == QDialog::Accepted) {
        Athlete *athlete = dialog.getAthlete();
        if (athlete && m_currentCompetition) {
            // Check for duplicate start number
            if (m_currentCompetition->findAthleteByStartNumber(athlete->startNumber())) {
                QMessageBox::warning(this, "Duplicate Start Number", 
                    QString("An athlete with start number %1 already exists.").arg(athlete->startNumber()));
                delete athlete;
                return;
            }
            
            m_currentCompetition->addAthlete(athlete);
            updateAthleteList();
            m_hasUnsavedChanges = true;
            updateWindowTitle();
            m_statusLabel->setText(QString("Added athlete: %1").arg(athlete->displayName()));
        }
    }
}

void MainWindow::editAthlete() 
{ 
    if (!m_currentAthlete) {
        QMessageBox::information(this, "No Selection", "Please select an athlete to edit.");
        return;
    }
    
    AthleteDialog dialog(m_currentAthlete.get(), this);
    if (dialog.exec() == QDialog::Accepted) {
        updateAthleteList();
        updateCurrentDisplay();
        m_hasUnsavedChanges = true;
        updateWindowTitle();
        m_statusLabel->setText(QString("Updated athlete: %1").arg(m_currentAthlete->displayName()));
    }
}

void MainWindow::removeAthlete() 
{ 
    if (!m_currentAthlete) {
        QMessageBox::information(this, "No Selection", "Please select an athlete to remove.");
        return;
    }
    
    QMessageBox::StandardButton result = QMessageBox::question(
        this, "Confirm Removal",
        QString("Are you sure you want to remove athlete '%1'?").arg(m_currentAthlete->displayName()),
        QMessageBox::Yes | QMessageBox::No);
        
    if (result == QMessageBox::Yes && m_currentCompetition) {
        m_currentCompetition->removeAthlete(m_currentAthlete.get());
        m_currentAthlete.reset();
        updateAthleteList();
        updateCurrentDisplay();
        m_hasUnsavedChanges = true;
        updateWindowTitle();
        m_statusLabel->setText("Athlete removed");
    }
}

void MainWindow::clearResults() 
{ 
    if (!m_currentCompetition) {
        QMessageBox::information(this, "No Competition", "No competition to clear results from.");
        return;
    }
    
    QMessageBox::StandardButton result = QMessageBox::question(
        this, "Confirm Clear Results",
        "Are you sure you want to clear all jump results for this competition? This action cannot be undone.",
        QMessageBox::Yes | QMessageBox::No);
        
    if (result == QMessageBox::Yes) {
        JumpManager *jumpManager = JumpManager::instance();
        jumpManager->clearCompetitionData(m_currentCompetition);
        updateResultsTable();
        updateCurrentDisplay();
        m_hasUnsavedChanges = true;
        updateWindowTitle();
        m_statusLabel->setText("All results cleared");
    }
}

void MainWindow::startCompetition()
{
    if (m_currentCompetition && m_currentCompetition->canStart()) {
        m_currentCompetition->startCompetition();
        m_statusLabel->setText(tr("Competition started"));
        updateCurrentDisplay();
    } else {
        QMessageBox::warning(this, "Cannot Start", "Competition cannot be started. Check that you have athletes and height progression set.");
    }
}

void MainWindow::pauseCompetition() 
{ 
    if (m_currentCompetition && m_currentCompetition->status() == Competition::InProgress) {
        m_currentCompetition->pauseCompetition();
        m_statusLabel->setText(tr("Competition paused"));
        updateCurrentDisplay();
    } else {
        QMessageBox::information(this, "Cannot Pause", "Competition is not in progress.");
    }
}

void MainWindow::endCompetition() 
{ 
    if (m_currentCompetition) {
        m_currentCompetition->endCompetition();
        m_statusLabel->setText(tr("Competition ended"));
        updateCurrentDisplay();
    }
}

void MainWindow::nextAthlete() 
{ 
    if (!m_currentCompetition || m_currentCompetition->athletes().isEmpty()) {
        return;
    }
    
    const QList<Athlete*> &athletes = m_currentCompetition->athletes();
    int currentIndex = athletes.indexOf(m_currentAthlete.get());
    
    if (currentIndex >= 0 && currentIndex < athletes.size() - 1) {
        m_currentAthlete = athletes[currentIndex + 1];
    } else if (currentIndex == -1 || currentIndex == athletes.size() - 1) {
        // Wrap around to first athlete
        m_currentAthlete = athletes.first();
    }
    
    // Update selection in list
    for (int i = 0; i < m_athleteList->count(); ++i) {
        QListWidgetItem *item = m_athleteList->item(i);
        Athlete *athlete = item->data(Qt::UserRole).value<Athlete*>();
        if (athlete == m_currentAthlete.get()) {
            m_athleteList->setCurrentRow(i);
            break;
        }
    }
    
    updateCurrentDisplay();
    m_statusLabel->setText(QString("Selected: %1").arg(m_currentAthlete->displayName()));
}

void MainWindow::previousAthlete() 
{ 
    if (!m_currentCompetition || m_currentCompetition->athletes().isEmpty()) {
        return;
    }
    
    const QList<Athlete*> &athletes = m_currentCompetition->athletes();
    int currentIndex = athletes.indexOf(m_currentAthlete.get());
    
    if (currentIndex > 0) {
        m_currentAthlete = athletes[currentIndex - 1];
    } else if (currentIndex == 0 || currentIndex == -1) {
        // Wrap around to last athlete
        m_currentAthlete = athletes.last();
    }
    
    // Update selection in list
    for (int i = 0; i < m_athleteList->count(); ++i) {
        QListWidgetItem *item = m_athleteList->item(i);
        Athlete *athlete = item->data(Qt::UserRole).value<Athlete*>();
        if (athlete == m_currentAthlete.get()) {
            m_athleteList->setCurrentRow(i);
            break;
        }
    }
    
    updateCurrentDisplay();
    m_statusLabel->setText(QString("Selected: %1").arg(m_currentAthlete->displayName()));
}

void MainWindow::setHeight150() 
{ 
    m_heightSpinBox->setValue(150);
    onHeightChanged();
}

void MainWindow::setHeight153() 
{ 
    m_heightSpinBox->setValue(153);
    onHeightChanged();
}

void MainWindow::setHeight156() 
{ 
    m_heightSpinBox->setValue(156);
    onHeightChanged();
}

void MainWindow::setHeight159() 
{ 
    m_heightSpinBox->setValue(159);
    onHeightChanged();
}

void MainWindow::setCustomHeight() 
{ 
    bool ok;
    int height = QInputDialog::getInt(this, "Set Custom Height", 
        "Enter height in centimeters:", m_heightSpinBox->value(), 100, 300, 1, &ok);
    
    if (ok) {
        m_heightSpinBox->setValue(height);
        onHeightChanged();
        m_statusLabel->setText(QString("Set custom height: %1 cm").arg(height));
    }
}

void MainWindow::advanceHeight()
{
    if (m_currentCompetition && m_currentCompetition->canAdvanceToNextHeight()) {
        m_currentCompetition->advanceToNextHeight();
        m_heightSpinBox->setValue(m_currentCompetition->currentHeight());
        updateCurrentDisplay();
        m_statusLabel->setText(QString("Advanced to height: %1 cm").arg(m_currentCompetition->currentHeight()));
    }
}

void MainWindow::showAbout()
{
    QMessageBox::about(this, "About High Jump Scorer", 
        "High Jump Competition Management System\n\n"
        "Version 1.0.0\n"
        "Built with Qt 6 and C++17\n\n"
        "A professional system for managing high jump competitions "
        "according to World Athletics rules.");
}

void MainWindow::showRules()
{
    QString rulesText = 
        "High Jump Competition Rules (World Athletics):\n\n"
        "1. Bar and Standards: Bar length 3.98-4.02m, weight 1.8-2.0kg. "
        "Standards distance 4.00-4.04m apart, adjustable height.\n\n"
        "2. Runway: Minimum 15m length (20m preferred indoors), flat surface.\n\n"
        "3. Attempts: Maximum 3 attempts per height. "
        "3 consecutive failures (across different heights) results in elimination.\n\n"
        "4. Bar Height: Competition heights set by officials with athletes. "
        "Starting height typically 90% of entrants' bests. "
        "Increments usually 3-5cm, adjustable as needed.\n\n"
        "5. Failures: Bar displacement, body/equipment touching ground beyond bar plane, "
        "takeoff with both feet, or returning to runway after takeoff.\n\n"
        "6. Ranking: By highest cleared height, then fewer failures at that height, "
        "then fewer total failures. Jump-off for ties (except shared 1st place).\n\n"
        "7. Time Limits: 1 minute per attempt (3+ athletes), "
        "1.5 minutes (2 athletes), 3 minutes (1 athlete). "
        "Additional 2 minutes for record attempts.\n\n"
        "8. Equipment: Shoe sole thickness ≤20mm, spike length ≤9mm. "
        "Support bands and knee supports allowed.\n\n"
        "9. Records: Single successful attempt meeting World Athletics standards required.";
    
    QMessageBox::information(this, "Competition Rules", rulesText);
}

void MainWindow::showHelp() 
{ 
    QString helpText = 
        "High Jump Competition Management System - User Guide\n\n"
        "Getting Started:\n"
        "1. Create a new competition or open an existing one\n"
        "2. Add athletes using the 'Add Athlete' button\n"
        "3. Set the starting height using the height controls\n"
        "4. Start the competition\n\n"
        "During Competition:\n"
        "1. Select an athlete from the list\n"
        "2. Record their jump result (Pass/Fail/Skip)\n"
        "3. Use the height controls to advance to the next height\n"
        "4. Monitor results in the results table\n\n"
        "Key Features:\n"
        "- Automatic elimination tracking (3 consecutive failures)\n"
        "- Real-time results display\n"
        "- Export results to CSV for Excel\n"
        "- Print competition reports\n"
        "- World Athletics rules compliance\n\n"
        "Keyboard Shortcuts:\n"
        "- F5: Start Competition\n"
        "- F6: Pause Competition\n"
        "- F7: End Competition\n"
        "- Ctrl+Up: Advance to next height\n"
        "- Ctrl+N: New Competition\n"
        "- Ctrl+O: Open Competition\n"
        "- Ctrl+S: Save Competition\n"
        "- Ctrl+E: Export Results\n\n"
        "For detailed competition rules, see 'Rules' in the Help menu.";
    
    QMessageBox::information(this, "Help", helpText);
}

void MainWindow::recordPass() 
{ 
    if (!m_currentAthlete || !m_currentCompetition) {
        QMessageBox::warning(this, "No Selection", "Please select an athlete and ensure competition is active.");
        return;
    }
    
    JumpManager *jumpManager = JumpManager::instance();
    int currentHeight = m_currentCompetition->currentHeight();
    QString notes = m_notesEdit->toPlainText();
    
    if (jumpManager->recordJump(m_currentAthlete.get(), m_currentCompetition.get(), currentHeight, JumpAttempt::Pass, notes)) {
        m_notesEdit->clear();
        updateResultsTable();
        updateCurrentDisplay();
        m_hasUnsavedChanges = true;
        updateWindowTitle();
        m_statusLabel->setText(QString("Recorded PASS for %1 at %2 cm").arg(m_currentAthlete->displayName()).arg(currentHeight));
    } else {
        QMessageBox::warning(this, "Recording Failed", "Failed to record jump. Please check if the athlete can still jump at this height.");
    }
}

void MainWindow::recordFail() 
{ 
    if (!m_currentAthlete || !m_currentCompetition) {
        QMessageBox::warning(this, "No Selection", "Please select an athlete and ensure competition is active.");
        return;
    }
    
    JumpManager *jumpManager = JumpManager::instance();
    int currentHeight = m_currentCompetition->currentHeight();
    QString notes = m_notesEdit->toPlainText();
    
    if (jumpManager->recordJump(m_currentAthlete.get(), m_currentCompetition.get(), currentHeight, JumpAttempt::Fail, notes)) {
        m_notesEdit->clear();
        updateResultsTable();
        updateCurrentDisplay();
        m_hasUnsavedChanges = true;
        updateWindowTitle();
        m_statusLabel->setText(QString("Recorded FAIL for %1 at %2 cm").arg(m_currentAthlete->displayName()).arg(currentHeight));
    } else {
        QMessageBox::warning(this, "Recording Failed", "Failed to record jump. Please check if the athlete can still jump at this height.");
    }
}

void MainWindow::recordSkip() 
{ 
    if (!m_currentAthlete || !m_currentCompetition) {
        QMessageBox::warning(this, "No Selection", "Please select an athlete and ensure competition is active.");
        return;
    }
    
    JumpManager *jumpManager = JumpManager::instance();
    int currentHeight = m_currentCompetition->currentHeight();
    QString notes = m_notesEdit->toPlainText();
    
    if (jumpManager->recordJump(m_currentAthlete.get(), m_currentCompetition.get(), currentHeight, JumpAttempt::Skip, notes)) {
        m_notesEdit->clear();
        updateResultsTable();
        updateCurrentDisplay();
        m_hasUnsavedChanges = true;
        updateWindowTitle();
        m_statusLabel->setText(QString("Recorded SKIP for %1 at %2 cm").arg(m_currentAthlete->displayName()).arg(currentHeight));
    } else {
        QMessageBox::warning(this, "Recording Failed", "Failed to record jump. Please check if the athlete can still jump at this height.");
    }
}

void MainWindow::onAthleteSelectionChanged() 
{ 
    updateCurrentAthlete(); 
}

void MainWindow::onHeightChanged()
{
    if (m_currentCompetition) {
        m_currentCompetition->setCurrentHeight(m_heightSpinBox->value());
        updateCurrentDisplay();
    }
}

void MainWindow::updateCurrentAthlete() 
{ 
    QListWidgetItem *currentItem = m_athleteList->currentItem();
    if (currentItem) {
        m_currentAthlete = currentItem->data(Qt::UserRole).value<Athlete*>();
        updateCurrentDisplay();
    } else {
        m_currentAthlete.reset();
        updateCurrentDisplay();
    }
}

void MainWindow::updateWindowTitle()
{
    QString title = "High Jump Scorer";
    if (m_currentCompetition && !m_currentCompetition->name().isEmpty()) {
        title += " - " + m_currentCompetition->name();
    }
    if (m_hasUnsavedChanges) {
        title += " *";
    }
    setWindowTitle(title);
}

void MainWindow::updateStatusBar()
{
    if (m_currentCompetition) {
        m_athleteCountLabel->setText(QString("Athletes: %1").arg(m_currentCompetition->athleteCount()));
    } else {
        m_athleteCountLabel->setText("Athletes: 0");
    }
}

void MainWindow::updateAthleteList()
{
    m_athleteList->clear();
    
    if (m_currentCompetition) {
        const auto athletes = m_currentCompetition->athletes();
        for (Athlete *athlete : athletes) {
            if (athlete) {
                QListWidgetItem *item = new QListWidgetItem(athlete->displayName());
                item->setData(Qt::UserRole, QVariant::fromValue(athlete));
                m_athleteList->addItem(item);
            }
        }
    }
    
    updateStatusBar();
}

void MainWindow::updateResultsTable()
{
    if (!m_currentCompetition) {
        m_resultsTable->clear();
        return;
    }
    
    JumpManager *jumpManager = JumpManager::instance();
    const auto athletes = m_currentCompetition->athletes();
    
    // Set up table headers
    m_resultsTable->setColumnCount(6);
    m_resultsTable->setHorizontalHeaderLabels({"Athlete", "Start #", "Highest Cleared", "Total Failures", "Status", "Current Height"});
    
    m_resultsTable->setRowCount(athletes.size());
    
    for (int i = 0; i < athletes.size(); ++i) {
        Athlete *athlete = athletes[i];
        if (!athlete) continue;
        
        // Athlete name
        QTableWidgetItem *nameItem = new QTableWidgetItem(athlete->displayName());
        nameItem->setFlags(nameItem->flags() & ~Qt::ItemIsEditable);
        m_resultsTable->setItem(i, 0, nameItem);
        
        // Start number
        QTableWidgetItem *startItem = new QTableWidgetItem(QString::number(athlete->startNumber()));
        startItem->setFlags(startItem->flags() & ~Qt::ItemIsEditable);
        m_resultsTable->setItem(i, 1, startItem);
        
        // Highest cleared height
        int highestCleared = jumpManager->getAthleteHighestClearedHeight(athlete, m_currentCompetition.get());
        QTableWidgetItem *heightItem = new QTableWidgetItem(highestCleared > 0 ? QString("%1 cm").arg(highestCleared) : "-");
        heightItem->setFlags(heightItem->flags() & ~Qt::ItemIsEditable);
        m_resultsTable->setItem(i, 2, heightItem);
        
        // Total failures
        int totalFailures = jumpManager->getAthleteTotalFailures(athlete, m_currentCompetition.get());
        QTableWidgetItem *failuresItem = new QTableWidgetItem(QString::number(totalFailures));
        failuresItem->setFlags(failuresItem->flags() & ~Qt::ItemIsEditable);
        m_resultsTable->setItem(i, 3, failuresItem);
        
        // Status
        bool isEliminated = jumpManager->isAthleteEliminated(athlete, m_currentCompetition.get());
        QString status = isEliminated ? "Eliminated" : "Active";
        QTableWidgetItem *statusItem = new QTableWidgetItem(status);
        statusItem->setFlags(statusItem->flags() & ~Qt::ItemIsEditable);
        if (isEliminated) {
            statusItem->setBackground(QColor(255, 200, 200)); // Light red for eliminated
        }
        m_resultsTable->setItem(i, 4, statusItem);
        
        // Current height attempts
        int currentHeight = m_currentCompetition->currentHeight();
        int attemptsAtCurrent = jumpManager->getAthleteAttemptsAtHeight(athlete, m_currentCompetition.get(), currentHeight);
        QString currentHeightText = attemptsAtCurrent > 0 ? QString("%1 cm (%2/3)").arg(currentHeight).arg(attemptsAtCurrent) : "-";
        QTableWidgetItem *currentItem = new QTableWidgetItem(currentHeightText);
        currentItem->setFlags(currentItem->flags() & ~Qt::ItemIsEditable);
        m_resultsTable->setItem(i, 5, currentItem);
    }
    
    // Resize columns to content
    m_resultsTable->resizeColumnsToContents();
}

void MainWindow::updateCurrentDisplay()
{
    if (m_currentCompetition) {
        m_currentHeightLabel->setText(QString("%1 cm").arg(m_currentCompetition->currentHeight()));
        m_competitionStatusLabel->setText(QString("Status: %1").arg(m_currentCompetition->statusString()));
        
        if (m_currentCompetition->status() == Competition::InProgress) {
            m_heightSpinBox->setValue(m_currentCompetition->currentHeight());
        }
    } else {
        m_currentHeightLabel->setText("0 cm");
        m_competitionStatusLabel->setText("No competition");
    }
    
    // Update current athlete display
    if (m_currentAthlete) {
        m_currentAthleteLabel->setText(m_currentAthlete->displayName());
        
        // Update attempt count
        JumpManager *jumpManager = JumpManager::instance();
        int currentHeight = m_currentCompetition ? m_currentCompetition->currentHeight() : 0;
        int attempts = jumpManager->getAthleteAttemptsAtHeight(m_currentAthlete.get(), m_currentCompetition.get(), currentHeight);
        m_attemptCountLabel->setText(QString("Attempt: %1/3").arg(attempts + 1));
        
        // Enable/disable buttons based on competition status and athlete state
        bool canRecord = m_currentCompetition && 
                        m_currentCompetition->status() == Competition::InProgress &&
                        !jumpManager->isAthleteEliminated(m_currentAthlete.get(), m_currentCompetition.get()) &&
                        attempts < 3;
        
        m_passBtn->setEnabled(canRecord);
        m_failBtn->setEnabled(canRecord);
        m_skipBtn->setEnabled(canRecord);
        
        // Show elimination status
        if (jumpManager->isAthleteEliminated(m_currentAthlete.get(), m_currentCompetition.get())) {
            m_currentAthleteLabel->setStyleSheet("color: red; font-weight: bold;");
        } else {
            m_currentAthleteLabel->setStyleSheet("");
        }
    } else {
        m_currentAthleteLabel->setText(tr("No athlete selected"));
        m_attemptCountLabel->setText(tr("Attempt: 1/3"));
        
        // Disable recording buttons
        m_passBtn->setEnabled(false);
        m_failBtn->setEnabled(false);
        m_skipBtn->setEnabled(false);
    }
}

bool MainWindow::saveChanges()
{
    // Will be implemented when save functionality is complete
    return true;
}

QString MainWindow::generateResultsHtml()
{
    if (!m_currentCompetition) {
        return "<html><body><h1>No competition data available</h1></body></html>";
    }
    
    QString html = "<html><head><style>";
    html += "body { font-family: Arial, sans-serif; margin: 20px; }";
    html += "h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
    html += "h2 { color: #34495e; margin-top: 30px; }";
    html += "table { border-collapse: collapse; width: 100%; margin-top: 20px; }";
    html += "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }";
    html += "th { background-color: #3498db; color: white; font-weight: bold; }";
    html += "tr:nth-child(even) { background-color: #f2f2f2; }";
    html += ".eliminated { background-color: #ffebee; color: #c62828; }";
    html += ".header { background-color: #ecf0f1; padding: 10px; margin-bottom: 20px; }";
    html += "</style></head><body>";
    
    // Competition header
    html += "<div class='header'>";
    html += QString("<h1>%1</h1>").arg(m_currentCompetition->name());
    html += QString("<p><strong>Date:</strong> %1</p>").arg(m_currentCompetition->date().toString("yyyy-MM-dd"));
    html += QString("<p><strong>Venue:</strong> %1</p>").arg(m_currentCompetition->venue());
    html += QString("<p><strong>Status:</strong> %1</p>").arg(m_currentCompetition->statusString());
    html += "</div>";
    
    // Athlete results table
    html += "<h2>Athlete Results</h2>";
    html += "<table>";
    html += "<tr><th>Start #</th><th>Name</th><th>Country</th><th>Club</th><th>PB</th><th>SB</th><th>Highest Cleared</th><th>Total Failures</th><th>Status</th></tr>";
    
    JumpManager *jumpManager = JumpManager::instance();
    const QList<Athlete*> &athletes = m_currentCompetition->athletes();
    
    for (Athlete *athlete : athletes) {
        int highestCleared = jumpManager->getAthleteHighestClearedHeight(athlete, m_currentCompetition.get());
        int totalFailures = jumpManager->getAthleteTotalFailures(athlete, m_currentCompetition.get());
        bool isEliminated = jumpManager->isAthleteEliminated(athlete, m_currentCompetition.get());
        
        QString status = isEliminated ? "Eliminated" : "Active";
        if (highestCleared > 0) {
            status = QString("Cleared %1cm").arg(highestCleared);
        }
        
        QString rowClass = isEliminated ? "class='eliminated'" : "";
        
        html += QString("<tr %1>").arg(rowClass);
        html += QString("<td>%1</td>").arg(athlete->startNumber());
        html += QString("<td>%1</td>").arg(athlete->displayName().toHtmlEscaped());
        html += QString("<td>%1</td>").arg(athlete->country().toHtmlEscaped());
        html += QString("<td>%1</td>").arg(athlete->club().toHtmlEscaped());
        html += QString("<td>%1</td>").arg(athlete->personalBest());
        html += QString("<td>%1</td>").arg(athlete->seasonBest());
        html += QString("<td>%1</td>").arg(highestCleared > 0 ? QString::number(highestCleared) : "-");
        html += QString("<td>%1</td>").arg(totalFailures);
        html += QString("<td>%1</td>").arg(status);
        html += "</tr>";
    }
    
    html += "</table>";
    
    // Detailed jump attempts
    html += "<h2>Detailed Jump Attempts</h2>";
    html += "<table>";
    html += "<tr><th>Start #</th><th>Name</th><th>Height</th><th>Attempt</th><th>Result</th><th>Notes</th></tr>";
    
    for (Athlete *athlete : athletes) {
        QList<JumpAttempt*> jumps = jumpManager->getAthleteJumps(athlete, m_currentCompetition.get());
        for (JumpAttempt *jump : jumps) {
            QString result;
            switch (jump->result()) {
                case JumpAttempt::Pass:
                    result = "Pass";
                    break;
                case JumpAttempt::Fail:
                    result = "Fail";
                    break;
                case JumpAttempt::Skip:
                    result = "Skip";
                    break;
            }
            
            html += "<tr>";
            html += QString("<td>%1</td>").arg(athlete->startNumber());
            html += QString("<td>%1</td>").arg(athlete->displayName().toHtmlEscaped());
            html += QString("<td>%1</td>").arg(jump->height());
            html += QString("<td>%1</td>").arg(jump->attemptNumber());
            html += QString("<td>%1</td>").arg(result);
            html += QString("<td>%1</td>").arg(jump->notes().toHtmlEscaped());
            html += "</tr>";
        }
    }
    
    html += "</table>";
    html += "</body></html>";
    
    return html;
}

bool MainWindow::confirmExit()
{
    if (m_hasUnsavedChanges) {
        QMessageBox::StandardButton result = QMessageBox::question(
            this, "Unsaved Changes",
            "You have unsaved changes. Do you want to save before exiting?",
            QMessageBox::Save | QMessageBox::Discard | QMessageBox::Cancel);
            
        if (result == QMessageBox::Save) {
            return saveChanges();
        } else if (result == QMessageBox::Cancel) {
            return false;
        }
    }
    return true;
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    if (confirmExit()) {
        event->accept();
    } else {
        event->ignore();
    }
}

void MainWindow::onCompetitionSelected(int competitionId)
{
    qDebug() << "MainWindow::onCompetitionSelected: Competition ID" << competitionId;

    // Find the selected competition from the cached data
    QJsonObject selectedCompetition = findCompetitionById(competitionId);
    if (selectedCompetition.isEmpty()) {
        qWarning() << "MainWindow::onCompetitionSelected: Competition not found with ID" << competitionId;
        QMessageBox::warning(this, tr("错误"), tr("无法找到选中的比赛数据。"));
        return;
    }

    // Load competition data
    if (!loadCompetitionData(selectedCompetition)) {
        qWarning() << "MainWindow::onCompetitionSelected: Failed to load competition data";
        QMessageBox::warning(this, tr("错误"), tr("加载比赛数据失败。"));
        return;
    }

    // Switch to main scoring view
    m_viewManager->switchToView(ViewManager::MainScoring);

    updateWindowTitle();
    updateStatusBar();

    qDebug() << "MainWindow::onCompetitionSelected: Successfully loaded competition" << selectedCompetition["name"].toString();
}

void MainWindow::onCompetitionSelectionCancelled()
{
    qDebug() << "MainWindow::onCompetitionSelectionCancelled: User cancelled competition selection";
    
    // For now, exit the application when user cancels selection
    QApplication::quit();
}

void MainWindow::fetchCompetitions()
{
    qDebug() << "MainWindow::fetchCompetitions: Starting competition fetch";

    // Show loading state in selection view
    m_selectionView->showLoadingState(true, tr("正在获取比赛列表..."));

    // Trigger API call to get competitions
    ApiClient::instance()->fetchCompetitions();
}

void MainWindow::onCompetitionsReceived(const QJsonArray &competitions)
{
    qDebug() << "MainWindow::onCompetitionsReceived: Received" << competitions.size() << "competitions";

    // Cache the competitions data
    m_cachedCompetitions = competitions;

    // Hide loading state
    m_selectionView->showLoadingState(false);

    // Load competitions into selection view
    m_selectionView->loadCompetitions(competitions);

    // Update status bar
    updateStatusBar();
}

void MainWindow::onNetworkError(const QString &errorMessage)
{
    qWarning() << "MainWindow::onNetworkError:" << errorMessage;

    // Hide loading state and show error
    m_selectionView->showLoadingState(false);

    // Check if we're offline
    if (!ApiClient::instance()->isOnline()) {
        m_selectionView->showOfflineMode();

        // Try to load cached competitions from database
        // TODO: Implement offline competition loading from database
        qDebug() << "MainWindow::onNetworkError: Should load cached competitions from database";
    } else {
        // Show network error with retry option
        QString userFriendlyMessage = tr("无法获取比赛列表：%1\n\n请检查网络连接后重试。").arg(errorMessage);
        m_selectionView->showError(userFriendlyMessage, true);
    }

    updateStatusBar();
}

void MainWindow::onNetworkStatusChanged(bool isOnline)
{
    qDebug() << "MainWindow::onNetworkStatusChanged: Network is" << (isOnline ? "online" : "offline");

    if (isOnline) {
        // Network came back online, automatically retry fetching competitions
        // if we're currently on the selection view and have an error state
        if (m_viewManager->currentViewType() == ViewManager::CompetitionSelection) {
            fetchCompetitions();
        }
    }

    updateStatusBar();
}

void MainWindow::onRetryRequested()
{
    qDebug() << "MainWindow::onRetryRequested: User requested retry";
    fetchCompetitions();
}

QJsonObject MainWindow::findCompetitionById(int competitionId) const
{
    for (const QJsonValue &value : m_cachedCompetitions) {
        QJsonObject competition = value.toObject();
        if (competition.value("id").toInt() == competitionId) {
            return competition;
        }
    }
    return QJsonObject();
}

bool MainWindow::loadCompetitionData(const QJsonObject &competitionData)
{
    try {
        qDebug() << "MainWindow::loadCompetitionData: Loading competition" << competitionData["name"].toString();

        // Create new competition object
        m_currentCompetition.reset(new Competition(this));

        // Set basic competition properties
        m_currentCompetition->setId(competitionData.value("id").toInt());
        m_currentCompetition->setName(competitionData.value("name").toString());
        m_currentCompetition->setVenue(competitionData.value("venue").toString());

        // Parse and set date
        QString dateStr = competitionData.value("date").toString();
        QDateTime dateTime = QDateTime::fromString(dateStr, Qt::ISODate);
        if (dateTime.isValid()) {
            m_currentCompetition->setDate(dateTime);
        }

        // Parse and set status
        QString statusStr = competitionData.value("status").toString();
        Competition::CompetitionStatus status = Competition::NotStarted;
        if (statusStr == "in_progress") {
            status = Competition::InProgress;
        } else if (statusStr == "finished") {
            status = Competition::Completed;
        } else if (statusStr == "cancelled") {
            status = Competition::Cancelled;
        }
        m_currentCompetition->setStatus(status);

        // Set height information if available
        int startingHeight = competitionData.value("starting_height").toInt(150);
        int heightIncrement = competitionData.value("height_increment").toInt(3);

        // For now, create a basic height progression
        // TODO: This should be loaded from the competition data or calculated
        QList<int> heightProgression;
        for (int height = startingHeight; height <= startingHeight + 50; height += heightIncrement) {
            heightProgression.append(height);
        }
        m_currentCompetition->setHeightProgression(heightProgression);

        // TODO: Load athletes and jump attempts from API
        // This would require additional API endpoints for detailed competition data

        qDebug() << "MainWindow::loadCompetitionData: Successfully loaded competition"
                 << m_currentCompetition->name() << "with" << heightProgression.size() << "heights";

        return true;

    } catch (const std::exception &e) {
        qCritical() << "MainWindow::loadCompetitionData: Exception occurred:" << e.what();
        return false;
    } catch (...) {
        qCritical() << "MainWindow::loadCompetitionData: Unknown exception occurred";
        return false;
    }
}