#ifndef SHORTCUT_MANAGER_H
#define SHORTCUT_MANAGER_H

#include <QObject>
#include <QShortcut>
#include <QKeySequence>
#include <QWidget>
#include <QHash>
#include <QStack>
#include <QTimer>

class AthleteTableModel;
class QTableView;

/**
 * @brief 键盘快捷键管理器 - 跳高比赛计分系统
 * 
 * 这个类负责管理跳高比赛计分界面的所有键盘快捷键和交互逻辑。
 * 提供专业的键盘操作体验，提高计分员的工作效率。
 * 
 * 主要功能：
 * - 试跳结果录入快捷键 (O, X, -, R)
 * - 撤销/重做功能 (Ctrl+Z, Ctrl+Y)
 * - 表格导航 (方向键, Tab, Shift+Tab)
 * - 确认/取消操作 (Enter, Esc)
 * - 上下文相关的快捷键管理
 * - 操作历史记录和撤销栈
 * 
 * 快捷键映射：
 * - O: 记录成功试跳
 * - X: 记录失败试跳
 * - -: 记录免跳
 * - R: 记录退赛
 * - Ctrl+Z: 撤销上一次操作
 * - Ctrl+Y: 重做操作
 * - Tab/Shift+Tab: 切换选中单元格
 * - 方向键: 导航表格
 * - Enter: 确认当前操作
 * - Esc: 取消当前操作
 */
class ShortcutManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 操作类型枚举
     */
    enum OperationType {
        AttemptRecord = 0,  // 试跳记录操作
        Navigation = 1,     // 导航操作
        Edit = 2,          // 编辑操作
        System = 3         // 系统操作
    };

    /**
     * @brief 试跳结果类型
     */
    enum AttemptResult {
        Success = 0,  // O - 成功
        Failure = 1,  // X - 失败
        Skip = 2,     // - - 免跳
        Retire = 3    // R - 退赛
    };

    /**
     * @brief 操作历史记录结构
     */
    struct OperationRecord {
        OperationType type;
        int athleteId;
        int height;
        int attemptNumber;
        AttemptResult oldResult;
        AttemptResult newResult;
        QDateTime timestamp;
        QString description;
    };

    explicit ShortcutManager(QWidget *parent = nullptr);
    ~ShortcutManager();

    // 初始化和配置
    /**
     * @brief 设置目标表格视图
     * @param tableView 表格视图指针
     */
    void setTableView(QTableView *tableView);

    /**
     * @brief 设置表格模型
     * @param model 表格模型指针
     */
    void setTableModel(AthleteTableModel *model);

    /**
     * @brief 启用或禁用快捷键
     * @param enabled 是否启用
     */
    void setEnabled(bool enabled);

    /**
     * @brief 检查快捷键是否启用
     * @return 是否启用
     */
    bool isEnabled() const;

    // 撤销/重做功能
    /**
     * @brief 检查是否可以撤销
     * @return 是否可以撤销
     */
    bool canUndo() const;

    /**
     * @brief 检查是否可以重做
     * @return 是否可以重做
     */
    bool canRedo() const;

    /**
     * @brief 获取撤销栈大小
     * @return 撤销栈中的操作数量
     */
    int undoStackSize() const;

    /**
     * @brief 清空撤销栈
     */
    void clearUndoStack();

    // 快捷键配置
    /**
     * @brief 设置自定义快捷键
     * @param action 操作类型
     * @param keySequence 键序列
     */
    void setCustomShortcut(const QString &action, const QKeySequence &keySequence);

    /**
     * @brief 重置为默认快捷键
     */
    void resetToDefaults();

signals:
    /**
     * @brief 试跳结果录入请求
     * @param athleteId 运动员ID
     * @param height 高度
     * @param attemptNumber 试跳次数
     * @param result 试跳结果
     */
    void attemptRecordRequested(int athleteId, int height, int attemptNumber, AttemptResult result);

    /**
     * @brief 导航请求
     * @param direction 导航方向 (0=上, 1=下, 2=左, 3=右)
     */
    void navigationRequested(int direction);

    /**
     * @brief 操作确认请求
     */
    void confirmationRequested();

    /**
     * @brief 操作取消请求
     */
    void cancellationRequested();

    /**
     * @brief 撤销操作完成
     * @param operation 被撤销的操作
     */
    void operationUndone(const OperationRecord &operation);

    /**
     * @brief 重做操作完成
     * @param operation 被重做的操作
     */
    void operationRedone(const OperationRecord &operation);

    /**
     * @brief 撤销栈状态变化
     * @param canUndo 是否可以撤销
     * @param canRedo 是否可以重做
     */
    void undoRedoStateChanged(bool canUndo, bool canRedo);

public slots:
    /**
     * @brief 执行撤销操作
     */
    void undo();

    /**
     * @brief 执行重做操作
     */
    void redo();

    /**
     * @brief 记录新的操作到撤销栈
     * @param operation 操作记录
     */
    void recordOperation(const OperationRecord &operation);

    /**
     * @brief 处理表格选择变化
     * @param current 当前选中的索引
     * @param previous 之前选中的索引
     */
    void onSelectionChanged(const QModelIndex &current, const QModelIndex &previous);

private slots:
    // 快捷键处理槽
    void onSuccessShortcut();
    void onFailureShortcut();
    void onSkipShortcut();
    void onRetireShortcut();
    void onUndoShortcut();
    void onRedoShortcut();
    void onNavigateUp();
    void onNavigateDown();
    void onNavigateLeft();
    void onNavigateRight();
    void onTabNavigation();
    void onShiftTabNavigation();
    void onConfirmShortcut();
    void onCancelShortcut();

    // 内部处理
    void onOperationTimeout();

private:
    // 初始化方法
    void setupShortcuts();
    void setupDefaultShortcuts();
    void connectShortcuts();

    // 快捷键管理
    void createShortcut(const QString &name, const QKeySequence &sequence, const char *slot);
    void updateShortcutContext();

    // 操作处理
    void processAttemptRecord(AttemptResult result);
    void processNavigation(int direction);
    bool isValidCell(const QModelIndex &index) const;
    bool isHeightColumn(const QModelIndex &index) const;
    int getNextAttemptNumber(int athleteId, int height) const;

    // 撤销/重做实现
    void executeUndo(const OperationRecord &operation);
    void executeRedo(const OperationRecord &operation);
    void pushOperation(const OperationRecord &operation);

    // 数据成员
    QWidget *m_parent;
    QTableView *m_tableView;
    AthleteTableModel *m_tableModel;
    bool m_enabled;

    // 快捷键对象
    QHash<QString, QShortcut*> m_shortcuts;
    QHash<QString, QKeySequence> m_customShortcuts;

    // 撤销/重做栈
    QStack<OperationRecord> m_undoStack;
    QStack<OperationRecord> m_redoStack;
    static const int MAX_UNDO_STACK_SIZE = 100;

    // 当前状态
    QModelIndex m_currentIndex;
    QTimer *m_operationTimer;
    bool m_operationInProgress;

    // 默认快捷键
    static const QHash<QString, QKeySequence> DEFAULT_SHORTCUTS;
};

#endif // SHORTCUT_MANAGER_H
