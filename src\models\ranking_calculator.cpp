#include "ranking_calculator.h"
#include "athlete.h"
#include "competition.h"
#include "jump_attempt.h"
#include <QDebug>
#include <algorithm>

RankingCalculator::RankingCalculator(QObject *parent)
    : QObject(parent)
    , m_strictRankingMode(false)
    , m_maxTiedRanks(10)
    , m_considerAttemptTime(false)
{
    qDebug() << "RankingCalculator: Initialized";
}

RankingCalculator::~RankingCalculator()
{
    qDebug() << "RankingCalculator: Destroyed";
}

bool RankingCalculator::AthleteRanking::operator<(const AthleteRanking &other) const
{
    // Primary: Best height (higher is better)
    if (bestHeight != other.bestHeight) {
        return bestHeight > other.bestHeight;
    }
    
    // Secondary: Failures at best height (fewer is better)
    if (failuresAtBest != other.failuresAtBest) {
        return failuresAtBest < other.failuresAtBest;
    }
    
    // Tertiary: Total failures (fewer is better)
    if (totalFailures != other.totalFailures) {
        return totalFailures < other.totalFailures;
    }
    
    // Quaternary: Consider attempt time if enabled
    // (Earlier last attempt is better - indicates fewer attempts needed)
    if (lastAttemptTime.isValid() && other.lastAttemptTime.isValid()) {
        return lastAttemptTime < other.lastAttemptTime;
    }
    
    // If all else is equal, maintain stable sort by athlete ID
    return athleteId < other.athleteId;
}

bool RankingCalculator::AthleteRanking::operator==(const AthleteRanking &other) const
{
    return bestHeight == other.bestHeight &&
           failuresAtBest == other.failuresAtBest &&
           totalFailures == other.totalFailures;
}

RankingCalculator::RankingResult RankingCalculator::calculateRankings(Competition *competition)
{
    if (!competition) {
        qWarning() << "RankingCalculator::calculateRankings: Null competition provided";
        return RankingResult();
    }
    
    QList<Athlete*> athletes = competition->getAthletes();
    QList<int> heightProgression = competition->getHeightProgression();
    
    // TODO: Get actual attempt data from competition
    // For now, create empty attempt data structure
    QHash<int, QHash<int, QHash<int, int>>> attempts;
    
    return calculateRankings(athletes, attempts, heightProgression);
}

RankingCalculator::RankingResult RankingCalculator::calculateRankings(
    const QList<Athlete*> &athletes,
    const QHash<int, QHash<int, QHash<int, int>>> &attempts,
    const QList<int> &heightProgression)
{
    RankingResult result;
    result.calculationTime = QDateTime::currentDateTime();
    result.totalAthletes = athletes.size();
    result.activeAthletes = 0;
    result.retiredAthletes = 0;
    result.completedAthletes = 0;
    result.isCompetitionFinished = false;
    
    // Calculate ranking for each athlete
    for (Athlete *athlete : athletes) {
        if (!athlete) continue;
        
        int athleteId = athlete->id();
        QHash<int, QHash<int, int>> athleteAttempts = attempts.value(athleteId);
        
        AthleteRanking ranking = calculateAthleteRanking(athleteId, athleteAttempts, heightProgression);
        result.rankings.append(ranking);
        
        // Update counters
        if (ranking.isRetired) {
            result.retiredAthletes++;
        } else if (ranking.hasStarted) {
            if (ranking.bestHeight > 0) {
                result.activeAthletes++;
            }
        }
    }
    
    result.completedAthletes = result.totalAthletes - result.activeAthletes - result.retiredAthletes;
    
    // Sort and assign ranks
    sortRankings(result.rankings);
    assignRanks(result.rankings);
    
    // Check if competition is finished
    result.isCompetitionFinished = isCompetitionFinished(result, 
        heightProgression.isEmpty() ? 0 : heightProgression.last());
    
    emit rankingsCalculated(result);
    emit competitionStateChanged(result.activeAthletes, result.completedAthletes, result.isCompetitionFinished);
    
    qDebug() << "RankingCalculator::calculateRankings: Calculated rankings for" 
             << result.totalAthletes << "athletes";
    
    return result;
}

RankingCalculator::AthleteRanking RankingCalculator::calculateAthleteRanking(
    int athleteId,
    const QHash<int, QHash<int, int>> &attempts,
    const QList<int> &heightProgression)
{
    AthleteRanking ranking;
    ranking.athleteId = athleteId;
    ranking.rank = 0; // Will be assigned later
    ranking.bestHeight = getBestHeight(athleteId, attempts);
    ranking.failuresAtBest = 0;
    ranking.totalFailures = countFailures(attempts);
    ranking.totalAttempts = 0;
    ranking.isRetired = isAthleteRetired(attempts);
    ranking.hasStarted = hasAthleteStarted(attempts);
    ranking.lastAttemptTime = getLastAttemptTime(attempts);
    
    // Count failures at best height
    if (ranking.bestHeight > 0) {
        ranking.failuresAtBest = countFailures(attempts, ranking.bestHeight);
    }
    
    // Count total attempts
    for (auto heightIt = attempts.begin(); heightIt != attempts.end(); ++heightIt) {
        for (auto attemptIt = heightIt.value().begin(); attemptIt != heightIt.value().end(); ++attemptIt) {
            if (isValidAttemptResult(attemptIt.value())) {
                ranking.totalAttempts++;
            }
        }
    }
    
    return ranking;
}

int RankingCalculator::getBestHeight(int athleteId, const QHash<int, QHash<int, int>> &attempts)
{
    Q_UNUSED(athleteId)
    
    int bestHeight = 0;
    
    for (auto heightIt = attempts.begin(); heightIt != attempts.end(); ++heightIt) {
        int height = heightIt.key();
        const QHash<int, int> &heightAttempts = heightIt.value();
        
        // Check if athlete passed this height
        for (auto attemptIt = heightAttempts.begin(); attemptIt != heightAttempts.end(); ++attemptIt) {
            if (attemptIt.value() == PASS_RESULT) {
                bestHeight = qMax(bestHeight, height);
                break; // Found a pass at this height
            }
        }
    }
    
    return bestHeight;
}

int RankingCalculator::countFailures(const QHash<int, QHash<int, int>> &attempts, int height)
{
    int failures = 0;
    
    for (auto heightIt = attempts.begin(); heightIt != attempts.end(); ++heightIt) {
        int currentHeight = heightIt.key();
        
        // If specific height requested, only count failures at that height
        if (height >= 0 && currentHeight != height) {
            continue;
        }
        
        const QHash<int, int> &heightAttempts = heightIt.value();
        for (auto attemptIt = heightAttempts.begin(); attemptIt != heightAttempts.end(); ++attemptIt) {
            if (attemptIt.value() == FAIL_RESULT) {
                failures++;
            }
        }
    }
    
    return failures;
}

bool RankingCalculator::isAthleteRetired(const QHash<int, QHash<int, int>> &attempts)
{
    // Check if any attempt is marked as retired
    for (auto heightIt = attempts.begin(); heightIt != attempts.end(); ++heightIt) {
        const QHash<int, int> &heightAttempts = heightIt.value();
        for (auto attemptIt = heightAttempts.begin(); attemptIt != heightAttempts.end(); ++attemptIt) {
            if (attemptIt.value() == RETIRE_RESULT) {
                return true;
            }
        }
    }
    
    return false;
}

bool RankingCalculator::hasAthleteStarted(const QHash<int, QHash<int, int>> &attempts)
{
    // Check if athlete has any valid attempts
    for (auto heightIt = attempts.begin(); heightIt != attempts.end(); ++heightIt) {
        const QHash<int, int> &heightAttempts = heightIt.value();
        for (auto attemptIt = heightAttempts.begin(); attemptIt != heightAttempts.end(); ++attemptIt) {
            if (isValidAttemptResult(attemptIt.value())) {
                return true;
            }
        }
    }
    
    return false;
}

bool RankingCalculator::isCompetitionFinished(const RankingResult &rankings, int currentHeight)
{
    Q_UNUSED(currentHeight)
    
    // Competition is finished if no athletes are active
    return rankings.activeAthletes == 0 && rankings.totalAthletes > 0;
}

int RankingCalculator::getActiveAthleteCount(const RankingResult &rankings)
{
    return rankings.activeAthletes;
}

int RankingCalculator::getNextSuggestedHeight(const RankingResult &rankings,
                                             const QList<int> &heightProgression,
                                             int currentHeight)
{
    Q_UNUSED(rankings)
    
    // Find current height index
    int currentIndex = heightProgression.indexOf(currentHeight);
    
    // If current height not found or is the last height, competition should end
    if (currentIndex < 0 || currentIndex >= heightProgression.size() - 1) {
        return -1;
    }
    
    // Return next height in progression
    return heightProgression[currentIndex + 1];
}

void RankingCalculator::setStrictRankingMode(bool strict)
{
    m_strictRankingMode = strict;
    qDebug() << "RankingCalculator::setStrictRankingMode:" << strict;
}

void RankingCalculator::setMaxTiedRanks(int maxTies)
{
    m_maxTiedRanks = maxTies;
    qDebug() << "RankingCalculator::setMaxTiedRanks:" << maxTies;
}

void RankingCalculator::setConsiderAttemptTime(bool considerTime)
{
    m_considerAttemptTime = considerTime;
    qDebug() << "RankingCalculator::setConsiderAttemptTime:" << considerTime;
}

void RankingCalculator::sortRankings(QList<AthleteRanking> &rankings)
{
    // Separate retired athletes
    QList<AthleteRanking> activeRankings;
    QList<AthleteRanking> retiredRankings;

    for (const AthleteRanking &ranking : rankings) {
        if (ranking.isRetired) {
            retiredRankings.append(ranking);
        } else {
            activeRankings.append(ranking);
        }
    }

    // Sort active athletes by performance
    std::sort(activeRankings.begin(), activeRankings.end());

    // Sort retired athletes by their best performance before retiring
    std::sort(retiredRankings.begin(), retiredRankings.end());

    // Combine: active athletes first, then retired athletes
    rankings.clear();
    rankings.append(activeRankings);
    rankings.append(retiredRankings);
}

void RankingCalculator::assignRanks(QList<AthleteRanking> &rankings)
{
    if (rankings.isEmpty()) {
        return;
    }

    int currentRank = 1;

    for (int i = 0; i < rankings.size(); ++i) {
        if (i > 0) {
            // Check if current athlete has same performance as previous
            const AthleteRanking &current = rankings[i];
            const AthleteRanking &previous = rankings[i-1];

            if (!m_strictRankingMode && current == previous) {
                // Same performance - keep same rank
                rankings[i].rank = rankings[i-1].rank;
            } else {
                // Different performance - advance rank
                currentRank = i + 1;
                rankings[i].rank = currentRank;
            }
        } else {
            // First athlete gets rank 1
            rankings[i].rank = currentRank;
        }
    }

    // Handle tied ranks if needed
    if (!m_strictRankingMode) {
        handleTiedRanks(rankings);
    }
}

void RankingCalculator::handleTiedRanks(QList<AthleteRanking> &rankings)
{
    // Group athletes with same rank
    QHash<int, QList<int>> rankGroups; // rank -> list of indices

    for (int i = 0; i < rankings.size(); ++i) {
        int rank = rankings[i].rank;
        rankGroups[rank].append(i);
    }

    // Check for groups that exceed max tied ranks
    for (auto it = rankGroups.begin(); it != rankGroups.end(); ++it) {
        QList<int> &indices = it.value();

        if (indices.size() > m_maxTiedRanks) {
            // Too many tied - need to break ties
            // Sort by secondary criteria (already done in main sort)
            // Just assign sequential ranks
            for (int i = 0; i < indices.size(); ++i) {
                rankings[indices[i]].rank = it.key() + i;
            }
        }
    }
}

bool RankingCalculator::isValidAttemptResult(int result)
{
    return result == PASS_RESULT || result == FAIL_RESULT ||
           result == SKIP_RESULT || result == RETIRE_RESULT;
}

int RankingCalculator::convertToJumpAttemptResult(int result)
{
    switch (result) {
    case PASS_RESULT:
        return static_cast<int>(JumpAttempt::Pass);
    case FAIL_RESULT:
        return static_cast<int>(JumpAttempt::Fail);
    case SKIP_RESULT:
        return static_cast<int>(JumpAttempt::Skip);
    case RETIRE_RESULT:
        return static_cast<int>(JumpAttempt::Retire);
    default:
        return static_cast<int>(JumpAttempt::Invalid);
    }
}

QDateTime RankingCalculator::getLastAttemptTime(const QHash<int, QHash<int, int>> &attempts)
{
    QDateTime lastTime;

    // For now, return current time as we don't have timestamp data
    // In a real implementation, this would extract the actual attempt timestamps
    if (!attempts.isEmpty()) {
        lastTime = QDateTime::currentDateTime();
    }

    return lastTime;
}
