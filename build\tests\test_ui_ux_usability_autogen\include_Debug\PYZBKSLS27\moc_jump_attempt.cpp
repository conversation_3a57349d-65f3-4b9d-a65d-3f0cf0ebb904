/****************************************************************************
** Meta object code from reading C++ file 'jump_attempt.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../src/models/jump_attempt.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'jump_attempt.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN11JumpAttemptE_t {};
} // unnamed namespace

template <> constexpr inline auto JumpAttempt::qt_create_metaobjectdata<qt_meta_tag_ZN11JumpAttemptE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "JumpAttempt",
        "idChanged",
        "",
        "athleteIdChanged",
        "competitionIdChanged",
        "heightChanged",
        "attemptNumberChanged",
        "resultChanged",
        "timestampChanged",
        "notesChanged",
        "id",
        "athleteId",
        "competitionId",
        "height",
        "attemptNumber",
        "result",
        "AttemptResult",
        "timestamp",
        "notes",
        "NotAttempted",
        "Pass",
        "Fail",
        "Skip",
        "Retire",
        "Invalid"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'idChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'athleteIdChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'competitionIdChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'heightChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'attemptNumberChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'resultChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'timestampChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'notesChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'id'
        QtMocHelpers::PropertyData<int>(10, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 0),
        // property 'athleteId'
        QtMocHelpers::PropertyData<int>(11, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 1),
        // property 'competitionId'
        QtMocHelpers::PropertyData<int>(12, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 2),
        // property 'height'
        QtMocHelpers::PropertyData<int>(13, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 3),
        // property 'attemptNumber'
        QtMocHelpers::PropertyData<int>(14, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 4),
        // property 'result'
        QtMocHelpers::PropertyData<AttemptResult>(15, 0x80000000 | 16, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag | QMC::StdCppSet, 5),
        // property 'timestamp'
        QtMocHelpers::PropertyData<QDateTime>(17, QMetaType::QDateTime, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 6),
        // property 'notes'
        QtMocHelpers::PropertyData<QString>(18, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 7),
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'AttemptResult'
        QtMocHelpers::EnumData<AttemptResult>(16, 16, QMC::EnumFlags{}).add({
            {   19, AttemptResult::NotAttempted },
            {   20, AttemptResult::Pass },
            {   21, AttemptResult::Fail },
            {   22, AttemptResult::Skip },
            {   23, AttemptResult::Retire },
            {   24, AttemptResult::Invalid },
        }),
    };
    return QtMocHelpers::metaObjectData<JumpAttempt, qt_meta_tag_ZN11JumpAttemptE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject JumpAttempt::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11JumpAttemptE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11JumpAttemptE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN11JumpAttemptE_t>.metaTypes,
    nullptr
} };

void JumpAttempt::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<JumpAttempt *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->idChanged(); break;
        case 1: _t->athleteIdChanged(); break;
        case 2: _t->competitionIdChanged(); break;
        case 3: _t->heightChanged(); break;
        case 4: _t->attemptNumberChanged(); break;
        case 5: _t->resultChanged(); break;
        case 6: _t->timestampChanged(); break;
        case 7: _t->notesChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (JumpAttempt::*)()>(_a, &JumpAttempt::idChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (JumpAttempt::*)()>(_a, &JumpAttempt::athleteIdChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (JumpAttempt::*)()>(_a, &JumpAttempt::competitionIdChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (JumpAttempt::*)()>(_a, &JumpAttempt::heightChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (JumpAttempt::*)()>(_a, &JumpAttempt::attemptNumberChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (JumpAttempt::*)()>(_a, &JumpAttempt::resultChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (JumpAttempt::*)()>(_a, &JumpAttempt::timestampChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (JumpAttempt::*)()>(_a, &JumpAttempt::notesChanged, 7))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->id(); break;
        case 1: *reinterpret_cast<int*>(_v) = _t->athleteId(); break;
        case 2: *reinterpret_cast<int*>(_v) = _t->competitionId(); break;
        case 3: *reinterpret_cast<int*>(_v) = _t->height(); break;
        case 4: *reinterpret_cast<int*>(_v) = _t->attemptNumber(); break;
        case 5: *reinterpret_cast<AttemptResult*>(_v) = _t->result(); break;
        case 6: *reinterpret_cast<QDateTime*>(_v) = _t->timestamp(); break;
        case 7: *reinterpret_cast<QString*>(_v) = _t->notes(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setId(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setAthleteId(*reinterpret_cast<int*>(_v)); break;
        case 2: _t->setCompetitionId(*reinterpret_cast<int*>(_v)); break;
        case 3: _t->setHeight(*reinterpret_cast<int*>(_v)); break;
        case 4: _t->setAttemptNumber(*reinterpret_cast<int*>(_v)); break;
        case 5: _t->setResult(*reinterpret_cast<AttemptResult*>(_v)); break;
        case 6: _t->setTimestamp(*reinterpret_cast<QDateTime*>(_v)); break;
        case 7: _t->setNotes(*reinterpret_cast<QString*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *JumpAttempt::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *JumpAttempt::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11JumpAttemptE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int JumpAttempt::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 8;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void JumpAttempt::idChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void JumpAttempt::athleteIdChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void JumpAttempt::competitionIdChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void JumpAttempt::heightChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void JumpAttempt::attemptNumberChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void JumpAttempt::resultChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void JumpAttempt::timestampChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void JumpAttempt::notesChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}
QT_WARNING_POP
