#ifndef JUMP_ATTEMPT_H
#define JUMP_ATTEMPT_H

#include <QObject>
#include <QDateTime>
#include <QMetaType>

class Athlete;
class Competition;

/**
 * @brief Jump attempt data model for High Jump Competition Management
 * 
 * This class represents a single jump attempt by an athlete during a high jump competition.
 * It provides comprehensive tracking of attempt details including height, result, timing,
 * and metadata. The class implements Qt's property system for easy data binding and
 * signal/slot communication.
 * 
 * The JumpAttempt class provides:
 * - Attempt information tracking (height, attempt number, timestamp)
 * - Result management (Pass, Fail, Skip) with symbolic representation
 * - Athlete and competition relationship management
 * - Attempt validation and result analysis
 * - Utility methods for result conversion and display
 * - Qt property system integration for UI binding
 * 
 * The class supports both programmatic attempt recording and user-initiated
 * attempt management through the application's competition interface.
 * 
 * @note This class maintains relationships with Athlete and Competition objects
 *       and provides the fundamental data structure for jump attempt tracking.
 */
class JumpAttempt : public QObject
{
    Q_OBJECT
    Q_PROPERTY(int id READ id WRITE setId NOTIFY idChanged)
    Q_PROPERTY(int athleteId READ athleteId WRITE setAthleteId NOTIFY athleteIdChanged)
    Q_PROPERTY(int competitionId READ competitionId WRITE setCompetitionId NOTIFY competitionIdChanged)
    Q_PROPERTY(int height READ height WRITE setHeight NOTIFY heightChanged)
    Q_PROPERTY(int attemptNumber READ attemptNumber WRITE setAttemptNumber NOTIFY attemptNumberChanged)
    Q_PROPERTY(AttemptResult result READ result WRITE setResult NOTIFY resultChanged)
    Q_PROPERTY(QDateTime timestamp READ timestamp WRITE setTimestamp NOTIFY timestampChanged)
    Q_PROPERTY(QString notes READ notes WRITE setNotes NOTIFY notesChanged)

public:
    /**
     * @brief Jump attempt result enumeration
     * 
     * Defines the possible results of a jump attempt.
     * Each result represents a specific outcome of the attempt.
     */
    enum AttemptResult {
        NotAttempted = 0,  ///< Attempt has not been made yet
        Pass,               ///< O - Successful jump, cleared the height
        Fail,               ///< X - Failed jump, did not clear the height
        Skip,               ///< - - Skipped attempt, athlete chose not to attempt
        Retire,
        Invalid               
    };
    Q_ENUM(AttemptResult)

    /**
     * @brief Constructs a jump attempt object
     * @param parent Parent QObject (default: nullptr)
     * 
     * Creates a new jump attempt object with default values.
     * All properties are initialized to empty/default values.
     */
    explicit JumpAttempt(QObject *parent = nullptr);
    
    // Getters
    /**
     * @brief Gets the attempt's unique identifier
     * @return Attempt ID
     * 
     * Returns the unique identifier assigned to this jump attempt.
     * Used for database storage and attempt management.
     */
    int id() const { return m_id; }
    
    /**
     * @brief Gets the athlete ID
     * @return Athlete ID
     * 
     * Returns the ID of the athlete who made this attempt.
     */
    int athleteId() const { return m_athleteId; }
    
    /**
     * @brief Gets the competition ID
     * @return Competition ID
     * 
     * Returns the ID of the competition where this attempt was made.
     */
    int competitionId() const { return m_competitionId; }
    
    /**
     * @brief Gets the attempt height
     * @return Height in centimeters
     * 
     * Returns the height that was attempted in this jump.
     */
    int height() const { return m_height; }
    
    /**
     * @brief Gets the attempt number
     * @return Attempt number (1, 2, or 3)
     * 
     * Returns the attempt number for this height (1st, 2nd, or 3rd attempt).
     */
    int attemptNumber() const { return m_attemptNumber; }
    
    /**
     * @brief Gets the attempt result
     * @return Attempt result
     * 
     * Returns the result of this jump attempt.
     */
    AttemptResult result() const { return m_result; }
    
    /**
     * @brief Gets the attempt timestamp
     * @return Attempt timestamp
     * 
     * Returns the date and time when this attempt was made.
     */
    QDateTime timestamp() const { return m_timestamp; }
    
    /**
     * @brief Gets the attempt notes
     * @return Attempt notes
     * 
     * Returns any additional notes or comments about this attempt.
     */
    QString notes() const { return m_notes; }
    
    // Setters
    /**
     * @brief Sets the attempt's unique identifier
     * @param id New attempt ID
     * 
     * Sets the unique identifier for this jump attempt.
     * Emits idChanged() signal when the value changes.
     */
    void setId(int id);
    
    /**
     * @brief Sets the athlete ID
     * @param athleteId New athlete ID
     * 
     * Sets the ID of the athlete who made this attempt.
     * Emits athleteIdChanged() signal when the value changes.
     */
    void setAthleteId(int athleteId);
    
    /**
     * @brief Sets the competition ID
     * @param competitionId New competition ID
     * 
     * Sets the ID of the competition where this attempt was made.
     * Emits competitionIdChanged() signal when the value changes.
     */
    void setCompetitionId(int competitionId);
    
    /**
     * @brief Sets the attempt height
     * @param height New height in centimeters
     * 
     * Sets the height that was attempted in this jump.
     * Emits heightChanged() signal when the value changes.
     */
    void setHeight(int height);
    
    /**
     * @brief Sets the attempt number
     * @param attemptNumber New attempt number (1, 2, or 3)
     * 
     * Sets the attempt number for this height.
     * Emits attemptNumberChanged() signal when the value changes.
     */
    void setAttemptNumber(int attemptNumber);
    
    /**
     * @brief Sets the attempt result
     * @param result New attempt result
     * 
     * Sets the result of this jump attempt.
     * Emits resultChanged() signal when the value changes.
     */
    void setResult(AttemptResult result);
    
    /**
     * @brief Sets the attempt timestamp
     * @param timestamp New attempt timestamp
     * 
     * Sets the date and time when this attempt was made.
     * Emits timestampChanged() signal when the value changes.
     */
    void setTimestamp(const QDateTime &timestamp);
    
    /**
     * @brief Sets the attempt notes
     * @param notes New attempt notes
     * 
     * Sets any additional notes or comments about this attempt.
     * Emits notesChanged() signal when the value changes.
     */
    void setNotes(const QString &notes);
    
    // Utility methods
    /**
     * @brief Validates attempt data
     * @return true if attempt data is valid, false otherwise
     * 
     * Validates that the attempt has all required information.
     * Checks for valid athlete ID, competition ID, height, and attempt number.
     */
    bool isValid() const;
    
    /**
     * @brief Gets the result as a string
     * @return Human-readable result string
     * 
     * Returns a human-readable string representation of the attempt result.
     */
    QString resultString() const;
    
    /**
     * @brief Gets the result as a symbol
     * @return Result symbol (O, X, or -)
     * 
     * Returns the symbolic representation of the attempt result.
     * O for Pass, X for Fail, - for Skip.
     */
    QString resultSymbol() const;
    
    /**
     * @brief Checks if the attempt was successful
     * @return true if attempt was successful, false otherwise
     * 
     * Returns whether the attempt resulted in a successful jump (Pass).
     */
    bool isSuccessful() const;
    
    /**
     * @brief Checks if the attempt failed
     * @return true if attempt failed, false otherwise
     * 
     * Returns whether the attempt resulted in a failed jump (Fail).
     */
    bool isFailed() const;
    
    /**
     * @brief Checks if the attempt was skipped
     * @return true if attempt was skipped, false otherwise
     * 
     * Returns whether the attempt was skipped by the athlete.
     */
    bool isSkipped() const;
    
    // Static utility methods
    /**
     * @brief Converts result to string
     * @param result Attempt result to convert
     * @return Human-readable result string
     * 
     * Converts an AttemptResult enum value to a human-readable string.
     * Static method for utility purposes.
     */
    static QString resultToString(AttemptResult result);
    
    /**
     * @brief Converts result to symbol
     * @param result Attempt result to convert
     * @return Result symbol (O, X, or -)
     * 
     * Converts an AttemptResult enum value to its symbolic representation.
     * Static method for utility purposes.
     */
    static QString resultToSymbol(AttemptResult result);
    
    /**
     * @brief Converts string to result
     * @param str String to convert
     * @return Attempt result
     * 
     * Converts a string representation to an AttemptResult enum value.
     * Static method for utility purposes.
     */
    static AttemptResult stringToResult(const QString &str);
    
signals:
    /**
     * @brief Emitted when attempt ID changes
     * 
     * Signal emitted when the attempt's unique identifier is modified.
     * Used for UI updates and data binding.
     */
    void idChanged();
    
    /**
     * @brief Emitted when athlete ID changes
     * 
     * Signal emitted when the athlete ID is modified.
     * Used for UI updates and data binding.
     */
    void athleteIdChanged();
    
    /**
     * @brief Emitted when competition ID changes
     * 
     * Signal emitted when the competition ID is modified.
     * Used for UI updates and data binding.
     */
    void competitionIdChanged();
    
    /**
     * @brief Emitted when height changes
     * 
     * Signal emitted when the attempt height is modified.
     * Used for UI updates and data binding.
     */
    void heightChanged();
    
    /**
     * @brief Emitted when attempt number changes
     * 
     * Signal emitted when the attempt number is modified.
     * Used for UI updates and data binding.
     */
    void attemptNumberChanged();
    
    /**
     * @brief Emitted when result changes
     * 
     * Signal emitted when the attempt result is modified.
     * Used for UI updates and data binding.
     */
    void resultChanged();
    
    /**
     * @brief Emitted when timestamp changes
     * 
     * Signal emitted when the attempt timestamp is modified.
     * Used for UI updates and data binding.
     */
    void timestampChanged();
    
    /**
     * @brief Emitted when notes change
     * 
     * Signal emitted when the attempt notes are modified.
     * Used for UI updates and data binding.
     */
    void notesChanged();

private:
    int m_id;                    ///< Unique attempt identifier
    int m_athleteId;             ///< ID of the athlete who made the attempt
    int m_competitionId;         ///< ID of the competition where attempt was made
    int m_height;                ///< Height attempted in centimeters
    int m_attemptNumber;         ///< Attempt number for this height (1, 2, or 3)
    AttemptResult m_result;      ///< Result of the attempt
    QDateTime m_timestamp;       ///< When the attempt was made
    QString m_notes;             ///< Additional notes about the attempt
};

Q_DECLARE_METATYPE(JumpAttempt*)
Q_DECLARE_METATYPE(JumpAttempt::AttemptResult)

#endif // JUMP_ATTEMPT_H