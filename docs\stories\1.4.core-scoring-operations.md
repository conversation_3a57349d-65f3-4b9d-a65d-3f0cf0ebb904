# Story 1.4: 实现核心计分操作 (离线优先)

## Status
Approved

## Epic
Epic 1: MVP核心功能 - 基础跳高计分系统

## Story Points
8

## Priority
High

## Summary
实现核心计分操作功能，包括试跳结果记录按钮和键盘快捷键（O/X/-/R），确保所有操作首先写入本地SQLite数据库，同时添加到同步队列中，实现真正的离线优先工作流。

## User Story
**作为一个** 记分员, **我想要** 使用简洁的按钮或键盘快捷键（o, x, -, r）来记录当前焦点运动员的试跳结果, **以便于** 结果能**即时、安全地保存在本地电脑上**，不受网络状态影响。

## Acceptance Criteria
1. 界面上提供"成功(o)", "失败(x)", "免跳(-)"和"弃权(r)"四个核心操作按钮。
2. 点击按钮或使用快捷键，能将结果**立即写入本地SQLite数据库**。
3. 该操作同时被添加到一个本地的"待同步队列"中，并标记为"待处理"状态。
4. 规则引擎能根据**本地数据库**的数据正确处理运动员状态并更新UI。
5. 每次录入后，操作焦点会自动按规则移至下一个逻辑状态。

## Tasks / Subtasks
- [ ] 实现试跳结果记录UI组件 (AC: 1)
  - [ ] 在ScoringView中添加四个操作按钮（成功/失败/免跳/弃权）
  - [ ] 设计按钮布局和样式，确保易于快速操作
  - [ ] 添加按钮图标和文字标识
- [ ] 实现键盘快捷键系统 (AC: 1)
  - [ ] 扩展ShortcutManager类支持O/X/-/R快捷键
  - [ ] 确保快捷键在主计分界面激活时有效
  - [ ] 添加快捷键提示显示
- [ ] 实现本地数据库写入逻辑 (AC: 2)
  - [ ] 扩展DatabaseManager的recordAttempt方法
  - [ ] 确保数据库操作在事务中进行
  - [ ] 添加数据验证和错误处理
- [ ] 实现同步队列机制 (AC: 3)
  - [ ] 创建SyncQueueManager类管理待同步操作
  - [ ] 在每次试跳记录后添加同步队列条目
  - [ ] 实现队列状态管理（待处理/进行中/已完成/失败）
- [ ] 集成规则引擎处理 (AC: 4)
  - [ ] 连接试跳记录到RulesEngine进行状态更新
  - [ ] 实现运动员状态自动更新（活跃/淘汰/退赛）
  - [ ] 触发排名重新计算
- [ ] 实现焦点自动前进逻辑 (AC: 5)
  - [ ] 在CompetitionState中实现下一个焦点计算
  - [ ] 根据跳高规则确定下一位试跳运动员
  - [ ] 更新UI焦点位置和高亮显示
- [ ] 添加单元测试
  - [ ] 测试试跳记录的数据库写入
  - [ ] 测试同步队列操作
  - [ ] 测试规则引擎集成
  - [ ] 测试焦点前进逻辑

## Dev Notes

### Previous Story Insights
从Story 1.3的实现中获得的关键经验：
- 已建立了完善的Model/View架构，CompetitionState和AthleteTableModel提供了良好的数据管理基础
- 信号槽机制工作良好，确保了UI和数据层的解耦
- 性能优化已到位，支持100+运动员的高效渲染
- 线程安全机制已实现，使用mutex保护共享数据访问

### Data Models
**AttemptRecord模型** [Source: architecture/data-models.md#AttemptRecord]:
- `id`: int - 记录唯一标识符
- `athleteId`: int - 运动员ID
- `height`: int - 试跳高度(厘米)
- `attemptNumber`: int - 该高度第几次试跳(1-3)
- `result`: AttemptResult - 试跳结果(Success/Failure/Pass/Retire)
- `timestamp`: QDateTime - 试跳时间戳

**SyncQueueEntry模型** [Source: architecture/data-models.md#SyncQueueEntry]:
- `id`: int - 队列条目唯一标识符
- `operationType`: OperationType - 操作类型(CreateAttempt/UpdateAthlete/UpdateCompetition)
- `data`: QJsonObject - JSON格式的操作数据
- `status`: SyncStatus - 同步状态(Pending/InProgress/Completed/Failed)
- `createdAt`: QDateTime - 创建时间

### Database Schema
**attempt_records表结构** [Source: architecture/database-schema.md#attempt_records]:
```sql
CREATE TABLE attempt_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    athlete_id INTEGER NOT NULL,
    height INTEGER NOT NULL,
    attempt_number INTEGER NOT NULL,  -- 该高度第几次试跳 (1-3)
    result TEXT NOT NULL,            -- 'success', 'failure', 'pass', 'retire'
    timestamp TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (athlete_id) REFERENCES athletes(id) ON DELETE CASCADE,
    UNIQUE (athlete_id, height, attempt_number),
    CHECK (attempt_number BETWEEN 1 AND 3),
    CHECK (height > 0),
    CHECK (result IN ('success', 'failure', 'pass', 'retire'))
);
```

**sync_queue表结构** [Source: architecture/database-schema.md#sync_queue]:
```sql
CREATE TABLE sync_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operation_type TEXT NOT NULL,   -- 'create_attempt', 'update_athlete', etc.
    data_json TEXT NOT NULL,       -- JSON格式的操作数据
    status TEXT NOT NULL DEFAULT 'pending',
    retry_count INTEGER NOT NULL DEFAULT 0,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_attempt_at TEXT,
    completed_at TEXT,
    error_message TEXT,
    
    CHECK (operation_type IN ('create_attempt', 'update_athlete', 'update_competition')),
    CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
    CHECK (retry_count >= 0)
);
```

### File Locations
基于项目结构指南 [Source: architecture/unified-project-structure.md]:
- **UI组件**: `src/ui/scoring_view.h/cpp` - 添加试跳操作按钮
- **快捷键管理**: `src/ui/shortcut_manager.h/cpp` - 扩展快捷键支持
- **数据库操作**: `src/persistence/database_manager.h/cpp` - 扩展试跳记录方法
- **同步队列**: 新建 `src/persistence/sync_queue_manager.h/cpp`
- **规则引擎**: `src/core/rules_engine.h/cpp` - 已存在，需集成
- **状态管理**: `src/core/competition_state.h/cpp` - 扩展焦点管理

### API Specifications
**试跳记录API端点** [Source: architecture/rest-api-spec.md]:
- POST `/api/competitions/{id}/attempts` - 创建试跳记录
- 请求格式: `{"athlete_id": int, "height": int, "attempt_number": int, "result": string, "timestamp": string}`
- 响应格式: `{"id": int, "status": "success", "message": string}`

### Component Specifications
**ScoringView UI组件扩展** [Source: architecture/frontend-architecture.md#AthleteTableWidget]:
- 继承现有的AthleteTableWidget架构
- 添加操作按钮工具栏
- 集成ShortcutManager进行键盘事件处理
- 使用信号槽连接到CompetitionModel

**离线优先架构** [Source: architecture/offline-sync.md]:
- 所有操作首先写入本地SQLite数据库
- 同时添加到sync_queue表进行后续同步
- 使用事务确保数据一致性
- 网络恢复时自动处理同步队列

### Technical Constraints
**数据库操作规则** [Source: architecture/coding-standards.md#数据库操作规则]:
- 必须使用参数化查询防止SQL注入
- 数据库写操作必须在事务中进行
- 所有数据库操作必须检查返回值和错误状态

**Qt特定规则** [Source: architecture/coding-standards.md#Qt特定规则]:
- 信号槽连接使用新式语法 `connect(sender, &Class::signal, receiver, &Class::slot)`
- 使用Qt的父子对象系统进行内存管理
- 所有用户可见字符串必须使用tr()包装

### Testing Requirements
**单元测试要求** [Source: architecture/testing-strategy.md#单元测试策略]:
- 测试文件位置: `tests/unit/test_scoring_operations.cpp`
- 使用Qt Test框架和QCOMPARE/QVERIFY宏
- 数据库操作使用内存SQLite数据库进行测试
- Mock外部依赖确保测试隔离性

**集成测试要求** [Source: architecture/testing-strategy.md#集成测试策略]:
- 测试UI组件与数据模型的集成
- 验证信号槽连接的正确性
- 测试完整的试跳记录工作流程

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-07 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*This section will be populated by the QA agent after implementation review*
