#include "athlete_table_model.h"
#include "athlete.h"
#include "competition.h"
#include "jump_attempt.h"
#include "persistence/database_manager.h"
#include <QDebug>
#include <QMutexLocker>
#include <QColor>
#include <QFont>
#include <QApplication>

AthleteTableModel::AthleteTableModel(QObject *parent)
    : QAbstractTableModel(parent)
    , m_competition(nullptr)
    , m_dbManager(DatabaseManager::instance())
    , m_refreshTimer(new QTimer(this))
{
    // Setup refresh timer
    m_refreshTimer->setInterval(REFRESH_INTERVAL_MS);
    connect(m_refreshTimer, &QTimer::timeout, this, &AthleteTableModel::onRefreshTimer);
    
    qDebug() << "AthleteTableModel: Initialized";
}

AthleteTableModel::~AthleteTableModel()
{
    clearData();
    qDebug() << "AthleteTableModel: Destroyed";
}

int AthleteTableModel::rowCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    QMutexLocker locker(&m_dataMutex);
    return m_athletes.size();
}

int AthleteTableModel::columnCount(const QModelIndex &parent) const
{
    Q_UNUSED(parent)
    QMutexLocker locker(&m_dataMutex);
    return FIXED_COLUMN_COUNT + m_heightProgression.size();
}

QVariant AthleteTableModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= m_athletes.size()) {
        return QVariant();
    }

    QMutexLocker locker(&m_dataMutex);

    // Safe array access - avoid potential exception from .at()
    Athlete *athlete = nullptr;
    if (index.row() >= 0 && index.row() < m_athletes.size()) {
        athlete = m_athletes[index.row()];
    } else {
        return QVariant();
    }

    if (!athlete) {
        return QVariant();
    }

    const int column = index.column();
    const int athleteId = athlete->id();

    // Handle display and edit roles
    if (role == Qt::DisplayRole || role == Qt::EditRole) {
        switch (column) {
        case RankColumn:
            return m_athleteRanks.value(athleteId, 0);
        case NumberColumn:
            return athlete->startNumber();
        case NameColumn:
            return QString("%1 %2").arg(athlete->firstName(), athlete->lastName());
        case TeamColumn:
            return athlete->club();
        case BestHeightColumn:
            return m_athleteBestHeights.value(athleteId, 0);
        case StatusColumn:
            return tr("Active"); // TODO: Implement athlete status
        default:
            // Height columns
            if (isHeightColumn(column)) {
                int height = getHeightForColumn(column);
                return formatAttemptDisplay(athleteId, height);
            }
            break;
        }
    }

    // Handle background color role
    if (role == Qt::BackgroundRole) {
        if (isHeightColumn(column)) {
            int height = getHeightForColumn(column);
            return getAttemptData(athleteId, height, role);
        }
        
        // Highlight current leader
        if (column == RankColumn && m_athleteRanks.value(athleteId, 0) == 1) {
            return QColor("#E3F2FD"); // Light blue for leader
        }
    }

    // Handle text color role
    if (role == Qt::ForegroundRole) {
        if (isHeightColumn(column)) {
            int height = getHeightForColumn(column);
            return getAttemptData(athleteId, height, role);
        }
    }

    // Handle font role
    if (role == Qt::FontRole) {
        if (column == RankColumn && m_athleteRanks.value(athleteId, 0) == 1) {
            QFont font = QApplication::font();
            font.setBold(true);
            return font;
        }
    }

    // Handle custom data roles
    if (role == AthleteIdRole) {
        return athleteId;
    }
    if (role == HeightRole && isHeightColumn(column)) {
        return getHeightForColumn(column);
    }
    if (role == RankingRole) {
        return m_athleteRanks.value(athleteId, 0);
    }

    return QVariant();
}

QVariant AthleteTableModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation != Qt::Horizontal || role != Qt::DisplayRole) {
        return QVariant();
    }

    QMutexLocker locker(&m_dataMutex);

    switch (section) {
    case RankColumn:
        return tr("排名");
    case NumberColumn:
        return tr("号码");
    case NameColumn:
        return tr("姓名");
    case TeamColumn:
        return tr("队伍");
    case BestHeightColumn:
        return tr("最佳成绩");
    case StatusColumn:
        return tr("状态");
    default:
        // Height columns
        if (isHeightColumn(section)) {
            int height = getHeightForColumn(section);
            return QString("%1cm").arg(height);
        }
        break;
    }

    return QVariant();
}

bool AthleteTableModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    if (!index.isValid() || role != Qt::EditRole) {
        return false;
    }

    // For now, we don't allow direct editing through the model
    // Attempts should be recorded through recordAttempt() method
    return false;
}

Qt::ItemFlags AthleteTableModel::flags(const QModelIndex &index) const
{
    if (!index.isValid()) {
        return Qt::NoItemFlags;
    }

    Qt::ItemFlags flags = Qt::ItemIsEnabled | Qt::ItemIsSelectable;

    // Height columns are focusable for attempt recording
    if (isHeightColumn(index.column())) {
        flags |= Qt::ItemIsFocusable;
    }

    return flags;
}

void AthleteTableModel::loadCompetition(Competition *competition)
{
    if (!competition) {
        qWarning() << "AthleteTableModel::loadCompetition: Null competition provided";
        return;
    }

    qDebug() << "AthleteTableModel::loadCompetition: Loading competition" << competition->name();

    beginResetModel();
    {
        QMutexLocker locker(&m_dataMutex);
        
        // Clear existing data
        clearData();
        
        // Set new competition
        m_competition = competition;
        
        // Load height progression
        m_heightProgression = competition->getHeightProgression();
        
        // Load athletes and attempts
        loadAthletes();
        loadAttempts();
        
        // Calculate initial rankings
        calculateRankings();
    }
    endResetModel();

    // Start refresh timer
    m_refreshTimer->start();

    emit competitionLoaded();
    qDebug() << "AthleteTableModel::loadCompetition: Loaded" << m_athletes.size() << "athletes with" 
             << m_heightProgression.size() << "heights";
}

void AthleteTableModel::clearData()
{
    QMutexLocker locker(&m_dataMutex);
    
    m_competition = nullptr;
    m_athletes.clear();
    m_heightProgression.clear();
    m_athleteRanks.clear();
    m_athleteBestHeights.clear();
    m_attempts.clear();
    
    m_refreshTimer->stop();
}

Competition* AthleteTableModel::getCurrentCompetition() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_competition;
}

void AthleteTableModel::setHeightProgression(const QList<int> &heights)
{
    if (heights == m_heightProgression) {
        return;
    }

    qDebug() << "AthleteTableModel::setHeightProgression: Setting heights" << heights;

    beginResetModel();
    {
        QMutexLocker locker(&m_dataMutex);
        m_heightProgression = heights;
    }
    endResetModel();
}

QList<int> AthleteTableModel::getHeightProgression() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_heightProgression;
}

void AthleteTableModel::addHeight(int height)
{
    if (m_heightProgression.contains(height)) {
        return;
    }

    qDebug() << "AthleteTableModel::addHeight: Adding height" << height;

    beginInsertColumns(QModelIndex(), columnCount(), columnCount());
    {
        QMutexLocker locker(&m_dataMutex);
        m_heightProgression.append(height);
        std::sort(m_heightProgression.begin(), m_heightProgression.end());
    }
    endInsertColumns();
}

bool AthleteTableModel::recordAttempt(int athleteId, int height, int attemptNumber, JumpAttempt::AttemptResult result)
{
    if (!m_competition || attemptNumber < 1 || attemptNumber > 3) {
        qWarning() << "AthleteTableModel::recordAttempt: Invalid parameters";
        return false;
    }

    qDebug() << "AthleteTableModel::recordAttempt: Recording attempt for athlete" << athleteId 
             << "at height" << height << "attempt" << attemptNumber << "result" << static_cast<int>(result);

    {
        QMutexLocker locker(&m_dataMutex);
        
        // Store attempt in memory
        m_attempts[athleteId][height][attemptNumber] = result;
        
        // Update best height if successful
        if (result == JumpAttempt::Pass) {
            int currentBest = m_athleteBestHeights.value(athleteId, 0);
            if (height > currentBest) {
                m_athleteBestHeights[athleteId] = height;
            }
        }
    }

    // Update rankings
    calculateRankings();

    // Find the athlete row and height column for targeted update
    int row = getRowForAthlete(athleteId);
    int column = getColumnForHeight(height);
    
    if (row >= 0 && column >= 0) {
        QModelIndex index = createIndex(row, column);
        emit dataChanged(index, index);
        
        // Also update rank and best height columns
        QModelIndex rankIndex = createIndex(row, RankColumn);
        QModelIndex bestIndex = createIndex(row, BestHeightColumn);
        emit dataChanged(rankIndex, bestIndex);
    }

    emit attemptRecorded(athleteId, height, attemptNumber, result);
    emit athleteDataUpdated(athleteId);

    return true;
}

JumpAttempt::AttemptResult AthleteTableModel::getAttemptResult(int athleteId, int height, int attemptNumber) const
{
    QMutexLocker locker(&m_dataMutex);

    if (m_attempts.contains(athleteId) &&
        m_attempts[athleteId].contains(height) &&
        m_attempts[athleteId][height].contains(attemptNumber)) {
        return m_attempts[athleteId][height][attemptNumber];
    }

    return JumpAttempt::Invalid;
}

void AthleteTableModel::updateRankings()
{
    calculateRankings();

    // Emit data changed for rank column
    if (!m_athletes.isEmpty()) {
        QModelIndex topLeft = createIndex(0, RankColumn);
        QModelIndex bottomRight = createIndex(m_athletes.size() - 1, RankColumn);
        emit dataChanged(topLeft, bottomRight);
    }

    emit rankingsUpdated();
}

int AthleteTableModel::getAthleteRank(int athleteId) const
{
    QMutexLocker locker(&m_dataMutex);
    return m_athleteRanks.value(athleteId, 0);
}

int AthleteTableModel::getAthleteBestHeight(int athleteId) const
{
    QMutexLocker locker(&m_dataMutex);
    return m_athleteBestHeights.value(athleteId, 0);
}

Athlete* AthleteTableModel::getAthleteByRow(int row) const
{
    QMutexLocker locker(&m_dataMutex);
    if (row >= 0 && row < m_athletes.size()) {
        return m_athletes[row];  // Safe array access instead of .at()
    }
    return nullptr;
}

int AthleteTableModel::getRowForAthlete(int athleteId) const
{
    QMutexLocker locker(&m_dataMutex);
    for (int i = 0; i < m_athletes.size(); ++i) {
        if (m_athletes[i]->id() == athleteId) {  // Safe array access instead of .at()
            return i;
        }
    }
    return -1;
}

int AthleteTableModel::getHeightForColumn(int column) const
{
    QMutexLocker locker(&m_dataMutex);
    int heightIndex = column - FIXED_COLUMN_COUNT;
    if (heightIndex >= 0 && heightIndex < m_heightProgression.size()) {
        return m_heightProgression[heightIndex];  // Safe array access instead of .at()
    }
    return -1;
}

int AthleteTableModel::getColumnForHeight(int height) const
{
    QMutexLocker locker(&m_dataMutex);
    int heightIndex = m_heightProgression.indexOf(height);
    if (heightIndex >= 0) {
        return FIXED_COLUMN_COUNT + heightIndex;
    }
    return -1;
}

void AthleteTableModel::refreshData()
{
    if (!m_competition) {
        return;
    }

    qDebug() << "AthleteTableModel::refreshData: Refreshing data";

    beginResetModel();
    {
        QMutexLocker locker(&m_dataMutex);
        loadAthletes();
        loadAttempts();
        calculateRankings();
    }
    endResetModel();
}

void AthleteTableModel::onAthleteDataChanged(int athleteId)
{
    int row = getRowForAthlete(athleteId);
    if (row >= 0) {
        QModelIndex topLeft = createIndex(row, 0);
        QModelIndex bottomRight = createIndex(row, columnCount() - 1);
        emit dataChanged(topLeft, bottomRight);
    }
}

void AthleteTableModel::onRefreshTimer()
{
    // Periodic refresh - only if we have a competition loaded
    if (m_competition) {
        refreshData();
    }
}

void AthleteTableModel::loadAthletes()
{
    if (!m_competition || !m_dbManager) {
        return;
    }

    // TODO: Load athletes from database
    // For now, use athletes from competition object
    m_athletes = m_competition->getAthletes();

    qDebug() << "AthleteTableModel::loadAthletes: Loaded" << m_athletes.size() << "athletes";
}

void AthleteTableModel::loadAttempts()
{
    if (!m_competition || !m_dbManager) {
        return;
    }

    // Clear existing attempts
    m_attempts.clear();
    m_athleteBestHeights.clear();

    // NOTE: Database integration planned for v1.1
    // Currently using in-memory storage only for v1.0
    // Database integration will be added in future version
    // For now, initialize empty attempts
    for (Athlete *athlete : m_athletes) {
        int athleteId = athlete->id();
        m_athleteBestHeights[athleteId] = 0;

        // Initialize attempt structure
        for (int height : m_heightProgression) {
            for (int attempt = 1; attempt <= 3; ++attempt) {
                m_attempts[athleteId][height][attempt] = JumpAttempt::Invalid;
            }
        }
    }

    qDebug() << "AthleteTableModel::loadAttempts: Initialized attempts for" << m_athletes.size() << "athletes";
}

void AthleteTableModel::calculateRankings()
{
    if (m_athletes.isEmpty()) {
        return;
    }

    // Create ranking data structure
    struct RankingData {
        int athleteId;
        int bestHeight;
        int totalFailures;
        int failuresAtBest;

        bool operator<(const RankingData &other) const {
            // Primary: Best height (higher is better)
            if (bestHeight != other.bestHeight) {
                return bestHeight > other.bestHeight;
            }

            // Secondary: Failures at best height (fewer is better)
            if (failuresAtBest != other.failuresAtBest) {
                return failuresAtBest < other.failuresAtBest;
            }

            // Tertiary: Total failures (fewer is better)
            return totalFailures < other.totalFailures;
        }
    };

    QList<RankingData> rankingList;

    // Calculate ranking data for each athlete
    for (Athlete *athlete : m_athletes) {
        int athleteId = athlete->id();
        RankingData data;
        data.athleteId = athleteId;
        data.bestHeight = m_athleteBestHeights.value(athleteId, 0);
        data.totalFailures = 0;
        data.failuresAtBest = 0;

        // Count failures
        if (m_attempts.contains(athleteId)) {
            for (auto heightIt = m_attempts[athleteId].begin(); heightIt != m_attempts[athleteId].end(); ++heightIt) {
                int height = heightIt.key();
                for (auto attemptIt = heightIt.value().begin(); attemptIt != heightIt.value().end(); ++attemptIt) {
                    if (attemptIt.value() == JumpAttempt::Fail) {
                        data.totalFailures++;
                        if (height == data.bestHeight) {
                            data.failuresAtBest++;
                        }
                    }
                }
            }
        }

        rankingList.append(data);
    }

    // Sort by ranking criteria
    std::sort(rankingList.begin(), rankingList.end());

    // Assign ranks (handle ties)
    m_athleteRanks.clear();
    int currentRank = 1;
    for (int i = 0; i < rankingList.size(); ++i) {
        if (i > 0) {
            const RankingData &current = rankingList[i];
            const RankingData &previous = rankingList[i-1];

            // Check if this athlete has the same performance as previous
            if (current.bestHeight != previous.bestHeight ||
                current.failuresAtBest != previous.failuresAtBest ||
                current.totalFailures != previous.totalFailures) {
                currentRank = i + 1;
            }
            // If performance is identical, keep the same rank
        }

        m_athleteRanks[rankingList[i].athleteId] = currentRank;
    }

    qDebug() << "AthleteTableModel::calculateRankings: Calculated rankings for" << rankingList.size() << "athletes";
}

void AthleteTableModel::sortAthletesByRank()
{
    if (m_athletes.isEmpty()) {
        return;
    }

    // Sort athletes by their current rank
    std::sort(m_athletes.begin(), m_athletes.end(), [this](Athlete *a, Athlete *b) {
        int rankA = m_athleteRanks.value(a->id(), 999);
        int rankB = m_athleteRanks.value(b->id(), 999);
        return rankA < rankB;
    });
}

QString AthleteTableModel::formatAttemptDisplay(int athleteId, int height) const
{
    QStringList attempts;

    for (int attempt = 1; attempt <= 3; ++attempt) {
        JumpAttempt::AttemptResult result = getAttemptResult(athleteId, height, attempt);

        switch (result) {
        case JumpAttempt::Pass:
            attempts << "O";
            break;
        case JumpAttempt::Fail:
            attempts << "X";
            break;
        case JumpAttempt::Skip:
            attempts << "-";
            break;
        case JumpAttempt::Retire:
            attempts << "R";
            return attempts.join(" ");  // Stop after retire
        case JumpAttempt::Invalid:
        default:
            attempts << " ";
            break;
        }
    }

    return attempts.join(" ");
}

QVariant AthleteTableModel::getAttemptData(int athleteId, int height, int role) const
{
    // Check if athlete has any attempts at this height
    bool hasAttempts = false;
    JumpAttempt::AttemptResult lastResult = JumpAttempt::Invalid;

    for (int attempt = 1; attempt <= 3; ++attempt) {
        JumpAttempt::AttemptResult result = getAttemptResult(athleteId, height, attempt);
        if (result != JumpAttempt::Invalid) {
            hasAttempts = true;
            lastResult = result;
        }
    }

    if (!hasAttempts) {
        return QVariant();
    }

    // Return appropriate color based on the last/dominant result
    if (role == Qt::BackgroundRole) {
        // Check if athlete passed this height
        for (int attempt = 1; attempt <= 3; ++attempt) {
            if (getAttemptResult(athleteId, height, attempt) == JumpAttempt::Pass) {
                return QColor("#4CAF50");  // Green for success
            }
        }

        // Check for retire
        for (int attempt = 1; attempt <= 3; ++attempt) {
            if (getAttemptResult(athleteId, height, attempt) == JumpAttempt::Retire) {
                return QColor("#9E9E9E");  // Grey for retire
            }
        }

        // Check for skip
        bool allSkips = true;
        for (int attempt = 1; attempt <= 3; ++attempt) {
            JumpAttempt::AttemptResult result = getAttemptResult(athleteId, height, attempt);
            if (result != JumpAttempt::Skip && result != JumpAttempt::Invalid) {
                allSkips = false;
                break;
            }
        }
        if (allSkips) {
            return QColor("#FFC107");  // Yellow for skip
        }

        // Default to red for failures
        return QColor("#F44336");  // Red for failure
    }

    return QVariant();
}

bool AthleteTableModel::isHeightColumn(int column) const
{
    return column >= FIXED_COLUMN_COUNT && column < (FIXED_COLUMN_COUNT + m_heightProgression.size());
}

int AthleteTableModel::getFixedColumnCount() const
{
    return FIXED_COLUMN_COUNT;
}
