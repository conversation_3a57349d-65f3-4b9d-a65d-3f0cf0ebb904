#ifndef RANKING_CALCULATOR_H
#define RANKING_CALCULATOR_H

#include <QObject>
#include <QList>
#include <QHash>
#include <QDateTime>

class Athlete;
class Competition;
class JumpAttempt;

/**
 * @brief 跳高比赛排名计算器
 * 
 * 这个类负责根据国际田联跳高比赛规则计算运动员排名。
 * 实现了完整的跳高排名算法，包括处理并列情况和决胜规则。
 * 
 * 跳高排名规则（按优先级）：
 * 1. 最佳成绩（越高越好）
 * 2. 在最佳成绩高度上的失败次数（越少越好）
 * 3. 整个比赛中的总失败次数（越少越好）
 * 4. 如果仍然并列，则并列排名
 * 
 * 特殊情况处理：
 * - 退赛运动员排在所有完成比赛的运动员之后
 * - 未开始比赛的运动员不参与排名
 * - 免跳不计入失败次数
 */
class RankingCalculator : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 运动员排名数据结构
     */
    struct AthleteRanking {
        int athleteId;              // 运动员ID
        int rank;                   // 排名（1-based）
        int bestHeight;             // 最佳成绩（厘米）
        int failuresAtBest;         // 在最佳成绩高度的失败次数
        int totalFailures;          // 总失败次数
        int totalAttempts;          // 总试跳次数
        bool isRetired;             // 是否退赛
        bool hasStarted;            // 是否已开始比赛
        QDateTime lastAttemptTime;  // 最后试跳时间
        
        // 比较运算符用于排序
        bool operator<(const AthleteRanking &other) const;
        bool operator==(const AthleteRanking &other) const;
    };

    /**
     * @brief 比赛排名结果
     */
    struct RankingResult {
        QList<AthleteRanking> rankings;     // 排名列表
        QDateTime calculationTime;          // 计算时间
        int totalAthletes;                  // 总运动员数
        int activeAthletes;                 // 活跃运动员数
        int retiredAthletes;                // 退赛运动员数
        int completedAthletes;              // 完成比赛运动员数
        bool isCompetitionFinished;         // 比赛是否结束
    };

    explicit RankingCalculator(QObject *parent = nullptr);
    ~RankingCalculator();

    // 主要计算方法
    /**
     * @brief 计算比赛排名
     * @param competition 比赛对象
     * @return 排名结果
     */
    RankingResult calculateRankings(Competition *competition);

    /**
     * @brief 计算运动员排名（基于试跳数据）
     * @param athletes 运动员列表
     * @param attempts 试跳数据 (athleteId -> height -> attemptNumber -> result)
     * @param heightProgression 高度序列
     * @return 排名结果
     */
    RankingResult calculateRankings(const QList<Athlete*> &athletes,
                                   const QHash<int, QHash<int, QHash<int, int>>> &attempts,
                                   const QList<int> &heightProgression);

    // 单个运动员分析
    /**
     * @brief 计算单个运动员的排名数据
     * @param athleteId 运动员ID
     * @param attempts 该运动员的试跳数据
     * @param heightProgression 高度序列
     * @return 运动员排名数据
     */
    AthleteRanking calculateAthleteRanking(int athleteId,
                                          const QHash<int, QHash<int, int>> &attempts,
                                          const QList<int> &heightProgression);

    /**
     * @brief 获取运动员最佳成绩
     * @param athleteId 运动员ID
     * @param attempts 试跳数据
     * @return 最佳成绩（厘米），0表示无成绩
     */
    int getBestHeight(int athleteId, const QHash<int, QHash<int, int>> &attempts);

    /**
     * @brief 计算失败次数
     * @param attempts 试跳数据
     * @param height 指定高度（-1表示所有高度）
     * @return 失败次数
     */
    int countFailures(const QHash<int, QHash<int, int>> &attempts, int height = -1);

    /**
     * @brief 检查运动员是否退赛
     * @param attempts 试跳数据
     * @return 是否退赛
     */
    bool isAthleteRetired(const QHash<int, QHash<int, int>> &attempts);

    /**
     * @brief 检查运动员是否已开始比赛
     * @param attempts 试跳数据
     * @return 是否已开始
     */
    bool hasAthleteStarted(const QHash<int, QHash<int, int>> &attempts);

    // 比赛状态分析
    /**
     * @brief 检查比赛是否结束
     * @param rankings 排名结果
     * @param currentHeight 当前高度
     * @return 比赛是否结束
     */
    bool isCompetitionFinished(const RankingResult &rankings, int currentHeight);

    /**
     * @brief 获取活跃运动员数量
     * @param rankings 排名结果
     * @return 活跃运动员数量
     */
    int getActiveAthleteCount(const RankingResult &rankings);

    /**
     * @brief 获取下一个建议高度
     * @param rankings 排名结果
     * @param heightProgression 高度序列
     * @param currentHeight 当前高度
     * @return 建议的下一个高度，-1表示比赛应该结束
     */
    int getNextSuggestedHeight(const RankingResult &rankings,
                              const QList<int> &heightProgression,
                              int currentHeight);

    // 配置选项
    /**
     * @brief 设置是否启用严格排名模式
     * @param strict 是否严格模式（不允许并列）
     */
    void setStrictRankingMode(bool strict);

    /**
     * @brief 设置最大并列数量
     * @param maxTies 最大并列数量
     */
    void setMaxTiedRanks(int maxTies);

    /**
     * @brief 设置是否考虑试跳时间
     * @param considerTime 是否考虑时间因素
     */
    void setConsiderAttemptTime(bool considerTime);

signals:
    /**
     * @brief 排名计算完成
     * @param result 排名结果
     */
    void rankingsCalculated(const RankingResult &result);

    /**
     * @brief 比赛状态变化
     * @param activeAthletes 活跃运动员数
     * @param completedAthletes 完成运动员数
     * @param isFinished 是否结束
     */
    void competitionStateChanged(int activeAthletes, int completedAthletes, bool isFinished);

private:
    // 内部计算方法
    void sortRankings(QList<AthleteRanking> &rankings);
    void assignRanks(QList<AthleteRanking> &rankings);
    void handleTiedRanks(QList<AthleteRanking> &rankings);
    
    // 辅助方法
    bool isValidAttemptResult(int result);
    int convertToJumpAttemptResult(int result);
    QDateTime getLastAttemptTime(const QHash<int, QHash<int, int>> &attempts);
    
    // 配置选项
    bool m_strictRankingMode;
    int m_maxTiedRanks;
    bool m_considerAttemptTime;
    
    // 常量
    static const int PASS_RESULT = 0;      // 成功
    static const int FAIL_RESULT = 1;      // 失败
    static const int SKIP_RESULT = 2;      // 免跳
    static const int RETIRE_RESULT = 3;    // 退赛
    static const int INVALID_RESULT = -1;  // 无效
};

// 使排名数据结构可以在Qt信号槽中使用
Q_DECLARE_METATYPE(RankingCalculator::AthleteRanking)
Q_DECLARE_METATYPE(RankingCalculator::RankingResult)

#endif // RANKING_CALCULATOR_H
