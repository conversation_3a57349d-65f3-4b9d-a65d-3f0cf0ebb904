#include "athlete_delegate.h"
#include "models/athlete_table_model.h"
#include "models/jump_attempt.h"
#include <QPainter>
#include <QApplication>
#include <QMouseEvent>
#include <QKeyEvent>
#include <QDebug>
#include <QStyleOptionViewItem>
#include <QAbstractItemView>

// Default color constants
const QColor AthleteDelegate::DEFAULT_SUCCESS_BG = QColor("#4CAF50");  // Green
const QColor AthleteDelegate::DEFAULT_FAILURE_BG = QColor("#F44336");  // Red
const QColor AthleteDelegate::DEFAULT_SKIP_BG = QColor("#FFC107");     // Yellow
const QColor AthleteDelegate::DEFAULT_RETIRE_BG = QColor("#9E9E9E");   // Grey
const QColor AthleteDelegate::DEFAULT_EMPTY_BG = QColor("#FFFFFF");    // White
const QColor AthleteDelegate::DEFAULT_TEXT_COLOR = QColor("#000000");  // Black
const QColor AthleteDelegate::DEFAULT_FOCUS_COLOR = QColor("#2196F3"); // Blue

AthleteDelegate::AthleteDelegate(QObject *parent)
    : QStyledItemDelegate(parent)
    , m_model(nullptr)
    , m_contextMenu(nullptr)
    , m_successAction(nullptr)
    , m_failureAction(nullptr)
    , m_skipAction(nullptr)
    , m_retireAction(nullptr)
    , m_clearAction(nullptr)
    , m_contextMenuEnabled(true)
    , m_animationsEnabled(true)
    , m_animationTimer(new QTimer(this))
    , m_animationValue(0.0)
{
    // Initialize default colors
    m_backgroundColors[Success] = DEFAULT_SUCCESS_BG;
    m_backgroundColors[Failure] = DEFAULT_FAILURE_BG;
    m_backgroundColors[Skip] = DEFAULT_SKIP_BG;
    m_backgroundColors[Retire] = DEFAULT_RETIRE_BG;
    m_backgroundColors[Empty] = DEFAULT_EMPTY_BG;
    
    m_textColors[Success] = DEFAULT_TEXT_COLOR;
    m_textColors[Failure] = DEFAULT_TEXT_COLOR;
    m_textColors[Skip] = DEFAULT_TEXT_COLOR;
    m_textColors[Retire] = DEFAULT_TEXT_COLOR;
    m_textColors[Empty] = DEFAULT_TEXT_COLOR;
    
    // Setup animation timer
    m_animationTimer->setInterval(16); // ~60 FPS
    connect(m_animationTimer, &QTimer::timeout, this, &AthleteDelegate::onAnimationTimer);
    
    // Create context menu
    createContextMenu();
    
    qDebug() << "AthleteDelegate: Initialized";
}

AthleteDelegate::~AthleteDelegate()
{
    qDebug() << "AthleteDelegate: Destroyed";
}

void AthleteDelegate::paint(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const
{
    if (!index.isValid()) {
        QStyledItemDelegate::paint(painter, option, index);
        return;
    }
    
    painter->save();
    
    // Paint background
    paintBackground(painter, option, index);
    
    // Paint content based on column type
    if (isHeightColumn(index)) {
        paintHeightColumn(painter, option, index);
    } else {
        paintFixedColumn(painter, option, index);
    }
    
    // Paint focus indicator
    if (option.state & QStyle::State_HasFocus) {
        paintFocusIndicator(painter, option);
    }
    
    painter->restore();
}

QSize AthleteDelegate::sizeHint(const QStyleOptionViewItem &option, const QModelIndex &index) const
{
    Q_UNUSED(option)
    Q_UNUSED(index)
    
    // Return consistent size for all cells
    return QSize(MIN_CELL_WIDTH, MIN_CELL_HEIGHT);
}

QWidget* AthleteDelegate::createEditor(QWidget *parent, const QStyleOptionViewItem &option, const QModelIndex &index) const
{
    Q_UNUSED(parent)
    Q_UNUSED(option)
    Q_UNUSED(index)
    
    // We don't use traditional editors - editing is done through context menu
    return nullptr;
}

void AthleteDelegate::setEditorData(QWidget *editor, const QModelIndex &index) const
{
    Q_UNUSED(editor)
    Q_UNUSED(index)
    // Not used - editing through context menu
}

void AthleteDelegate::setModelData(QWidget *editor, QAbstractItemModel *model, const QModelIndex &index) const
{
    Q_UNUSED(editor)
    Q_UNUSED(model)
    Q_UNUSED(index)
    // Not used - editing through context menu
}

void AthleteDelegate::updateEditorGeometry(QWidget *editor, const QStyleOptionViewItem &option, const QModelIndex &index) const
{
    Q_UNUSED(editor)
    Q_UNUSED(option)
    Q_UNUSED(index)
    // Not used - editing through context menu
}

bool AthleteDelegate::editorEvent(QEvent *event, QAbstractItemModel *model, const QStyleOptionViewItem &option, const QModelIndex &index)
{
    Q_UNUSED(model)
    Q_UNUSED(option)
    
    if (!index.isValid() || !isHeightColumn(index)) {
        return false;
    }
    
    // Handle mouse events for context menu
    if (event->type() == QEvent::MouseButtonPress) {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
        if (mouseEvent->button() == Qt::RightButton && m_contextMenuEnabled) {
            m_contextMenuIndex = index;
            
            // Show context menu at mouse position
            if (QAbstractItemView *view = qobject_cast<QAbstractItemView*>(parent())) {
                QPoint globalPos = view->viewport()->mapToGlobal(mouseEvent->pos());
                showContextMenu(globalPos, index);
                return true;
            }
        }
    }
    
    // Handle focus changes
    if (event->type() == QEvent::FocusIn) {
        if (m_model && isHeightColumn(index)) {
            int athleteId = index.data(AthleteTableModel::AthleteIdRole).toInt();
            int height = index.data(AthleteTableModel::HeightRole).toInt();
            emit cellFocusChanged(athleteId, height);
        }
    }
    
    return QStyledItemDelegate::editorEvent(event, model, option, index);
}

void AthleteDelegate::setAthleteTableModel(AthleteTableModel *model)
{
    m_model = model;
    qDebug() << "AthleteDelegate::setAthleteTableModel: Model set";
}

void AthleteDelegate::setContextMenuEnabled(bool enabled)
{
    m_contextMenuEnabled = enabled;
    qDebug() << "AthleteDelegate::setContextMenuEnabled:" << enabled;
}

void AthleteDelegate::setResultColors(AttemptResult result, const QColor &backgroundColor, const QColor &textColor)
{
    m_backgroundColors[result] = backgroundColor;
    m_textColors[result] = textColor;
    qDebug() << "AthleteDelegate::setResultColors: Updated colors for result" << static_cast<int>(result);
}

void AthleteDelegate::setAnimationsEnabled(bool enabled)
{
    m_animationsEnabled = enabled;
    if (!enabled && m_animationTimer->isActive()) {
        m_animationTimer->stop();
    }
    qDebug() << "AthleteDelegate::setAnimationsEnabled:" << enabled;
}

void AthleteDelegate::paintFixedColumn(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const
{
    // Use default painting for fixed columns
    QStyleOptionViewItem opt = option;
    
    // Get text data
    QString text = index.data(Qt::DisplayRole).toString();
    
    // Set font based on column type
    QFont font = opt.font;
    if (index.column() == AthleteTableModel::RankColumn) {
        // Check if this is the leader
        int rank = index.data(Qt::DisplayRole).toInt();
        if (rank == 1) {
            font.setBold(true);
        }
    }
    
    painter->setFont(font);
    painter->setPen(m_textColors[Empty]);
    
    // Draw text centered
    QRect textRect = opt.rect.adjusted(CELL_PADDING, 0, -CELL_PADDING, 0);
    painter->drawText(textRect, Qt::AlignCenter, text);
}

void AthleteDelegate::paintHeightColumn(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const
{
    // Get attempt data
    QString attempts = index.data(Qt::DisplayRole).toString();
    
    // Paint attempt results
    paintAttemptResults(painter, option.rect, attempts, index);
}

void AthleteDelegate::paintAttemptResults(QPainter *painter, const QRect &rect, const QString &attempts, const QModelIndex &index) const
{
    Q_UNUSED(index)
    
    // Parse attempts string (e.g., "O X -")
    QStringList attemptList = attempts.split(' ', Qt::SkipEmptyParts);
    
    // Calculate cell dimensions for 3 attempts
    int cellWidth = rect.width() / 3;
    int cellHeight = rect.height();
    
    // Paint each attempt
    for (int i = 0; i < 3; ++i) {
        QRect attemptRect(rect.x() + i * cellWidth, rect.y(), cellWidth, cellHeight);
        
        AttemptResult result = Empty;
        QString symbol = " ";
        
        if (i < attemptList.size()) {
            symbol = attemptList[i];
            result = parseAttemptResult(symbol.at(0));
        }
        
        // Paint background
        painter->fillRect(attemptRect, getBackgroundColor(result));
        
        // Paint text
        painter->setPen(getTextColor(result));
        QFont font = painter->font();
        font.setBold(result == Success || result == Failure);
        font.setItalic(result == Retire);
        painter->setFont(font);
        
        painter->drawText(attemptRect, Qt::AlignCenter, symbol);
    }
}

void AthleteDelegate::paintBackground(QPainter *painter, const QStyleOptionViewItem &option, const QModelIndex &index) const
{
    // Paint selection background
    if (option.state & QStyle::State_Selected) {
        painter->fillRect(option.rect, option.palette.highlight());
        return;
    }
    
    // For height columns, background is handled in paintAttemptResults
    if (isHeightColumn(index)) {
        return;
    }
    
    // Paint default background for fixed columns
    QColor bgColor = DEFAULT_EMPTY_BG;
    
    // Alternate row colors
    if (index.row() % 2 == 1) {
        bgColor = bgColor.darker(105);
    }
    
    painter->fillRect(option.rect, bgColor);
}

void AthleteDelegate::paintFocusIndicator(QPainter *painter, const QStyleOptionViewItem &option) const
{
    // Paint focus border
    QPen focusPen(DEFAULT_FOCUS_COLOR, FOCUS_BORDER_WIDTH);
    painter->setPen(focusPen);
    painter->setBrush(Qt::NoBrush);

    QRect focusRect = option.rect.adjusted(1, 1, -1, -1);
    painter->drawRect(focusRect);
}

AthleteDelegate::AttemptResult AthleteDelegate::parseAttemptResult(const QChar &symbol) const
{
    switch (symbol.toLatin1()) {
    case 'O':
    case 'o':
        return Success;
    case 'X':
    case 'x':
        return Failure;
    case '-':
        return Skip;
    case 'R':
    case 'r':
        return Retire;
    default:
        return Empty;
    }
}

QChar AthleteDelegate::getAttemptSymbol(AttemptResult result) const
{
    switch (result) {
    case Success:
        return 'O';
    case Failure:
        return 'X';
    case Skip:
        return '-';
    case Retire:
        return 'R';
    case Empty:
    default:
        return ' ';
    }
}

QColor AthleteDelegate::getBackgroundColor(AttemptResult result) const
{
    return m_backgroundColors.value(result, DEFAULT_EMPTY_BG);
}

QColor AthleteDelegate::getTextColor(AttemptResult result) const
{
    return m_textColors.value(result, DEFAULT_TEXT_COLOR);
}

bool AthleteDelegate::isHeightColumn(const QModelIndex &index) const
{
    if (!m_model) {
        return index.column() >= AthleteTableModel::FirstHeightColumn;
    }

    return index.column() >= AthleteTableModel::FirstHeightColumn &&
           index.column() < m_model->columnCount();
}

bool AthleteDelegate::isEditableCell(const QModelIndex &index) const
{
    return isHeightColumn(index);
}

void AthleteDelegate::showContextMenu(const QPoint &position, const QModelIndex &index)
{
    if (!m_contextMenu || !isEditableCell(index)) {
        return;
    }

    m_contextMenuIndex = index;

    // Update action states based on current cell content
    QString currentData = index.data(Qt::DisplayRole).toString();
    bool hasData = !currentData.isEmpty() && currentData.trimmed() != " ";

    m_clearAction->setEnabled(hasData);

    // Show menu
    m_contextMenu->exec(position);
}

void AthleteDelegate::createContextMenu()
{
    m_contextMenu = new QMenu();

    // Create actions
    m_successAction = m_contextMenu->addAction(tr("成功 (O)"));
    m_successAction->setIcon(QIcon()); // TODO: Add icons
    m_successAction->setData(static_cast<int>(Success));

    m_failureAction = m_contextMenu->addAction(tr("失败 (X)"));
    m_failureAction->setData(static_cast<int>(Failure));

    m_skipAction = m_contextMenu->addAction(tr("免跳 (-)"));
    m_skipAction->setData(static_cast<int>(Skip));

    m_retireAction = m_contextMenu->addAction(tr("退赛 (R)"));
    m_retireAction->setData(static_cast<int>(Retire));

    m_contextMenu->addSeparator();

    m_clearAction = m_contextMenu->addAction(tr("清除"));
    m_clearAction->setData(static_cast<int>(Empty));

    // Connect actions
    connect(m_successAction, &QAction::triggered, this, &AthleteDelegate::onContextMenuTriggered);
    connect(m_failureAction, &QAction::triggered, this, &AthleteDelegate::onContextMenuTriggered);
    connect(m_skipAction, &QAction::triggered, this, &AthleteDelegate::onContextMenuTriggered);
    connect(m_retireAction, &QAction::triggered, this, &AthleteDelegate::onContextMenuTriggered);
    connect(m_clearAction, &QAction::triggered, this, &AthleteDelegate::onContextMenuTriggered);
}

void AthleteDelegate::onContextMenuTriggered()
{
    QAction *action = qobject_cast<QAction*>(sender());
    if (!action || !m_contextMenuIndex.isValid() || !m_model) {
        return;
    }

    AttemptResult result = static_cast<AttemptResult>(action->data().toInt());

    // Get athlete and height information
    int athleteId = m_contextMenuIndex.data(AthleteTableModel::AthleteIdRole).toInt();
    int height = m_contextMenuIndex.data(AthleteTableModel::HeightRole).toInt();

    if (athleteId <= 0 || height <= 0) {
        qWarning() << "AthleteDelegate::onContextMenuTriggered: Invalid athlete ID or height";
        return;
    }

    // For now, assume attempt number 1 (this should be determined by existing attempts)
    int attemptNumber = 1; // TODO: Calculate based on existing attempts

    // Convert to JumpAttempt::AttemptResult
    JumpAttempt::AttemptResult jumpResult;
    switch (result) {
    case Success:
        jumpResult = JumpAttempt::Pass;
        break;
    case Failure:
        jumpResult = JumpAttempt::Fail;
        break;
    case Skip:
        jumpResult = JumpAttempt::Skip;
        break;
    case Retire:
        jumpResult = JumpAttempt::Retire;
        break;
    case Empty:
    default:
        jumpResult = JumpAttempt::Invalid;
        break;
    }

    qDebug() << "AthleteDelegate::onContextMenuTriggered: Recording attempt for athlete"
             << athleteId << "at height" << height << "attempt" << attemptNumber
             << "result" << static_cast<int>(jumpResult);

    emit attemptRequested(athleteId, height, attemptNumber, result);

    // Start animation if enabled
    if (m_animationsEnabled) {
        m_animatedIndex = m_contextMenuIndex;
        m_animationValue = 0.0;
        m_animationTimer->start();
    }
}

void AthleteDelegate::onAnimationTimer()
{
    m_animationValue += 0.1;

    if (m_animationValue >= 1.0) {
        m_animationTimer->stop();
        m_animationValue = 0.0;
        m_animatedIndex = QModelIndex();
    }

    // Trigger repaint of animated cell
    if (m_animatedIndex.isValid()) {
        if (QAbstractItemView *view = qobject_cast<QAbstractItemView*>(parent())) {
            view->update(m_animatedIndex);
        }
    }
}
