#include <QtTest/QtTest>
#include <QJsonArray>
#include <QJsonObject>
#include <QSignalSpy>
#include <QListWidget>
#include <QPushButton>
#include "ui/competition_selection_view.h"

class TestCompetitionSelectionView : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void init();
    void cleanup();
    void cleanupTestCase();
    
    // 界面初始化测试
    void testUIInitialization();
    void testInitialState();
    
    // 比赛列表显示功能测试
    void testLoadCompetitions();
    void testLoadEmptyCompetitions();
    void testClearCompetitions();
    void testCompetitionFormatting();
    
    // 用户选择交互测试
    void testSelectionByClick();
    void testSelectionByDoubleClick();
    void testSelectionCancellation();
    void testButtonStateUpdates();
    
    // 信号槽连接测试
    void testSignalEmission();

    // 新功能测试 - 加载状态和错误处理
    void testLoadingState();
    void testErrorDisplay();
    void testOfflineMode();
    void testRetryFunctionality();

private:
    CompetitionSelectionView *m_view;
    QJsonArray createTestCompetitions();
    QJsonObject createTestCompetition(int id, const QString &name, 
                                    const QString &status = "not_started");
};

void TestCompetitionSelectionView::initTestCase()
{
    // 整个测试类开始前的初始化
}

void TestCompetitionSelectionView::init()
{
    // 每个测试方法开始前的初始化
    m_view = new CompetitionSelectionView();
}

void TestCompetitionSelectionView::cleanup()
{
    // 每个测试方法结束后的清理
    delete m_view;
    m_view = nullptr;
}

void TestCompetitionSelectionView::cleanupTestCase()
{
    // 整个测试类结束后的清理
}

void TestCompetitionSelectionView::testUIInitialization()
{
    QVERIFY(m_view != nullptr);
    
    // 检查主要UI组件是否存在
    auto titleLabel = m_view->findChild<QLabel*>();
    QVERIFY(titleLabel != nullptr);
    
    auto competitionsList = m_view->findChild<QListWidget*>();
    QVERIFY(competitionsList != nullptr);
    
    auto selectButton = m_view->findChild<QPushButton*>("选择比赛");
    auto cancelButton = m_view->findChild<QPushButton*>("取消");
    
    // 检查布局设置
    QVERIFY(competitionsList->selectionMode() == QAbstractItemView::SingleSelection);
    QVERIFY(competitionsList->alternatingRowColors());
}

void TestCompetitionSelectionView::testInitialState()
{
    // 检查初始状态
    QCOMPARE(m_view->selectedCompetitionId(), -1);
    
    auto competitionsList = m_view->findChild<QListWidget*>();
    QCOMPARE(competitionsList->count(), 0);
}

void TestCompetitionSelectionView::testLoadCompetitions()
{
    QJsonArray competitions = createTestCompetitions();
    m_view->loadCompetitions(competitions);
    
    auto competitionsList = m_view->findChild<QListWidget*>();
    QCOMPARE(competitionsList->count(), competitions.size());
    
    // 检查第一个项目的数据
    QListWidgetItem *firstItem = competitionsList->item(0);
    QVERIFY(firstItem != nullptr);
    QCOMPARE(firstItem->data(Qt::UserRole).toInt(), 1);
}

void TestCompetitionSelectionView::testLoadEmptyCompetitions()
{
    QJsonArray emptyCompetitions;
    m_view->loadCompetitions(emptyCompetitions);
    
    auto competitionsList = m_view->findChild<QListWidget*>();
    QCOMPARE(competitionsList->count(), 0);
    QCOMPARE(m_view->selectedCompetitionId(), -1);
}

void TestCompetitionSelectionView::testClearCompetitions()
{
    // 先加载比赛数据
    m_view->loadCompetitions(createTestCompetitions());
    
    auto competitionsList = m_view->findChild<QListWidget*>();
    QVERIFY(competitionsList->count() > 0);
    
    // 清空
    m_view->clearCompetitions();
    QCOMPARE(competitionsList->count(), 0);
    QCOMPARE(m_view->selectedCompetitionId(), -1);
}

void TestCompetitionSelectionView::testCompetitionFormatting()
{
    QJsonObject competition = createTestCompetition(1, "测试比赛", "in_progress");
    competition["venue"] = "测试场地";
    competition["date"] = "2024-08-15T10:00:00Z";
    
    QJsonArray competitions;
    competitions.append(competition);
    
    m_view->loadCompetitions(competitions);
    
    auto competitionsList = m_view->findChild<QListWidget*>();
    QListWidgetItem *item = competitionsList->item(0);
    QVERIFY(item != nullptr);
    
    QString itemText = item->text();
    QVERIFY(itemText.contains("测试比赛"));
    QVERIFY(itemText.contains("测试场地"));
    QVERIFY(itemText.contains("进行中"));
}

void TestCompetitionSelectionView::testSelectionByClick()
{
    m_view->loadCompetitions(createTestCompetitions());
    
    auto competitionsList = m_view->findChild<QListWidget*>();
    
    // 模拟点击第一个项目
    competitionsList->setCurrentRow(0);
    
    QCOMPARE(m_view->selectedCompetitionId(), 1);
}

void TestCompetitionSelectionView::testSelectionByDoubleClick()
{
    QSignalSpy spy(m_view, &CompetitionSelectionView::competitionSelected);
    
    m_view->loadCompetitions(createTestCompetitions());
    
    auto competitionsList = m_view->findChild<QListWidget*>();
    competitionsList->setCurrentRow(0);
    
    // 模拟双击
    QListWidgetItem *item = competitionsList->item(0);
    competitionsList->itemDoubleClicked(item);
    
    QCOMPARE(spy.count(), 1);
    QCOMPARE(spy.takeFirst().at(0).toInt(), 1);
}

void TestCompetitionSelectionView::testSelectionCancellation()
{
    QSignalSpy spy(m_view, &CompetitionSelectionView::selectionCancelled);
    
    // 查找取消按钮并点击
    auto buttons = m_view->findChildren<QPushButton*>();
    QPushButton *cancelButton = nullptr;
    
    for (auto button : buttons) {
        if (button->text().contains("取消")) {
            cancelButton = button;
            break;
        }
    }
    
    QVERIFY(cancelButton != nullptr);
    cancelButton->click();
    
    QCOMPARE(spy.count(), 1);
}

void TestCompetitionSelectionView::testButtonStateUpdates()
{
    m_view->loadCompetitions(createTestCompetitions());
    
    auto buttons = m_view->findChildren<QPushButton*>();
    QPushButton *selectButton = nullptr;
    
    for (auto button : buttons) {
        if (button->text().contains("选择")) {
            selectButton = button;
            break;
        }
    }
    
    QVERIFY(selectButton != nullptr);
    
    // 初始状态下选择按钮应该被禁用
    QVERIFY(!selectButton->isEnabled());
    
    // 选择一个项目后按钮应该启用
    auto competitionsList = m_view->findChild<QListWidget*>();
    competitionsList->setCurrentRow(0);
    
    QVERIFY(selectButton->isEnabled());
}

void TestCompetitionSelectionView::testSignalEmission()
{
    QSignalSpy selectionSpy(m_view, &CompetitionSelectionView::competitionSelected);
    QSignalSpy cancellationSpy(m_view, &CompetitionSelectionView::selectionCancelled);
    
    m_view->loadCompetitions(createTestCompetitions());
    
    auto competitionsList = m_view->findChild<QListWidget*>();
    competitionsList->setCurrentRow(0);
    
    // 测试选择信号
    auto buttons = m_view->findChildren<QPushButton*>();
    for (auto button : buttons) {
        if (button->text().contains("选择")) {
            button->click();
            break;
        }
    }
    
    QCOMPARE(selectionSpy.count(), 1);
    QCOMPARE(selectionSpy.takeFirst().at(0).toInt(), 1);
    
    // 测试取消信号
    for (auto button : buttons) {
        if (button->text().contains("取消")) {
            button->click();
            break;
        }
    }
    
    QCOMPARE(cancellationSpy.count(), 1);
}

QJsonArray TestCompetitionSelectionView::createTestCompetitions()
{
    QJsonArray competitions;
    
    competitions.append(createTestCompetition(1, "2024年全国跳高锦标赛", "not_started"));
    competitions.append(createTestCompetition(2, "区域选拔赛", "in_progress"));
    competitions.append(createTestCompetition(3, "青年组比赛", "finished"));
    
    return competitions;
}

QJsonObject TestCompetitionSelectionView::createTestCompetition(int id, const QString &name, const QString &status)
{
    QJsonObject competition;
    competition["id"] = id;
    competition["name"] = name;
    competition["date"] = "2024-08-15T10:00:00Z";
    competition["venue"] = "国家体育场";
    competition["status"] = status;

    return competition;
}

void TestCompetitionSelectionView::testLoadingState()
{
    // 测试显示加载状态
    m_view->showLoadingState(true, "正在加载测试数据...");

    // 检查进度条是否可见
    auto progressBar = m_view->findChild<QProgressBar*>();
    QVERIFY(progressBar != nullptr);
    QVERIFY(progressBar->isVisible());

    // 检查状态标签
    auto statusLabel = m_view->findChild<QLabel*>();
    bool foundStatusLabel = false;
    for (auto label : m_view->findChildren<QLabel*>()) {
        if (label->text().contains("正在加载测试数据")) {
            foundStatusLabel = true;
            QVERIFY(label->isVisible());
            break;
        }
    }
    QVERIFY(foundStatusLabel);

    // 检查列表是否被禁用
    auto competitionsList = m_view->findChild<QListWidget*>();
    QVERIFY(!competitionsList->isEnabled());

    // 测试隐藏加载状态
    m_view->showLoadingState(false);
    QVERIFY(!progressBar->isVisible());
    QVERIFY(competitionsList->isEnabled());
}

void TestCompetitionSelectionView::testErrorDisplay()
{
    QString errorMessage = "测试错误消息";
    m_view->showError(errorMessage, true);

    // 检查错误标签是否显示
    bool foundErrorLabel = false;
    for (auto label : m_view->findChildren<QLabel*>()) {
        if (label->text().contains(errorMessage)) {
            foundErrorLabel = true;
            QVERIFY(label->isVisible());
            break;
        }
    }
    QVERIFY(foundErrorLabel);

    // 检查重试按钮是否可见
    bool foundRetryButton = false;
    for (auto button : m_view->findChildren<QPushButton*>()) {
        if (button->text().contains("重试")) {
            foundRetryButton = true;
            QVERIFY(button->isVisible());
            break;
        }
    }
    QVERIFY(foundRetryButton);

    // 检查列表是否被禁用
    auto competitionsList = m_view->findChild<QListWidget*>();
    QVERIFY(!competitionsList->isEnabled());
}

void TestCompetitionSelectionView::testOfflineMode()
{
    m_view->showOfflineMode();

    // 检查是否显示离线模式消息
    bool foundOfflineMessage = false;
    for (auto label : m_view->findChildren<QLabel*>()) {
        if (label->text().contains("网络连接不可用")) {
            foundOfflineMessage = true;
            QVERIFY(label->isVisible());
            break;
        }
    }
    QVERIFY(foundOfflineMessage);

    // 检查重试按钮是否可见
    bool foundRetryButton = false;
    for (auto button : m_view->findChildren<QPushButton*>()) {
        if (button->text().contains("重试")) {
            foundRetryButton = true;
            QVERIFY(button->isVisible());
            break;
        }
    }
    QVERIFY(foundRetryButton);
}

void TestCompetitionSelectionView::testRetryFunctionality()
{
    QSignalSpy retrySpy(m_view, &CompetitionSelectionView::retryRequested);

    // 显示错误状态以显示重试按钮
    m_view->showError("测试错误", true);

    // 查找并点击重试按钮
    QPushButton *retryButton = nullptr;
    for (auto button : m_view->findChildren<QPushButton*>()) {
        if (button->text().contains("重试")) {
            retryButton = button;
            break;
        }
    }

    QVERIFY(retryButton != nullptr);
    QVERIFY(retryButton->isVisible());

    // 点击重试按钮
    retryButton->click();

    // 检查是否发出了重试信号
    QCOMPARE(retrySpy.count(), 1);
}

QTEST_MAIN(TestCompetitionSelectionView)
#include "test_competition_selection_view.moc"