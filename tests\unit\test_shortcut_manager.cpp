#include <QtTest/QtTest>
#include <QTableView>
#include <QApplication>
#include <QSignalSpy>
#include <QKeyEvent>

#include "ui/shortcut_manager.h"
#include "models/athlete_table_model.h"
#include "models/athlete.h"
#include "models/competition.h"

/**
 * @brief Unit tests for ShortcutManager
 * 
 * Tests the keyboard shortcut functionality including:
 * - Shortcut creation and management
 * - Attempt recording shortcuts
 * - Navigation shortcuts
 * - Undo/redo functionality
 * - Context-sensitive shortcut enabling
 */
class TestShortcutManager : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void init();
    void cleanup();
    void cleanupTestCase();
    
    // Basic functionality tests
    void testShortcutManagerCreation();
    void testTableViewAssignment();
    void testTableModelAssignment();
    void testEnableDisable();
    
    // Shortcut configuration tests
    void testDefaultShortcuts();
    void testCustomShortcuts();
    void testResetToDefaults();
    
    // Undo/redo tests
    void testUndoRedoState();
    void testUndoStackManagement();
    void testOperationRecording();
    
    // Context tests
    void testContextSensitiveShortcuts();
    void testValidCellDetection();
    void testHeightColumnDetection();
    
    // Signal emission tests
    void testAttemptRecordSignals();
    void testNavigationSignals();
    void testUndoRedoSignals();

private:
    ShortcutManager *m_shortcutManager;
    AthleteTableModel *m_tableModel;
    QTableView *m_tableView;
    Competition *m_competition;
    QList<Athlete*> m_athletes;
    
    void createTestData();
    Athlete* createAthlete(int id, const QString &firstName, const QString &lastName, int number);
};

void TestShortcutManager::initTestCase()
{
    // Ensure QApplication exists for widget tests
    if (!QApplication::instance()) {
        int argc = 0;
        char **argv = nullptr;
        new QApplication(argc, argv);
    }
}

void TestShortcutManager::init()
{
    // Create test components
    m_shortcutManager = new ShortcutManager(this);
    m_tableModel = new AthleteTableModel(this);
    m_tableView = new QTableView(this);
    
    // Create test data
    createTestData();
    
    // Setup components
    m_tableView->setModel(m_tableModel);
    m_shortcutManager->setTableView(m_tableView);
    m_shortcutManager->setTableModel(m_tableModel);
    
    // Load test competition
    m_tableModel->loadCompetition(m_competition);
}

void TestShortcutManager::cleanup()
{
    delete m_shortcutManager;
    delete m_tableView;
    delete m_tableModel;
    delete m_competition;
    qDeleteAll(m_athletes);
    m_athletes.clear();
    
    m_shortcutManager = nullptr;
    m_tableView = nullptr;
    m_tableModel = nullptr;
    m_competition = nullptr;
}

void TestShortcutManager::cleanupTestCase()
{
    // Cleanup handled in cleanup()
}

void TestShortcutManager::testShortcutManagerCreation()
{
    // Test basic shortcut manager creation
    QVERIFY(m_shortcutManager != nullptr);
    QVERIFY(m_shortcutManager->isEnabled());
    QCOMPARE(m_shortcutManager->undoStackSize(), 0);
    QVERIFY(!m_shortcutManager->canUndo());
    QVERIFY(!m_shortcutManager->canRedo());
}

void TestShortcutManager::testTableViewAssignment()
{
    // Test table view assignment
    QTableView *newView = new QTableView(this);
    m_shortcutManager->setTableView(newView);
    
    // Verify assignment (we can't directly test private member, but no crash is good)
    QVERIFY(true);
    
    delete newView;
}

void TestShortcutManager::testTableModelAssignment()
{
    // Test table model assignment
    AthleteTableModel *newModel = new AthleteTableModel(this);
    m_shortcutManager->setTableModel(newModel);
    
    // Verify assignment (we can't directly test private member, but no crash is good)
    QVERIFY(true);
    
    delete newModel;
}

void TestShortcutManager::testEnableDisable()
{
    // Test enabling/disabling shortcuts
    QVERIFY(m_shortcutManager->isEnabled());
    
    m_shortcutManager->setEnabled(false);
    QVERIFY(!m_shortcutManager->isEnabled());
    
    m_shortcutManager->setEnabled(true);
    QVERIFY(m_shortcutManager->isEnabled());
}

void TestShortcutManager::testDefaultShortcuts()
{
    // Test that default shortcuts are created
    // We can't directly test private shortcut objects, but we can verify no crashes
    m_shortcutManager->resetToDefaults();
    QVERIFY(true);
}

void TestShortcutManager::testCustomShortcuts()
{
    // Test setting custom shortcuts
    QKeySequence customSequence(Qt::CTRL | Qt::Key_O);
    m_shortcutManager->setCustomShortcut("success", customSequence);
    
    // Verify no crashes
    QVERIFY(true);
}

void TestShortcutManager::testResetToDefaults()
{
    // Test resetting to default shortcuts
    QKeySequence customSequence(Qt::CTRL | Qt::Key_O);
    m_shortcutManager->setCustomShortcut("success", customSequence);
    
    m_shortcutManager->resetToDefaults();
    
    // Verify no crashes
    QVERIFY(true);
}

void TestShortcutManager::testUndoRedoState()
{
    // Test initial undo/redo state
    QVERIFY(!m_shortcutManager->canUndo());
    QVERIFY(!m_shortcutManager->canRedo());
    QCOMPARE(m_shortcutManager->undoStackSize(), 0);
}

void TestShortcutManager::testUndoStackManagement()
{
    // Test undo stack management
    m_shortcutManager->clearUndoStack();
    QCOMPARE(m_shortcutManager->undoStackSize(), 0);
    QVERIFY(!m_shortcutManager->canUndo());
    QVERIFY(!m_shortcutManager->canRedo());
}

void TestShortcutManager::testOperationRecording()
{
    // Test operation recording
    ShortcutManager::OperationRecord operation;
    operation.type = ShortcutManager::AttemptRecord;
    operation.athleteId = 1;
    operation.height = 150;
    operation.attemptNumber = 1;
    operation.oldResult = ShortcutManager::Success;
    operation.newResult = ShortcutManager::Failure;
    operation.timestamp = QDateTime::currentDateTime();
    operation.description = "Test operation";
    
    m_shortcutManager->recordOperation(operation);
    
    QCOMPARE(m_shortcutManager->undoStackSize(), 1);
    QVERIFY(m_shortcutManager->canUndo());
    QVERIFY(!m_shortcutManager->canRedo());
}

void TestShortcutManager::testContextSensitiveShortcuts()
{
    // Test context-sensitive shortcut enabling
    // This is mainly tested through the UI interaction
    QVERIFY(true);
}

void TestShortcutManager::testValidCellDetection()
{
    // Test valid cell detection
    // We can't directly test private methods, but we can test through public interface
    QVERIFY(true);
}

void TestShortcutManager::testHeightColumnDetection()
{
    // Test height column detection
    // We can't directly test private methods, but we can test through public interface
    QVERIFY(true);
}

void TestShortcutManager::testAttemptRecordSignals()
{
    // Test attempt record signal emission
    QSignalSpy spy(m_shortcutManager, &ShortcutManager::attemptRecordRequested);
    
    // Verify signal exists
    QVERIFY(spy.isValid());
}

void TestShortcutManager::testNavigationSignals()
{
    // Test navigation signal emission
    QSignalSpy spy(m_shortcutManager, &ShortcutManager::navigationRequested);
    
    // Verify signal exists
    QVERIFY(spy.isValid());
}

void TestShortcutManager::testUndoRedoSignals()
{
    // Test undo/redo signal emission
    QSignalSpy undoSpy(m_shortcutManager, &ShortcutManager::operationUndone);
    QSignalSpy redoSpy(m_shortcutManager, &ShortcutManager::operationRedone);
    QSignalSpy stateSpy(m_shortcutManager, &ShortcutManager::undoRedoStateChanged);
    
    // Verify signals exist
    QVERIFY(undoSpy.isValid());
    QVERIFY(redoSpy.isValid());
    QVERIFY(stateSpy.isValid());
}

void TestShortcutManager::createTestData()
{
    // Create test athletes
    m_athletes.clear();
    m_athletes.append(createAthlete(1, "张", "三", 1));
    m_athletes.append(createAthlete(2, "李", "四", 2));
    m_athletes.append(createAthlete(3, "王", "五", 3));
    
    // Create test competition
    m_competition = new Competition(this);
    m_competition->setId(1);
    m_competition->setName("测试比赛");
    m_competition->setVenue("测试场地");
    
    // Set height progression
    QList<int> heights = {150, 153, 156, 159, 162};
    m_competition->setHeightProgression(heights);
    
    // Add athletes to competition
    for (Athlete *athlete : m_athletes) {
        m_competition->addAthlete(athlete);
    }
}

Athlete* TestShortcutManager::createAthlete(int id, const QString &firstName, const QString &lastName, int number)
{
    Athlete *athlete = new Athlete(this);
    athlete->setId(id);
    athlete->setFirstName(firstName);
    athlete->setLastName(lastName);
    athlete->setStartNumber(number);
    athlete->setClub("测试队伍");
    return athlete;
}

QTEST_MAIN(TestShortcutManager)
#include "test_shortcut_manager.moc"
