{"json.schemas": [], "cmake.configureArgs": ["-DCMAKE_PREFIX_PATH=C:/Qt/install-x64"], "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "C_Cpp.default.compileCommands": "${workspaceFolder}/build/compile_commands.json", "C_Cpp.default.includePath": ["${workspaceFolder}/**", "${workspaceFolder}/src", "${workspaceFolder}/src/persistence", "${workspaceFolder}/src/utils", "${workspaceFolder}/src/ui", "${workspaceFolder}/src/api", "${workspaceFolder}/tests", "${workspaceFolder}/build/tests/test_e2e_workflow_autogen/include_Release", "${workspaceFolder}/build/tests/test_e2e_workflow_autogen/include_Debug", "${workspaceFolder}/build/tests/test_database_manager_autogen/include_Release", "${workspaceFolder}/build/tests/test_config_manager_autogen/include_Release", "${workspaceFolder}/build/tests/test_api_client_autogen/include_Release", "${workspaceFolder}/build/high-jump-scorer_autogen/include_Release", "C:/Qt/install-x64/include", "C:/Qt/install-x64/include/QtCore", "C:/Qt/install-x64/include/QtWidgets", "C:/Qt/install-x64/include/QtSql", "C:/Qt/install-x64/include/QtNetwork", "C:/Qt/install-x64/include/QtTest"], "C_Cpp.default.defines": ["UNICODE", "_UNICODE", "WIN32", "_WINDOWS", "QT_CORE_LIB", "QT_WIDGETS_LIB", "QT_GUI_LIB", "QT_SQL_LIB", "QT_NETWORK_LIB", "QT_TEST_LIB"], "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.intelliSenseMode": "windows-msvc-x64", "C_Cpp.errorSquiggles": "enabled", "C_Cpp.intelliSenseEngine": "default", "C_Cpp.intelliSenseEngineFallback": "enabled", "cmake.configureOnOpen": true, "cmake.buildDirectory": "${workspaceFolder}/build", "files.associations": {"*.h": "cpp", "*.cpp": "cpp", "*.hpp": "cpp", "qobject": "cpp", "qstring": "cpp", "qlist": "cpp", "qmap": "cpp", "qvariant": "cpp"}}