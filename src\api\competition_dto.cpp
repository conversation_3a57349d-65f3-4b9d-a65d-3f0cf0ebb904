#include "competition_dto.h"
#include "../models/competition.h"
#include <QDateTime>
#include <QDebug>

CompetitionDto::CompetitionDto()
    : id(-1)
    , startingHeight(150)
    , heightIncrement(3)
{
}

CompetitionDto::CompetitionDto(const QJsonObject &json)
    : CompetitionDto()
{
    populateFromJson(json);
}

CompetitionDto CompetitionDto::fromJson(const QJsonObject &json)
{
    return CompetitionDto(json);
}

QJsonObject CompetitionDto::toJson() const
{
    QJsonObject json;
    
    json["id"] = id;
    json["name"] = name;
    json["date"] = date;
    json["venue"] = venue;
    json["status"] = status;
    json["starting_height"] = startingHeight;
    json["height_increment"] = heightIncrement;
    
    if (!athletes.isEmpty()) {
        json["athletes"] = athletes;
    }
    
    if (!heightSequence.isEmpty()) {
        json["height_sequence"] = heightSequence;
    }
    
    return json;
}

Competition* CompetitionDto::toModel() const
{
    if (!isValid()) {
        return nullptr;
    }
    
    Competition *competition = new Competition();
    
    // Set basic properties
    competition->setId(id);
    competition->setName(name);
    competition->setVenue(venue);
    competition->setStartingHeight(startingHeight);
    competition->setHeightIncrement(heightIncrement);
    
    // Parse and set date
    QDateTime dateTime = QDateTime::fromString(date, Qt::ISODate);
    if (dateTime.isValid()) {
        competition->setDate(dateTime);
    }
    
    // Set status
    if (status == "not_started") {
        competition->setStatus(Competition::NotStarted);
    } else if (status == "in_progress") {
        competition->setStatus(Competition::InProgress);
    } else if (status == "paused") {
        competition->setStatus(Competition::Paused);
    } else if (status == "finished") {
        competition->setStatus(Competition::Finished);
    }
    
    return competition;
}

CompetitionDto CompetitionDto::fromModel(const Competition &competition)
{
    CompetitionDto dto;
    
    dto.id = competition.id();
    dto.name = competition.name();
    dto.venue = competition.venue();
    dto.date = competition.date().toString(Qt::ISODate);
    dto.startingHeight = competition.startingHeight();
    dto.heightIncrement = competition.heightIncrement();
    
    // Convert status
    switch (competition.status()) {
    case Competition::NotStarted:
        dto.status = "not_started";
        break;
    case Competition::InProgress:
        dto.status = "in_progress";
        break;
    case Competition::Paused:
        dto.status = "paused";
        break;
    case Competition::Finished:
        dto.status = "finished";
        break;
    }
    
    return dto;
}

bool CompetitionDto::isValid() const
{
    return id > 0 && 
           !name.isEmpty() && 
           !date.isEmpty() && 
           !venue.isEmpty() && 
           !status.isEmpty() &&
           startingHeight > 0 &&
           heightIncrement > 0;
}

QString CompetitionDto::getFormattedDate() const
{
    QDateTime dateTime = QDateTime::fromString(date, Qt::ISODate);
    if (dateTime.isValid()) {
        return dateTime.toString("yyyy-MM-dd hh:mm");
    }
    return date;
}

QString CompetitionDto::getLocalizedStatus() const
{
    if (status == "not_started") {
        return QObject::tr("未开始");
    } else if (status == "in_progress") {
        return QObject::tr("进行中");
    } else if (status == "paused") {
        return QObject::tr("暂停");
    } else if (status == "finished") {
        return QObject::tr("已结束");
    } else {
        return status;
    }
}

void CompetitionDto::populateFromJson(const QJsonObject &json)
{
    id = json.value("id").toInt(-1);
    name = json.value("name").toString();
    date = json.value("date").toString();
    venue = json.value("venue").toString();
    status = json.value("status").toString();
    startingHeight = json.value("starting_height").toInt(150);
    heightIncrement = json.value("height_increment").toInt(3);
    
    if (json.contains("athletes") && json["athletes"].isArray()) {
        athletes = json["athletes"].toArray();
    }
    
    if (json.contains("height_sequence") && json["height_sequence"].isArray()) {
        heightSequence = json["height_sequence"].toArray();
    }
    
    qDebug() << "CompetitionDto::populateFromJson: Populated competition" << name 
             << "with ID" << id << "and status" << status;
}

// CompetitionDtoList implementation
QList<CompetitionDto> CompetitionDtoList::fromJsonArray(const QJsonArray &jsonArray)
{
    QList<CompetitionDto> competitions;
    
    for (const QJsonValue &value : jsonArray) {
        if (value.isObject()) {
            CompetitionDto dto = CompetitionDto::fromJson(value.toObject());
            if (dto.isValid()) {
                competitions.append(dto);
            } else {
                qWarning() << "CompetitionDtoList::fromJsonArray: Invalid competition data, skipping";
            }
        }
    }
    
    qDebug() << "CompetitionDtoList::fromJsonArray: Parsed" << competitions.size() 
             << "valid competitions from" << jsonArray.size() << "JSON objects";
    
    return competitions;
}

QJsonArray CompetitionDtoList::toJsonArray(const QList<CompetitionDto> &competitions)
{
    QJsonArray jsonArray;
    
    for (const CompetitionDto &dto : competitions) {
        jsonArray.append(dto.toJson());
    }
    
    return jsonArray;
}

QList<CompetitionDto> CompetitionDtoList::filterByStatus(const QList<CompetitionDto> &competitions, 
                                                        const QString &status)
{
    QList<CompetitionDto> filtered;
    
    for (const CompetitionDto &dto : competitions) {
        if (dto.status == status) {
            filtered.append(dto);
        }
    }
    
    return filtered;
}

QList<CompetitionDto> CompetitionDtoList::sortByDate(const QList<CompetitionDto> &competitions, 
                                                    bool ascending)
{
    QList<CompetitionDto> sorted = competitions;
    
    std::sort(sorted.begin(), sorted.end(), [ascending](const CompetitionDto &a, const CompetitionDto &b) {
        QDateTime dateA = QDateTime::fromString(a.date, Qt::ISODate);
        QDateTime dateB = QDateTime::fromString(b.date, Qt::ISODate);
        
        if (!dateA.isValid() && !dateB.isValid()) {
            return false;
        }
        if (!dateA.isValid()) {
            return !ascending;
        }
        if (!dateB.isValid()) {
            return ascending;
        }
        
        return ascending ? (dateA < dateB) : (dateA > dateB);
    });
    
    return sorted;
}