/****************************************************************************
** Meta object code from reading C++ file 'shortcut_manager.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/ui/shortcut_manager.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'shortcut_manager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN15ShortcutManagerE_t {};
} // unnamed namespace

template <> constexpr inline auto ShortcutManager::qt_create_metaobjectdata<qt_meta_tag_ZN15ShortcutManagerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ShortcutManager",
        "attemptRecordRequested",
        "",
        "athleteId",
        "height",
        "attemptNumber",
        "AttemptResult",
        "result",
        "navigationRequested",
        "direction",
        "confirmationRequested",
        "cancellationRequested",
        "operationUndone",
        "OperationRecord",
        "operation",
        "operationRedone",
        "undoRedoStateChanged",
        "canUndo",
        "canRedo",
        "undo",
        "redo",
        "recordOperation",
        "onSelectionChanged",
        "QModelIndex",
        "current",
        "previous",
        "onSuccessShortcut",
        "onFailureShortcut",
        "onSkipShortcut",
        "onRetireShortcut",
        "onUndoShortcut",
        "onRedoShortcut",
        "onNavigateUp",
        "onNavigateDown",
        "onNavigateLeft",
        "onNavigateRight",
        "onTabNavigation",
        "onShiftTabNavigation",
        "onConfirmShortcut",
        "onCancelShortcut",
        "onOperationTimeout"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'attemptRecordRequested'
        QtMocHelpers::SignalData<void(int, int, int, AttemptResult)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 3 }, { QMetaType::Int, 4 }, { QMetaType::Int, 5 }, { 0x80000000 | 6, 7 },
        }}),
        // Signal 'navigationRequested'
        QtMocHelpers::SignalData<void(int)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 9 },
        }}),
        // Signal 'confirmationRequested'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'cancellationRequested'
        QtMocHelpers::SignalData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'operationUndone'
        QtMocHelpers::SignalData<void(const OperationRecord &)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 13, 14 },
        }}),
        // Signal 'operationRedone'
        QtMocHelpers::SignalData<void(const OperationRecord &)>(15, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 13, 14 },
        }}),
        // Signal 'undoRedoStateChanged'
        QtMocHelpers::SignalData<void(bool, bool)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 17 }, { QMetaType::Bool, 18 },
        }}),
        // Slot 'undo'
        QtMocHelpers::SlotData<void()>(19, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'redo'
        QtMocHelpers::SlotData<void()>(20, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'recordOperation'
        QtMocHelpers::SlotData<void(const OperationRecord &)>(21, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 13, 14 },
        }}),
        // Slot 'onSelectionChanged'
        QtMocHelpers::SlotData<void(const QModelIndex &, const QModelIndex &)>(22, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 23, 24 }, { 0x80000000 | 23, 25 },
        }}),
        // Slot 'onSuccessShortcut'
        QtMocHelpers::SlotData<void()>(26, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onFailureShortcut'
        QtMocHelpers::SlotData<void()>(27, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onSkipShortcut'
        QtMocHelpers::SlotData<void()>(28, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onRetireShortcut'
        QtMocHelpers::SlotData<void()>(29, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onUndoShortcut'
        QtMocHelpers::SlotData<void()>(30, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onRedoShortcut'
        QtMocHelpers::SlotData<void()>(31, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onNavigateUp'
        QtMocHelpers::SlotData<void()>(32, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onNavigateDown'
        QtMocHelpers::SlotData<void()>(33, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onNavigateLeft'
        QtMocHelpers::SlotData<void()>(34, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onNavigateRight'
        QtMocHelpers::SlotData<void()>(35, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onTabNavigation'
        QtMocHelpers::SlotData<void()>(36, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onShiftTabNavigation'
        QtMocHelpers::SlotData<void()>(37, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onConfirmShortcut'
        QtMocHelpers::SlotData<void()>(38, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onCancelShortcut'
        QtMocHelpers::SlotData<void()>(39, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onOperationTimeout'
        QtMocHelpers::SlotData<void()>(40, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ShortcutManager, qt_meta_tag_ZN15ShortcutManagerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ShortcutManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ShortcutManagerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ShortcutManagerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN15ShortcutManagerE_t>.metaTypes,
    nullptr
} };

void ShortcutManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ShortcutManager *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->attemptRecordRequested((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<AttemptResult>>(_a[4]))); break;
        case 1: _t->navigationRequested((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 2: _t->confirmationRequested(); break;
        case 3: _t->cancellationRequested(); break;
        case 4: _t->operationUndone((*reinterpret_cast< std::add_pointer_t<OperationRecord>>(_a[1]))); break;
        case 5: _t->operationRedone((*reinterpret_cast< std::add_pointer_t<OperationRecord>>(_a[1]))); break;
        case 6: _t->undoRedoStateChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 7: _t->undo(); break;
        case 8: _t->redo(); break;
        case 9: _t->recordOperation((*reinterpret_cast< std::add_pointer_t<OperationRecord>>(_a[1]))); break;
        case 10: _t->onSelectionChanged((*reinterpret_cast< std::add_pointer_t<QModelIndex>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QModelIndex>>(_a[2]))); break;
        case 11: _t->onSuccessShortcut(); break;
        case 12: _t->onFailureShortcut(); break;
        case 13: _t->onSkipShortcut(); break;
        case 14: _t->onRetireShortcut(); break;
        case 15: _t->onUndoShortcut(); break;
        case 16: _t->onRedoShortcut(); break;
        case 17: _t->onNavigateUp(); break;
        case 18: _t->onNavigateDown(); break;
        case 19: _t->onNavigateLeft(); break;
        case 20: _t->onNavigateRight(); break;
        case 21: _t->onTabNavigation(); break;
        case 22: _t->onShiftTabNavigation(); break;
        case 23: _t->onConfirmShortcut(); break;
        case 24: _t->onCancelShortcut(); break;
        case 25: _t->onOperationTimeout(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ShortcutManager::*)(int , int , int , AttemptResult )>(_a, &ShortcutManager::attemptRecordRequested, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ShortcutManager::*)(int )>(_a, &ShortcutManager::navigationRequested, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ShortcutManager::*)()>(_a, &ShortcutManager::confirmationRequested, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ShortcutManager::*)()>(_a, &ShortcutManager::cancellationRequested, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ShortcutManager::*)(const OperationRecord & )>(_a, &ShortcutManager::operationUndone, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ShortcutManager::*)(const OperationRecord & )>(_a, &ShortcutManager::operationRedone, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ShortcutManager::*)(bool , bool )>(_a, &ShortcutManager::undoRedoStateChanged, 6))
            return;
    }
}

const QMetaObject *ShortcutManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ShortcutManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ShortcutManagerE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ShortcutManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 26)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 26;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 26)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 26;
    }
    return _id;
}

// SIGNAL 0
void ShortcutManager::attemptRecordRequested(int _t1, int _t2, int _t3, AttemptResult _t4)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2, _t3, _t4);
}

// SIGNAL 1
void ShortcutManager::navigationRequested(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void ShortcutManager::confirmationRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ShortcutManager::cancellationRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ShortcutManager::operationUndone(const OperationRecord & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void ShortcutManager::operationRedone(const OperationRecord & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}

// SIGNAL 6
void ShortcutManager::undoRedoStateChanged(bool _t1, bool _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1, _t2);
}
QT_WARNING_POP
