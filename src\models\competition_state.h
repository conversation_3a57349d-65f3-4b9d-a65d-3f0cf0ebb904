#ifndef COMPETITION_STATE_H
#define COMPETITION_STATE_H

#include <QObject>
#include <QDateTime>
#include <QTimer>
#include <QHash>
#include <QList>
#include <QMutex>

class Competition;
class RankingCalculator;
class DatabaseManager;

/**
 * @brief 比赛状态管理器
 * 
 * 这个类负责管理跳高比赛的整体状态，包括：
 * - 比赛进度跟踪
 * - 实时状态更新
 * - 数据持久化
 * - 状态同步
 * - 比赛结束检测
 * 
 * 状态管理功能：
 * - 当前高度管理
 * - 运动员状态跟踪
 * - 比赛阶段控制
 * - 自动保存和恢复
 * - 实时数据同步
 */
class CompetitionState : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 比赛阶段枚举
     */
    enum CompetitionPhase {
        NotStarted = 0,     // 未开始
        InProgress = 1,     // 进行中
        Paused = 2,         // 暂停
        Finished = 3,       // 已结束
        Cancelled = 4       // 已取消
    };

    /**
     * @brief 运动员状态枚举
     */
    enum AthleteStatus {
        Waiting = 0,        // 等待中
        Active = 1,         // 活跃
        Passed = 2,         // 通过当前高度
        Failed = 3,         // 失败
        Retired = 4,        // 退赛
        Finished = 5        // 完成比赛
    };

    /**
     * @brief 比赛状态快照
     */
    struct StateSnapshot {
        CompetitionPhase phase;
        int currentHeight;
        int currentRound;
        QDateTime startTime;
        QDateTime lastUpdateTime;
        QHash<int, AthleteStatus> athleteStatuses;
        QHash<int, int> athleteBestHeights;
        QHash<int, int> athleteRanks;
        int totalAthletes;
        int activeAthletes;
        int completedAthletes;
        int retiredAthletes;
        bool isAutoSaveEnabled;
        QString notes;
    };

    explicit CompetitionState(QObject *parent = nullptr);
    ~CompetitionState();

    // 比赛管理
    /**
     * @brief 加载比赛
     * @param competition 比赛对象
     */
    void loadCompetition(Competition *competition);

    /**
     * @brief 开始比赛
     */
    void startCompetition();

    /**
     * @brief 暂停比赛
     */
    void pauseCompetition();

    /**
     * @brief 恢复比赛
     */
    void resumeCompetition();

    /**
     * @brief 结束比赛
     */
    void finishCompetition();

    /**
     * @brief 取消比赛
     */
    void cancelCompetition();

    // 状态查询
    /**
     * @brief 获取当前比赛阶段
     * @return 比赛阶段
     */
    CompetitionPhase getCurrentPhase() const;

    /**
     * @brief 获取当前高度
     * @return 当前高度（厘米）
     */
    int getCurrentHeight() const;

    /**
     * @brief 获取当前轮次
     * @return 当前轮次
     */
    int getCurrentRound() const;

    /**
     * @brief 获取比赛开始时间
     * @return 开始时间
     */
    QDateTime getStartTime() const;

    /**
     * @brief 获取比赛持续时间
     * @return 持续时间（秒）
     */
    qint64 getElapsedTime() const;

    /**
     * @brief 获取运动员状态
     * @param athleteId 运动员ID
     * @return 运动员状态
     */
    AthleteStatus getAthleteStatus(int athleteId) const;

    /**
     * @brief 获取活跃运动员数量
     * @return 活跃运动员数量
     */
    int getActiveAthleteCount() const;

    /**
     * @brief 检查比赛是否可以开始
     * @return 是否可以开始
     */
    bool canStartCompetition() const;

    /**
     * @brief 检查比赛是否应该结束
     * @return 是否应该结束
     */
    bool shouldFinishCompetition() const;

    // 高度管理
    /**
     * @brief 设置当前高度
     * @param height 高度（厘米）
     */
    void setCurrentHeight(int height);

private:
    /**
     * @brief 设置当前高度（无锁版本，假设调用者已持有锁）
     * @param height 高度（厘米）
     */
    void setCurrentHeightLocked(int height);

    /**
     * @brief 前进到下一高度
     * @return 是否成功前进
     */
    bool advanceToNextHeight();

    /**
     * @brief 获取下一个高度
     * @return 下一个高度，-1表示没有更多高度
     */
    int getNextHeight() const;

    // 运动员状态管理
    /**
     * @brief 更新运动员状态
     * @param athleteId 运动员ID
     * @param status 新状态
     */
    void updateAthleteStatus(int athleteId, AthleteStatus status);

    /**
     * @brief 更新运动员最佳成绩
     * @param athleteId 运动员ID
     * @param height 最佳成绩
     */
    void updateAthleteBestHeight(int athleteId, int height);

    /**
     * @brief 更新运动员排名
     * @param athleteId 运动员ID
     * @param rank 排名
     */
    void updateAthleteRank(int athleteId, int rank);

    // 数据持久化
    /**
     * @brief 保存状态到数据库
     * @return 是否成功
     */
    bool saveState();

    /**
     * @brief 从数据库加载状态
     * @return 是否成功
     */
    bool loadState();

    /**
     * @brief 设置自动保存
     * @param enabled 是否启用
     * @param intervalSeconds 保存间隔（秒）
     */
    void setAutoSave(bool enabled, int intervalSeconds = 30);

    /**
     * @brief 创建状态快照
     * @return 状态快照
     */
    StateSnapshot createSnapshot() const;

    /**
     * @brief 从快照恢复状态
     * @param snapshot 状态快照
     */
    void restoreFromSnapshot(const StateSnapshot &snapshot);

    // 配置
    /**
     * @brief 设置排名计算器
     * @param calculator 排名计算器
     */
    void setRankingCalculator(RankingCalculator *calculator);

    /**
     * @brief 设置数据库管理器
     * @param dbManager 数据库管理器
     */
    void setDatabaseManager(DatabaseManager *dbManager);

signals:
    /**
     * @brief 比赛阶段变化
     * @param phase 新阶段
     */
    void phaseChanged(CompetitionPhase phase);

    /**
     * @brief 当前高度变化
     * @param height 新高度
     */
    void currentHeightChanged(int height);

    /**
     * @brief 运动员状态变化
     * @param athleteId 运动员ID
     * @param status 新状态
     */
    void athleteStatusChanged(int athleteId, AthleteStatus status);

    /**
     * @brief 比赛统计更新
     * @param totalAthletes 总运动员数
     * @param activeAthletes 活跃运动员数
     * @param completedAthletes 完成运动员数
     * @param retiredAthletes 退赛运动员数
     */
    void statisticsUpdated(int totalAthletes, int activeAthletes, int completedAthletes, int retiredAthletes);

    /**
     * @brief 状态保存完成
     * @param success 是否成功
     */
    void stateSaved(bool success);

    /**
     * @brief 状态加载完成
     * @param success 是否成功
     */
    void stateLoaded(bool success);

    /**
     * @brief 比赛结束检测
     */
    void competitionShouldFinish();

public slots:
    /**
     * @brief 刷新状态
     */
    void refreshState();

    /**
     * @brief 处理试跳结果更新
     * @param athleteId 运动员ID
     * @param height 高度
     * @param result 试跳结果
     */
    void onAttemptResultUpdated(int athleteId, int height, int result);

    /**
     * @brief 处理排名更新
     */
    void onRankingsUpdated();

private slots:
    /**
     * @brief 自动保存定时器
     */
    void onAutoSaveTimer();

    /**
     * @brief 状态更新定时器
     */
    void onStateUpdateTimer();

private:
    // 初始化方法
    void initializeState();
    void setupTimers();

    // 状态计算
    void calculateAthleteStatuses();
    void calculateStatistics();
    void checkCompetitionEnd();

    // 数据持久化实现
    bool saveStateToDatabase();
    bool loadStateFromDatabase();

    // 数据成员
    Competition *m_competition;
    RankingCalculator *m_rankingCalculator;
    DatabaseManager *m_dbManager;

    // 状态数据
    CompetitionPhase m_currentPhase;
    int m_currentHeight;
    int m_currentRound;
    QDateTime m_startTime;
    QDateTime m_lastUpdateTime;

    // 运动员状态
    QHash<int, AthleteStatus> m_athleteStatuses;
    QHash<int, int> m_athleteBestHeights;
    QHash<int, int> m_athleteRanks;

    // 统计数据
    int m_totalAthletes;
    int m_activeAthletes;
    int m_completedAthletes;
    int m_retiredAthletes;

    // 配置
    bool m_autoSaveEnabled;
    int m_autoSaveInterval;

    // 定时器
    QTimer *m_autoSaveTimer;
    QTimer *m_stateUpdateTimer;

    // 线程安全
    mutable QMutex m_stateMutex;

    // 常量
    static const int DEFAULT_AUTO_SAVE_INTERVAL = 30; // 30 seconds
    static const int STATE_UPDATE_INTERVAL = 1000;    // 1 second
};

// 使状态枚举可以在Qt信号槽中使用
Q_DECLARE_METATYPE(CompetitionState::CompetitionPhase)
Q_DECLARE_METATYPE(CompetitionState::AthleteStatus)
Q_DECLARE_METATYPE(CompetitionState::StateSnapshot)

#endif // COMPETITION_STATE_H
