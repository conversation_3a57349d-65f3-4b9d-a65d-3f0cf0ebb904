#ifndef API_CLIENT_H
#define API_CLIENT_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonDocument>
#include <QString>
#include <QUrl>
#include <QTimer>
#include <QMutex>

class ConfigManager;

/**
 * @brief API client for High Jump Competition Management System
 * 
 * This class provides comprehensive HTTP/HTTPS API communication capabilities
 * for the High Jump Competition Management System. It handles all external
 * API interactions including data synchronization, remote configuration,
 * and cloud-based services.
 * 
 * The ApiClient implements the Singleton pattern to ensure a single network
 * manager instance throughout the application lifecycle. It provides:
 * - HTTP/HTTPS request management (GET, POST, PUT, DELETE)
 * - Authentication and authorization handling
 * - Network status monitoring and automatic reconnection
 * - Error handling and logging
 * - SSL/TLS security support
 * - Request/response lifecycle management
 * 
 * The client supports both synchronous and asynchronous operations with
 * comprehensive signal/slot architecture for event-driven programming.
 */
class ApiClient : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Gets the singleton instance of ApiClient
     * @return Pointer to the ApiClient instance
     * 
     * Returns the single instance of ApiClient, creating it if necessary.
     * Implements the Singleton pattern for network connection management.
     */
    static ApiClient* instance();
    
    // Client lifecycle
    /**
     * @brief Initializes the API client
     * @return true if initialization successful, false otherwise
     * 
     * Performs complete API client initialization including:
     * - Network manager setup
     * - Configuration loading
     * - Timer initialization
     * - Signal/slot connections
     * - SSL configuration
     */
    bool initialize();
    
    /**
     * @brief Checks if the API client is initialized
     * @return true if initialized, false otherwise
     * 
     * Verifies that the API client has been properly initialized
     * and is ready for network operations.
     */
    bool isInitialized() const;
    
    // Network status
    /**
     * @brief Checks if the system is online
     * @return true if online, false otherwise
     * 
     * Determines if the system has an active internet connection
     * and can reach the configured API endpoints.
     */
    bool isOnline() const;
    
    /**
     * @brief Manually checks network status
     * 
     * Performs an active network connectivity check by attempting
     * to reach the configured API endpoints. Emits network status
     * signals based on the result.
     */
    void checkNetworkStatus();
    
    // Test connectivity
    /**
     * @brief Tests API connectivity
     * 
     * Performs a connectivity test to the configured API endpoints.
     * Emits connectionTestCompleted() or connectionTestFailed() signals
     * based on the test result.
     */
    void testConnection();
    
    /**
     * @brief Fetches available competitions from API
     * 
     * Sends a GET request to retrieve the list of available competitions
     * from the server. Emits competitionsReceived() signal with the
     * competition data on success, or networkError() on failure.
     */
    void fetchCompetitions();
    
    // Request management
    /**
     * @brief Performs a GET request to the specified endpoint
     * @param endpoint The API endpoint to request (relative to base URL)
     * @return QNetworkReply pointer for handling the response, or nullptr on error
     * 
     * Sends an HTTP GET request to the specified endpoint. The endpoint
     * should be relative to the configured base URL. Returns a QNetworkReply
     * object that can be used to handle the asynchronous response.
     * 
     * @note The caller is responsible for managing the QNetworkReply lifecycle
     */
    QNetworkReply* get(const QString &endpoint);
    
    /**
     * @brief Performs a POST request to the specified endpoint
     * @param endpoint The API endpoint to request (relative to base URL)
     * @param data JSON data to send in the request body
     * @return QNetworkReply pointer for handling the response, or nullptr on error
     * 
     * Sends an HTTP POST request with JSON data to the specified endpoint.
     * The data is automatically serialized as JSON and sent in the request body.
     * 
     * @note The caller is responsible for managing the QNetworkReply lifecycle
     */
    QNetworkReply* post(const QString &endpoint, const QJsonObject &data);
    
    /**
     * @brief Performs a PUT request to the specified endpoint
     * @param endpoint The API endpoint to request (relative to base URL)
     * @param data JSON data to send in the request body
     * @return QNetworkReply pointer for handling the response, or nullptr on error
     * 
     * Sends an HTTP PUT request with JSON data to the specified endpoint.
     * Used for updating existing resources on the server.
     * 
     * @note The caller is responsible for managing the QNetworkReply lifecycle
     */
    QNetworkReply* put(const QString &endpoint, const QJsonObject &data);
    
    /**
     * @brief Performs a DELETE request to the specified endpoint
     * @param endpoint The API endpoint to request (relative to base URL)
     * @return QNetworkReply pointer for handling the response, or nullptr on error
     * 
     * Sends an HTTP DELETE request to the specified endpoint.
     * Used for deleting resources on the server.
     * 
     * @note The caller is responsible for managing the QNetworkReply lifecycle
     */
    QNetworkReply* deleteResource(const QString &endpoint);
    
    // Error handling
    /**
     * @brief Gets the last error message
     * @return QString containing the last error message
     * 
     * Returns the most recent API error message for debugging
     * and user notification purposes.
     */
    QString lastError() const;
    
    // Authentication
    /**
     * @brief Sets the authentication token
     * @param token The authentication token to use for API requests
     * 
     * Sets the Bearer token for API authentication. This token will
     * be automatically included in the Authorization header for all
     * subsequent API requests.
     */
    void setAuthToken(const QString &token);
    
    /**
     * @brief Sets the API key
     * @param key The API key to use for API requests
     * 
     * Sets the API key for API authentication. This key will be
     * automatically included in the X-API-Key header for all
     * subsequent API requests.
     */
    void setApiKey(const QString &key);
    
    /**
     * @brief Gets the current authentication token
     * @return QString containing the current auth token
     * 
     * Returns the currently configured authentication token.
     */
    QString getAuthToken() const;
    
    /**
     * @brief Gets the current API key
     * @return QString containing the current API key
     * 
     * Returns the currently configured API key.
     */
    QString getApiKey() const;
    
    /**
     * @brief Clears all authentication credentials
     * 
     * Removes both the authentication token and API key from
     * the client configuration. Subsequent requests will be
     * made without authentication headers.
     */
    void clearAuth();

signals:
    // Network status signals
    /**
     * @brief Emitted when network status changes
     * @param isOnline true if network is online, false if offline
     * 
     * Signal emitted when the network connectivity status changes.
     * Used for updating UI and application state based on connectivity.
     */
    void networkStatusChanged(bool isOnline);
    
    /**
     * @brief Emitted when network connection is restored
     * 
     * Signal emitted when network connectivity is restored after
     * being offline. Used for automatic reconnection and sync.
     */
    void connectionRestored();
    
    /**
     * @brief Emitted when network connection is lost
     * 
     * Signal emitted when network connectivity is lost.
     * Used for error handling and offline mode activation.
     */
    void connectionLost();
    
    // Test signals
    /**
     * @brief Emitted when connection test completes successfully
     * @param success true if test was successful, false otherwise
     * 
     * Signal emitted when a connectivity test completes.
     * Used for connection validation and status reporting.
     */
    void connectionTestCompleted(bool success);
    
    /**
     * @brief Emitted when connection test fails
     * @param error Error message describing the failure
     * 
     * Signal emitted when a connectivity test fails.
     * Used for error reporting and debugging.
     */
    void connectionTestFailed(const QString &error);
    
    // Competition API signals
    /**
     * @brief Emitted when competitions are received from API
     * @param competitions JSON array containing competition data
     * 
     * Signal emitted when the fetchCompetitions() request completes successfully.
     * The competitions parameter contains an array of competition objects with
     * id, name, date, venue, and status information.
     */
    void competitionsReceived(const QJsonArray &competitions);
    
    // Generic response signals
    /**
     * @brief Emitted when a request completes successfully
     * @param reply The QNetworkReply object containing the response
     * 
     * Signal emitted when any API request completes successfully.
     * Used for response handling and data processing.
     */
    void requestCompleted(QNetworkReply* reply);
    
    /**
     * @brief Emitted when a request fails
     * @param reply The QNetworkReply object containing error information
     * @param error Error message describing the failure
     * 
     * Signal emitted when any API request fails.
     * Used for error handling and user notification.
     */
    void requestFailed(QNetworkReply* reply, const QString &error);

private slots:
    /**
     * @brief Handles network reply completion
     * 
     * Slot for handling the completion of network requests.
     * Processes the response and emits appropriate signals.
     */
    void onNetworkReplyFinished();
    
    /**
     * @brief Handles network errors
     * @param error The network error that occurred
     * 
     * Slot for handling network errors during API requests.
     * Logs errors and emits error signals.
     */
    void onNetworkError(QNetworkReply::NetworkError error);
    
    /**
     * @brief Handles SSL errors
     * @param errors List of SSL errors that occurred
     * 
     * Slot for handling SSL/TLS errors during secure connections.
     * Logs errors and may emit security-related signals.
     */
    void onSslErrors(const QList<QSslError> &errors);
    
    /**
     * @brief Handles network status check timeout
     * 
     * Slot for handling network status check timeouts.
     * Used for periodic connectivity monitoring.
     */
    void onNetworkStatusCheckTimeout();
    
    /**
     * @brief Handles configuration changes
     * 
     * Slot for handling configuration changes from ConfigManager.
     * Updates API client settings based on new configuration.
     */
    void onConfigurationChanged();

private:
    /**
     * @brief Private constructor for Singleton pattern
     * @param parent Parent QObject (default: nullptr)
     * 
     * Private constructor to enforce Singleton pattern.
     * Use instance() to get the ApiClient instance.
     */
    explicit ApiClient(QObject *parent = nullptr);
    
    /**
     * @brief Destructor
     * 
     * Ensures proper cleanup of network resources and connections.
     */
    ~ApiClient();
    
    // Initialization helpers
    /**
     * @brief Sets up the network manager
     * @return true if setup successful, false otherwise
     * 
     * Initializes the QNetworkAccessManager and configures
     * network settings, SSL configuration, and proxy settings.
     */
    bool setupNetworkManager();
    
    /**
     * @brief Sets up network monitoring timers
     * 
     * Initializes timers for periodic network status checks
     * and connection monitoring.
     */
    void setupTimers();
    
    /**
     * @brief Connects to ConfigManager signals
     * 
     * Establishes signal/slot connections to ConfigManager
     * for configuration change notifications.
     */
    void connectConfigManager();
    
    /**
     * @brief Loads configuration from ConfigManager
     * 
     * Loads API configuration settings including base URL,
     * timeout values, and authentication settings.
     */
    void loadConfiguration();
    
    // Request building
    /**
     * @brief Builds a network request for the specified endpoint
     * @param endpoint The API endpoint (relative to base URL)
     * @return QNetworkRequest object configured for the endpoint
     * 
     * Creates and configures a QNetworkRequest object with
     * proper headers, authentication, and URL construction.
     */
    QNetworkRequest buildRequest(const QString &endpoint);
    
    /**
     * @brief Sets common headers for all requests
     * @param request The QNetworkRequest to configure
     * 
     * Adds standard headers like Content-Type, User-Agent,
     * and API version to all network requests.
     */
    void setCommonHeaders(QNetworkRequest &request);
    
    /**
     * @brief Sets authentication headers for requests
     * @param request The QNetworkRequest to configure
     * 
     * Adds authentication headers (Authorization and X-API-Key)
     * to network requests if credentials are configured.
     */
    void setAuthHeaders(QNetworkRequest &request);
    
    // Response handling
    /**
     * @brief Handles network response processing
     * @param reply The QNetworkReply object to process
     * 
     * Processes completed network responses, validates them,
     * and emits appropriate signals based on the result.
     */
    void handleResponse(QNetworkReply *reply);
    
    /**
     * @brief Checks if a response indicates success
     * @param reply The QNetworkReply object to check
     * @return true if response indicates success, false otherwise
     * 
     * Validates HTTP response codes and determines if the
     * request was successful.
     */
    bool isSuccessResponse(QNetworkReply *reply);
    
    /**
     * @brief Parses JSON response from network reply
     * @param reply The QNetworkReply object containing JSON data
     * @return QJsonObject containing the parsed JSON data
     * 
     * Extracts and parses JSON data from network responses.
     * Returns empty QJsonObject if parsing fails.
     */
    QJsonObject parseJsonResponse(QNetworkReply *reply);
    
    // Network status management
    /**
     * @brief Updates internal network status
     * @param isOnline true if network is online, false otherwise
     * 
     * Updates internal network status tracking and emits
     * appropriate signals if status has changed.
     */
    void updateNetworkStatus(bool isOnline);
    
    /**
     * @brief Schedules periodic network status checks
     * 
     * Sets up periodic network connectivity checks to monitor
     * connection status and detect changes.
     */
    void scheduleNetworkStatusCheck();
    
    // Error handling
    /**
     * @brief Logs network errors for debugging
     * @param operation Description of the operation that failed
     * @param reply The QNetworkReply object containing error details
     * 
     * Logs detailed network error information for debugging
     * and error tracking purposes.
     */
    void logNetworkError(const QString &operation, QNetworkReply *reply);
    
    /**
     * @brief Converts network error to human-readable string
     * @param error The QNetworkReply::NetworkError to convert
     * @return QString containing human-readable error description
     * 
     * Converts Qt network error codes to user-friendly
     * error messages.
     */
    QString getNetworkErrorString(QNetworkReply::NetworkError error);
    
    // Static constants
    static const int STATUS_CHECK_INTERVAL_MS; ///< Interval for network status checks
    static const QString USER_AGENT;           ///< User agent string for requests
    
    // Member variables
    QNetworkAccessManager *m_networkManager;  ///< Network manager for HTTP requests
    ConfigManager *m_configManager;           ///< Configuration manager instance
    QTimer *m_networkStatusTimer;             ///< Timer for network status checks
    QTimer *m_statusCheckTimer;               ///< Timer for periodic status checks
    QTimer *m_reconnectTimer;                 ///< Timer for reconnection attempts
    QString m_baseUrl;                        ///< Base URL for API endpoints
    QString m_apiVersion;                     ///< API version string
    QString m_authToken;                      ///< Authentication token
    QString m_apiKey;                         ///< API key for authentication
    QString m_lastError;                      ///< Last error message
    int m_timeout;                            ///< Request timeout in milliseconds
    int m_maxRetries;                         ///< Maximum retry attempts
    bool m_isInitialized;                     ///< Initialization status
    bool m_isOnline;                          ///< Network connectivity status
    mutable QMutex m_mutex;                   ///< Thread safety mutex
    
    static ApiClient* s_instance;             ///< Singleton instance
};

#endif // API_CLIENT_H