#include "shortcut_manager.h"
#include "models/athlete_table_model.h"
#include "models/jump_attempt.h"
#include <QTableView>
#include <QApplication>
#include <QDebug>
#include <QDateTime>
#include <QMessageBox>

// 默认快捷键定义
const QHash<QString, QKeySequence> ShortcutManager::DEFAULT_SHORTCUTS = {
    {"success", QKeySequence(Qt::Key_O)},
    {"failure", QKeySequence(Qt::Key_X)},
    {"skip", QKeySequence(Qt::Key_Minus)},
    {"retire", QKeySequence(Qt::Key_R)},
    {"undo", QKeySequence(QKeySequence::Undo)},
    {"redo", QKeySequence(QKeySequence::Redo)},
    {"navigate_up", QKeySequence(Qt::Key_Up)},
    {"navigate_down", QKeySequence(Qt::Key_Down)},
    {"navigate_left", QKeySequence(Qt::Key_Left)},
    {"navigate_right", QKeySequence(Qt::Key_Right)},
    {"tab_navigation", QKeySequence(Qt::Key_Tab)},
    {"shift_tab_navigation", QKeySequence(Qt::SHIFT | Qt::Key_Tab)},
    {"confirm", QKeySequence(Qt::Key_Return)},
    {"cancel", QKeySequence(Qt::Key_Escape)}
};

ShortcutManager::ShortcutManager(QWidget *parent)
    : QObject(parent)
    , m_parent(parent)
    , m_tableView(nullptr)
    , m_tableModel(nullptr)
    , m_enabled(true)
    , m_operationTimer(new QTimer(this))
    , m_operationInProgress(false)
{
    // Setup operation timer
    m_operationTimer->setSingleShot(true);
    m_operationTimer->setInterval(100); // 100ms delay for operation processing
    connect(m_operationTimer, &QTimer::timeout, this, &ShortcutManager::onOperationTimeout);
    
    // Initialize shortcuts
    setupShortcuts();
    
    qDebug() << "ShortcutManager: Initialized with default shortcuts";
}

ShortcutManager::~ShortcutManager()
{
    // Clean up shortcuts
    for (auto it = m_shortcuts.begin(); it != m_shortcuts.end(); ++it) {
        delete it.value();
    }
    m_shortcuts.clear();
    
    qDebug() << "ShortcutManager: Destroyed";
}

void ShortcutManager::setTableView(QTableView *tableView)
{
    if (m_tableView == tableView) {
        return;
    }
    
    m_tableView = tableView;
    
    if (m_tableView) {
        // Connect selection model signals
        if (m_tableView->selectionModel()) {
            connect(m_tableView->selectionModel(), &QItemSelectionModel::currentChanged,
                    this, &ShortcutManager::onSelectionChanged);
        }
        
        // Update shortcut context
        updateShortcutContext();
    }
    
    qDebug() << "ShortcutManager::setTableView: Table view set";
}

void ShortcutManager::setTableModel(AthleteTableModel *model)
{
    m_tableModel = model;
    qDebug() << "ShortcutManager::setTableModel: Table model set";
}

void ShortcutManager::setEnabled(bool enabled)
{
    if (m_enabled == enabled) {
        return;
    }
    
    m_enabled = enabled;
    
    // Enable/disable all shortcuts
    for (auto it = m_shortcuts.begin(); it != m_shortcuts.end(); ++it) {
        it.value()->setEnabled(enabled);
    }
    
    qDebug() << "ShortcutManager::setEnabled:" << enabled;
}

bool ShortcutManager::isEnabled() const
{
    return m_enabled;
}

bool ShortcutManager::canUndo() const
{
    return !m_undoStack.isEmpty();
}

bool ShortcutManager::canRedo() const
{
    return !m_redoStack.isEmpty();
}

int ShortcutManager::undoStackSize() const
{
    return m_undoStack.size();
}

void ShortcutManager::clearUndoStack()
{
    m_undoStack.clear();
    m_redoStack.clear();
    emit undoRedoStateChanged(false, false);
    qDebug() << "ShortcutManager::clearUndoStack: Undo stack cleared";
}

void ShortcutManager::setCustomShortcut(const QString &action, const QKeySequence &keySequence)
{
    m_customShortcuts[action] = keySequence;
    
    // Update existing shortcut if it exists
    if (m_shortcuts.contains(action)) {
        m_shortcuts[action]->setKey(keySequence);
    }
    
    qDebug() << "ShortcutManager::setCustomShortcut:" << action << "=" << keySequence.toString();
}

void ShortcutManager::resetToDefaults()
{
    m_customShortcuts.clear();
    
    // Reset all shortcuts to defaults
    for (auto it = DEFAULT_SHORTCUTS.begin(); it != DEFAULT_SHORTCUTS.end(); ++it) {
        if (m_shortcuts.contains(it.key())) {
            m_shortcuts[it.key()]->setKey(it.value());
        }
    }
    
    qDebug() << "ShortcutManager::resetToDefaults: Reset to default shortcuts";
}

void ShortcutManager::undo()
{
    if (!canUndo()) {
        return;
    }
    
    OperationRecord operation = m_undoStack.pop();
    executeUndo(operation);
    m_redoStack.push(operation);
    
    emit operationUndone(operation);
    emit undoRedoStateChanged(canUndo(), canRedo());
    
    qDebug() << "ShortcutManager::undo: Undid operation" << operation.description;
}

void ShortcutManager::redo()
{
    if (!canRedo()) {
        return;
    }
    
    OperationRecord operation = m_redoStack.pop();
    executeRedo(operation);
    m_undoStack.push(operation);
    
    emit operationRedone(operation);
    emit undoRedoStateChanged(canUndo(), canRedo());
    
    qDebug() << "ShortcutManager::redo: Redid operation" << operation.description;
}

void ShortcutManager::recordOperation(const OperationRecord &operation)
{
    pushOperation(operation);
    qDebug() << "ShortcutManager::recordOperation: Recorded" << operation.description;
}

void ShortcutManager::onSelectionChanged(const QModelIndex &current, const QModelIndex &previous)
{
    Q_UNUSED(previous)
    m_currentIndex = current;
    updateShortcutContext();
}

// Private slot implementations
void ShortcutManager::onSuccessShortcut()
{
    processAttemptRecord(Success);
}

void ShortcutManager::onFailureShortcut()
{
    processAttemptRecord(Failure);
}

void ShortcutManager::onSkipShortcut()
{
    processAttemptRecord(Skip);
}

void ShortcutManager::onRetireShortcut()
{
    processAttemptRecord(Retire);
}

void ShortcutManager::onUndoShortcut()
{
    undo();
}

void ShortcutManager::onRedoShortcut()
{
    redo();
}

void ShortcutManager::onNavigateUp()
{
    processNavigation(0);
}

void ShortcutManager::onNavigateDown()
{
    processNavigation(1);
}

void ShortcutManager::onNavigateLeft()
{
    processNavigation(2);
}

void ShortcutManager::onNavigateRight()
{
    processNavigation(3);
}

void ShortcutManager::onTabNavigation()
{
    processNavigation(3); // Tab moves right
}

void ShortcutManager::onShiftTabNavigation()
{
    processNavigation(2); // Shift+Tab moves left
}

void ShortcutManager::onConfirmShortcut()
{
    emit confirmationRequested();
}

void ShortcutManager::onCancelShortcut()
{
    emit cancellationRequested();
}

void ShortcutManager::onOperationTimeout()
{
    m_operationInProgress = false;
}

void ShortcutManager::setupShortcuts()
{
    setupDefaultShortcuts();
    connectShortcuts();
}

void ShortcutManager::setupDefaultShortcuts()
{
    // Create shortcuts for all default actions
    for (auto it = DEFAULT_SHORTCUTS.begin(); it != DEFAULT_SHORTCUTS.end(); ++it) {
        const QString &action = it.key();
        const QKeySequence &sequence = it.value();
        
        // Check for custom override
        QKeySequence finalSequence = m_customShortcuts.value(action, sequence);
        
        // Create shortcut
        QShortcut *shortcut = new QShortcut(finalSequence, m_parent);
        shortcut->setContext(Qt::WidgetWithChildrenShortcut);
        m_shortcuts[action] = shortcut;
    }
    
    qDebug() << "ShortcutManager::setupDefaultShortcuts: Created" << m_shortcuts.size() << "shortcuts";
}

void ShortcutManager::connectShortcuts()
{
    // Connect attempt recording shortcuts
    connect(m_shortcuts["success"], &QShortcut::activated, this, &ShortcutManager::onSuccessShortcut);
    connect(m_shortcuts["failure"], &QShortcut::activated, this, &ShortcutManager::onFailureShortcut);
    connect(m_shortcuts["skip"], &QShortcut::activated, this, &ShortcutManager::onSkipShortcut);
    connect(m_shortcuts["retire"], &QShortcut::activated, this, &ShortcutManager::onRetireShortcut);

    // Connect undo/redo shortcuts
    connect(m_shortcuts["undo"], &QShortcut::activated, this, &ShortcutManager::onUndoShortcut);
    connect(m_shortcuts["redo"], &QShortcut::activated, this, &ShortcutManager::onRedoShortcut);

    // Connect navigation shortcuts
    connect(m_shortcuts["navigate_up"], &QShortcut::activated, this, &ShortcutManager::onNavigateUp);
    connect(m_shortcuts["navigate_down"], &QShortcut::activated, this, &ShortcutManager::onNavigateDown);
    connect(m_shortcuts["navigate_left"], &QShortcut::activated, this, &ShortcutManager::onNavigateLeft);
    connect(m_shortcuts["navigate_right"], &QShortcut::activated, this, &ShortcutManager::onNavigateRight);
    connect(m_shortcuts["tab_navigation"], &QShortcut::activated, this, &ShortcutManager::onTabNavigation);
    connect(m_shortcuts["shift_tab_navigation"], &QShortcut::activated, this, &ShortcutManager::onShiftTabNavigation);

    // Connect confirmation shortcuts
    connect(m_shortcuts["confirm"], &QShortcut::activated, this, &ShortcutManager::onConfirmShortcut);
    connect(m_shortcuts["cancel"], &QShortcut::activated, this, &ShortcutManager::onCancelShortcut);

    qDebug() << "ShortcutManager::connectShortcuts: Connected all shortcut signals";
}

void ShortcutManager::updateShortcutContext()
{
    // Enable/disable shortcuts based on current context
    bool inHeightColumn = isHeightColumn(m_currentIndex);
    bool validCell = isValidCell(m_currentIndex);

    // Attempt recording shortcuts only work in height columns
    m_shortcuts["success"]->setEnabled(m_enabled && inHeightColumn);
    m_shortcuts["failure"]->setEnabled(m_enabled && inHeightColumn);
    m_shortcuts["skip"]->setEnabled(m_enabled && inHeightColumn);
    m_shortcuts["retire"]->setEnabled(m_enabled && inHeightColumn);

    // Navigation shortcuts work everywhere
    m_shortcuts["navigate_up"]->setEnabled(m_enabled && validCell);
    m_shortcuts["navigate_down"]->setEnabled(m_enabled && validCell);
    m_shortcuts["navigate_left"]->setEnabled(m_enabled && validCell);
    m_shortcuts["navigate_right"]->setEnabled(m_enabled && validCell);
    m_shortcuts["tab_navigation"]->setEnabled(m_enabled && validCell);
    m_shortcuts["shift_tab_navigation"]->setEnabled(m_enabled && validCell);

    // Undo/redo always available when enabled
    m_shortcuts["undo"]->setEnabled(m_enabled && canUndo());
    m_shortcuts["redo"]->setEnabled(m_enabled && canRedo());

    // Confirmation shortcuts always available
    m_shortcuts["confirm"]->setEnabled(m_enabled);
    m_shortcuts["cancel"]->setEnabled(m_enabled);
}

void ShortcutManager::processAttemptRecord(AttemptResult result)
{
    if (!m_tableView || !m_tableModel || !isHeightColumn(m_currentIndex)) {
        return;
    }

    if (m_operationInProgress) {
        return; // Prevent rapid-fire operations
    }

    // Get attempt information
    int athleteId = m_currentIndex.data(AthleteTableModel::AthleteIdRole).toInt();
    int height = m_currentIndex.data(AthleteTableModel::HeightRole).toInt();

    if (athleteId <= 0 || height <= 0) {
        qWarning() << "ShortcutManager::processAttemptRecord: Invalid athlete ID or height";
        return;
    }

    // Get next attempt number
    int attemptNumber = getNextAttemptNumber(athleteId, height);
    if (attemptNumber <= 0 || attemptNumber > 3) {
        qWarning() << "ShortcutManager::processAttemptRecord: Invalid attempt number" << attemptNumber;
        return;
    }

    // Get current result for undo
    JumpAttempt::AttemptResult currentResult = m_tableModel->getAttemptResult(athleteId, height, attemptNumber);
    AttemptResult oldResult = static_cast<AttemptResult>(static_cast<int>(currentResult));

    // Create operation record
    OperationRecord operation;
    operation.type = AttemptRecord;
    operation.athleteId = athleteId;
    operation.height = height;
    operation.attemptNumber = attemptNumber;
    operation.oldResult = oldResult;
    operation.newResult = result;
    operation.timestamp = QDateTime::currentDateTime();
    operation.description = QString("Attempt %1 for athlete %2 at %3cm: %4")
                           .arg(attemptNumber).arg(athleteId).arg(height)
                           .arg(result == Success ? "O" : result == Failure ? "X" :
                                result == Skip ? "-" : "R");

    // Record operation for undo
    pushOperation(operation);

    // Emit signal for attempt recording
    emit attemptRecordRequested(athleteId, height, attemptNumber, result);

    // Set operation in progress flag
    m_operationInProgress = true;
    m_operationTimer->start();

    qDebug() << "ShortcutManager::processAttemptRecord:" << operation.description;
}

void ShortcutManager::processNavigation(int direction)
{
    if (!m_tableView || !m_tableModel) {
        return;
    }

    QModelIndex current = m_currentIndex;
    if (!current.isValid()) {
        // If no current selection, start at first cell
        current = m_tableModel->index(0, 0);
        if (current.isValid()) {
            m_tableView->setCurrentIndex(current);
        }
        return;
    }

    QModelIndex next;
    int row = current.row();
    int col = current.column();

    switch (direction) {
    case 0: // Up
        if (row > 0) {
            next = m_tableModel->index(row - 1, col);
        }
        break;
    case 1: // Down
        if (row < m_tableModel->rowCount() - 1) {
            next = m_tableModel->index(row + 1, col);
        }
        break;
    case 2: // Left
        if (col > 0) {
            next = m_tableModel->index(row, col - 1);
        }
        break;
    case 3: // Right
        if (col < m_tableModel->columnCount() - 1) {
            next = m_tableModel->index(row, col + 1);
        }
        break;
    }

    if (next.isValid()) {
        m_tableView->setCurrentIndex(next);
        emit navigationRequested(direction);
    }
}

bool ShortcutManager::isValidCell(const QModelIndex &index) const
{
    return index.isValid() && m_tableModel &&
           index.row() >= 0 && index.row() < m_tableModel->rowCount() &&
           index.column() >= 0 && index.column() < m_tableModel->columnCount();
}

bool ShortcutManager::isHeightColumn(const QModelIndex &index) const
{
    if (!isValidCell(index)) {
        return false;
    }

    return index.column() >= AthleteTableModel::FirstHeightColumn;
}

int ShortcutManager::getNextAttemptNumber(int athleteId, int height) const
{
    if (!m_tableModel) {
        return 1;
    }

    // Find the next available attempt number
    for (int attempt = 1; attempt <= 3; ++attempt) {
        JumpAttempt::AttemptResult result = m_tableModel->getAttemptResult(athleteId, height, attempt);
        if (result == JumpAttempt::Invalid) {
            return attempt;
        }
    }

    // If all attempts are filled, return the last one for overwriting
    return 3;
}

void ShortcutManager::executeUndo(const OperationRecord &operation)
{
    if (!m_tableModel || operation.type != AttemptRecord) {
        return;
    }

    // Convert back to JumpAttempt::AttemptResult
    JumpAttempt::AttemptResult jumpResult;
    switch (operation.oldResult) {
    case Success:
        jumpResult = JumpAttempt::Pass;
        break;
    case Failure:
        jumpResult = JumpAttempt::Fail;
        break;
    case Skip:
        jumpResult = JumpAttempt::Skip;
        break;
    case Retire:
        jumpResult = JumpAttempt::Retire;
        break;
    default:
        jumpResult = JumpAttempt::Invalid;
        break;
    }

    // Restore the old result
    m_tableModel->recordAttempt(operation.athleteId, operation.height,
                               operation.attemptNumber, jumpResult);

    qDebug() << "ShortcutManager::executeUndo: Restored" << operation.description;
}

void ShortcutManager::executeRedo(const OperationRecord &operation)
{
    if (!m_tableModel || operation.type != AttemptRecord) {
        return;
    }

    // Convert to JumpAttempt::AttemptResult
    JumpAttempt::AttemptResult jumpResult;
    switch (operation.newResult) {
    case Success:
        jumpResult = JumpAttempt::Pass;
        break;
    case Failure:
        jumpResult = JumpAttempt::Fail;
        break;
    case Skip:
        jumpResult = JumpAttempt::Skip;
        break;
    case Retire:
        jumpResult = JumpAttempt::Retire;
        break;
    default:
        jumpResult = JumpAttempt::Invalid;
        break;
    }

    // Apply the new result
    m_tableModel->recordAttempt(operation.athleteId, operation.height,
                               operation.attemptNumber, jumpResult);

    qDebug() << "ShortcutManager::executeRedo: Reapplied" << operation.description;
}

void ShortcutManager::pushOperation(const OperationRecord &operation)
{
    // Clear redo stack when new operation is added
    m_redoStack.clear();

    // Add to undo stack
    m_undoStack.push(operation);

    // Limit stack size
    while (m_undoStack.size() > MAX_UNDO_STACK_SIZE) {
        // Remove oldest operation from bottom of stack
        QStack<OperationRecord> tempStack;
        while (m_undoStack.size() > 1) {
            tempStack.push(m_undoStack.pop());
        }
        m_undoStack.clear();
        while (!tempStack.isEmpty()) {
            m_undoStack.push(tempStack.pop());
        }
    }

    emit undoRedoStateChanged(canUndo(), canRedo());
}
