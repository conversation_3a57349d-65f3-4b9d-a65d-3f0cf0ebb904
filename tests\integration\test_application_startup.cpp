#include <QtTest/QtTest>
#include <QApplication>
#include <QSignalSpy>
#include <QJsonArray>
#include <QJsonObject>
#include <QTimer>

#include "ui/main_window.h"
#include "ui/competition_selection_view.h"
#include "ui/view_manager.h"
#include "api/api_client.h"
#include "persistence/database_manager.h"
#include "utils/config_manager.h"

/**
 * @brief 应用启动流程集成测试
 * 
 * 测试应用启动时的完整流程，包括：
 * - 数据库初始化
 * - API客户端初始化
 * - 主窗口创建和视图管理
 * - 比赛列表获取和显示
 * - 比赛选择和数据加载
 */
class TestApplicationStartup : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void init();
    void cleanup();
    void cleanupTestCase();
    
    // 启动流程测试
    void testDatabaseInitialization();
    void testApiClientInitialization();
    void testMainWindowCreation();
    void testViewManagerInitialization();
    void testCompetitionSelectionViewDisplay();
    
    // API集成测试
    void testCompetitionFetching();
    void testCompetitionSelection();
    void testCompetitionDataLoading();
    
    // 错误处理测试
    void testNetworkErrorHandling();
    void testOfflineModeHandling();

private:
    MainWindow *m_mainWindow;
    QJsonArray createMockCompetitions();
    QJsonObject createMockCompetition(int id, const QString &name, const QString &status = "not_started");
    void simulateApiResponse(const QJsonArray &competitions);
    void simulateNetworkError(const QString &errorMessage);
};

void TestApplicationStartup::initTestCase()
{
    // 设置测试环境
    QApplication::setApplicationName("High Jump Scorer Test");
    QApplication::setApplicationVersion("1.0.0-test");
    QApplication::setOrganizationName("Test Organization");
    
    // 初始化核心管理器
    ConfigManager::instance()->initialize();
    DatabaseManager::instance()->initialize();
    ApiClient::instance()->initialize();
}

void TestApplicationStartup::init()
{
    // 每个测试前创建新的主窗口
    m_mainWindow = new MainWindow();
}

void TestApplicationStartup::cleanup()
{
    // 每个测试后清理
    if (m_mainWindow) {
        delete m_mainWindow;
        m_mainWindow = nullptr;
    }
}

void TestApplicationStartup::cleanupTestCase()
{
    // 测试类结束后的清理
}

void TestApplicationStartup::testDatabaseInitialization()
{
    // 测试数据库管理器是否正确初始化
    DatabaseManager *dbManager = DatabaseManager::instance();
    QVERIFY(dbManager != nullptr);
    QVERIFY(dbManager->isInitialized());
    QVERIFY(!dbManager->getDatabasePath().isEmpty());
}

void TestApplicationStartup::testApiClientInitialization()
{
    // 测试API客户端是否正确初始化
    ApiClient *apiClient = ApiClient::instance();
    QVERIFY(apiClient != nullptr);
    QVERIFY(apiClient->isInitialized());
}

void TestApplicationStartup::testMainWindowCreation()
{
    // 测试主窗口是否正确创建
    QVERIFY(m_mainWindow != nullptr);
    
    // 检查窗口基本属性
    QVERIFY(m_mainWindow->minimumSize().width() >= 1200);
    QVERIFY(m_mainWindow->minimumSize().height() >= 800);
    
    // 检查窗口标题
    QVERIFY(m_mainWindow->windowTitle().contains("High Jump"));
}

void TestApplicationStartup::testViewManagerInitialization()
{
    // 测试视图管理器是否正确初始化
    ViewManager *viewManager = m_mainWindow->findChild<ViewManager*>();
    QVERIFY(viewManager != nullptr);
    
    // 检查初始视图是否为比赛选择视图
    QCOMPARE(viewManager->currentViewType(), ViewManager::CompetitionSelection);
}

void TestApplicationStartup::testCompetitionSelectionViewDisplay()
{
    // 测试比赛选择视图是否正确显示
    CompetitionSelectionView *selectionView = m_mainWindow->findChild<CompetitionSelectionView*>();
    QVERIFY(selectionView != nullptr);
    QVERIFY(selectionView->isVisible());
    
    // 检查初始状态
    QCOMPARE(selectionView->selectedCompetitionId(), -1);
}

void TestApplicationStartup::testCompetitionFetching()
{
    // 测试比赛列表获取功能
    CompetitionSelectionView *selectionView = m_mainWindow->findChild<CompetitionSelectionView*>();
    QVERIFY(selectionView != nullptr);
    
    // 设置信号监听
    QSignalSpy loadingSpy(selectionView, &CompetitionSelectionView::showLoadingState);
    
    // 模拟API响应
    QJsonArray mockCompetitions = createMockCompetitions();
    simulateApiResponse(mockCompetitions);
    
    // 等待信号处理
    QTest::qWait(100);
    
    // 检查比赛是否加载到视图中
    auto competitionsList = selectionView->findChild<QListWidget*>();
    QVERIFY(competitionsList != nullptr);
    QCOMPARE(competitionsList->count(), mockCompetitions.size());
}

void TestApplicationStartup::testCompetitionSelection()
{
    // 测试比赛选择功能
    CompetitionSelectionView *selectionView = m_mainWindow->findChild<CompetitionSelectionView*>();
    QVERIFY(selectionView != nullptr);
    
    // 加载测试数据
    QJsonArray mockCompetitions = createMockCompetitions();
    selectionView->loadCompetitions(mockCompetitions);
    
    // 设置信号监听
    QSignalSpy selectionSpy(selectionView, &CompetitionSelectionView::competitionSelected);
    
    // 模拟选择第一个比赛
    auto competitionsList = selectionView->findChild<QListWidget*>();
    competitionsList->setCurrentRow(0);
    
    // 模拟点击选择按钮
    auto buttons = selectionView->findChildren<QPushButton*>();
    for (auto button : buttons) {
        if (button->text().contains("选择")) {
            button->click();
            break;
        }
    }
    
    // 检查信号是否发出
    QCOMPARE(selectionSpy.count(), 1);
    QCOMPARE(selectionSpy.takeFirst().at(0).toInt(), 1);
}

void TestApplicationStartup::testCompetitionDataLoading()
{
    // 测试比赛数据加载功能
    CompetitionSelectionView *selectionView = m_mainWindow->findChild<CompetitionSelectionView*>();
    ViewManager *viewManager = m_mainWindow->findChild<ViewManager*>();
    
    QVERIFY(selectionView != nullptr);
    QVERIFY(viewManager != nullptr);
    
    // 加载测试数据
    QJsonArray mockCompetitions = createMockCompetitions();
    selectionView->loadCompetitions(mockCompetitions);
    
    // 模拟选择比赛
    auto competitionsList = selectionView->findChild<QListWidget*>();
    competitionsList->setCurrentRow(0);
    
    // 触发比赛选择
    emit selectionView->competitionSelected(1);
    
    // 等待处理
    QTest::qWait(100);
    
    // 检查是否切换到主计分视图
    QCOMPARE(viewManager->currentViewType(), ViewManager::MainScoring);
}

void TestApplicationStartup::testNetworkErrorHandling()
{
    // 测试网络错误处理
    CompetitionSelectionView *selectionView = m_mainWindow->findChild<CompetitionSelectionView*>();
    QVERIFY(selectionView != nullptr);
    
    // 模拟网络错误
    simulateNetworkError("网络连接失败");
    
    // 等待错误处理
    QTest::qWait(100);
    
    // 检查是否显示错误信息
    bool foundErrorMessage = false;
    for (auto label : selectionView->findChildren<QLabel*>()) {
        if (label->text().contains("网络连接失败")) {
            foundErrorMessage = true;
            QVERIFY(label->isVisible());
            break;
        }
    }
    QVERIFY(foundErrorMessage);
}

void TestApplicationStartup::testOfflineModeHandling()
{
    // 测试离线模式处理
    CompetitionSelectionView *selectionView = m_mainWindow->findChild<CompetitionSelectionView*>();
    QVERIFY(selectionView != nullptr);
    
    // 模拟离线模式
    selectionView->showOfflineMode();
    
    // 检查是否显示离线模式提示
    bool foundOfflineMessage = false;
    for (auto label : selectionView->findChildren<QLabel*>()) {
        if (label->text().contains("离线模式")) {
            foundOfflineMessage = true;
            QVERIFY(label->isVisible());
            break;
        }
    }
    QVERIFY(foundOfflineMessage);
}

QJsonArray TestApplicationStartup::createMockCompetitions()
{
    QJsonArray competitions;
    competitions.append(createMockCompetition(1, "2024年全国跳高锦标赛", "not_started"));
    competitions.append(createMockCompetition(2, "区域选拔赛", "in_progress"));
    competitions.append(createMockCompetition(3, "青年组比赛", "finished"));
    return competitions;
}

QJsonObject TestApplicationStartup::createMockCompetition(int id, const QString &name, const QString &status)
{
    QJsonObject competition;
    competition["id"] = id;
    competition["name"] = name;
    competition["date"] = "2024-08-15T10:00:00Z";
    competition["venue"] = "国家体育场";
    competition["status"] = status;
    competition["starting_height"] = 150;
    competition["height_increment"] = 3;
    return competition;
}

void TestApplicationStartup::simulateApiResponse(const QJsonArray &competitions)
{
    // 模拟API响应
    QTimer::singleShot(50, [this, competitions]() {
        emit ApiClient::instance()->competitionsReceived(competitions);
    });
}

void TestApplicationStartup::simulateNetworkError(const QString &errorMessage)
{
    // 模拟网络错误
    QTimer::singleShot(50, [this, errorMessage]() {
        emit ApiClient::instance()->networkError(errorMessage);
    });
}

QTEST_MAIN(TestApplicationStartup)
#include "test_application_startup.moc"
