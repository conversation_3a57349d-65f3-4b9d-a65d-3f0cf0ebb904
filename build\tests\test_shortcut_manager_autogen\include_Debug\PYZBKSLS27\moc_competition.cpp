/****************************************************************************
** Meta object code from reading C++ file 'competition.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../src/models/competition.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'competition.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN11CompetitionE_t {};
} // unnamed namespace

template <> constexpr inline auto Competition::qt_create_metaobjectdata<qt_meta_tag_ZN11CompetitionE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "Competition",
        "idChanged",
        "",
        "nameChanged",
        "venueChanged",
        "dateChanged",
        "statusChanged",
        "currentHeightChanged",
        "descriptionChanged",
        "athleteAdded",
        "Athlete*",
        "athlete",
        "athleteRemoved",
        "competitionStarted",
        "competitionEnded",
        "heightChanged",
        "newHeight",
        "id",
        "name",
        "venue",
        "date",
        "status",
        "CompetitionStatus",
        "currentHeight",
        "description",
        "NotStarted",
        "InProgress",
        "Completed",
        "Cancelled"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'idChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'nameChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'venueChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'dateChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'statusChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentHeightChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'descriptionChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'athleteAdded'
        QtMocHelpers::SignalData<void(Athlete *)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 10, 11 },
        }}),
        // Signal 'athleteRemoved'
        QtMocHelpers::SignalData<void(Athlete *)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 10, 11 },
        }}),
        // Signal 'competitionStarted'
        QtMocHelpers::SignalData<void()>(13, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'competitionEnded'
        QtMocHelpers::SignalData<void()>(14, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'heightChanged'
        QtMocHelpers::SignalData<void(int)>(15, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 16 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'id'
        QtMocHelpers::PropertyData<int>(17, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 0),
        // property 'name'
        QtMocHelpers::PropertyData<QString>(18, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 1),
        // property 'venue'
        QtMocHelpers::PropertyData<QString>(19, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 2),
        // property 'date'
        QtMocHelpers::PropertyData<QDateTime>(20, QMetaType::QDateTime, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 3),
        // property 'status'
        QtMocHelpers::PropertyData<CompetitionStatus>(21, 0x80000000 | 22, QMC::DefaultPropertyFlags | QMC::Writable | QMC::EnumOrFlag | QMC::StdCppSet, 4),
        // property 'currentHeight'
        QtMocHelpers::PropertyData<int>(23, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 5),
        // property 'description'
        QtMocHelpers::PropertyData<QString>(24, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 6),
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'CompetitionStatus'
        QtMocHelpers::EnumData<CompetitionStatus>(22, 22, QMC::EnumFlags{}).add({
            {   25, CompetitionStatus::NotStarted },
            {   26, CompetitionStatus::InProgress },
            {   27, CompetitionStatus::Completed },
            {   28, CompetitionStatus::Cancelled },
        }),
    };
    return QtMocHelpers::metaObjectData<Competition, qt_meta_tag_ZN11CompetitionE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject Competition::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11CompetitionE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11CompetitionE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN11CompetitionE_t>.metaTypes,
    nullptr
} };

void Competition::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<Competition *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->idChanged(); break;
        case 1: _t->nameChanged(); break;
        case 2: _t->venueChanged(); break;
        case 3: _t->dateChanged(); break;
        case 4: _t->statusChanged(); break;
        case 5: _t->currentHeightChanged(); break;
        case 6: _t->descriptionChanged(); break;
        case 7: _t->athleteAdded((*reinterpret_cast< std::add_pointer_t<Athlete*>>(_a[1]))); break;
        case 8: _t->athleteRemoved((*reinterpret_cast< std::add_pointer_t<Athlete*>>(_a[1]))); break;
        case 9: _t->competitionStarted(); break;
        case 10: _t->competitionEnded(); break;
        case 11: _t->heightChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (Competition::*)()>(_a, &Competition::idChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (Competition::*)()>(_a, &Competition::nameChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (Competition::*)()>(_a, &Competition::venueChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (Competition::*)()>(_a, &Competition::dateChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (Competition::*)()>(_a, &Competition::statusChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (Competition::*)()>(_a, &Competition::currentHeightChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (Competition::*)()>(_a, &Competition::descriptionChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (Competition::*)(Athlete * )>(_a, &Competition::athleteAdded, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (Competition::*)(Athlete * )>(_a, &Competition::athleteRemoved, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (Competition::*)()>(_a, &Competition::competitionStarted, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (Competition::*)()>(_a, &Competition::competitionEnded, 10))
            return;
        if (QtMocHelpers::indexOfMethod<void (Competition::*)(int )>(_a, &Competition::heightChanged, 11))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->id(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->name(); break;
        case 2: *reinterpret_cast<QString*>(_v) = _t->venue(); break;
        case 3: *reinterpret_cast<QDateTime*>(_v) = _t->date(); break;
        case 4: *reinterpret_cast<CompetitionStatus*>(_v) = _t->status(); break;
        case 5: *reinterpret_cast<int*>(_v) = _t->currentHeight(); break;
        case 6: *reinterpret_cast<QString*>(_v) = _t->description(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setId(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setName(*reinterpret_cast<QString*>(_v)); break;
        case 2: _t->setVenue(*reinterpret_cast<QString*>(_v)); break;
        case 3: _t->setDate(*reinterpret_cast<QDateTime*>(_v)); break;
        case 4: _t->setStatus(*reinterpret_cast<CompetitionStatus*>(_v)); break;
        case 5: _t->setCurrentHeight(*reinterpret_cast<int*>(_v)); break;
        case 6: _t->setDescription(*reinterpret_cast<QString*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *Competition::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *Competition::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11CompetitionE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int Competition::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 12;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void Competition::idChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void Competition::nameChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void Competition::venueChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void Competition::dateChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void Competition::statusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void Competition::currentHeightChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void Competition::descriptionChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void Competition::athleteAdded(Athlete * _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 7, nullptr, _t1);
}

// SIGNAL 8
void Competition::athleteRemoved(Athlete * _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 8, nullptr, _t1);
}

// SIGNAL 9
void Competition::competitionStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 9, nullptr);
}

// SIGNAL 10
void Competition::competitionEnded()
{
    QMetaObject::activate(this, &staticMetaObject, 10, nullptr);
}

// SIGNAL 11
void Competition::heightChanged(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 11, nullptr, _t1);
}
QT_WARNING_POP
