#include "view_manager.h"
#include <QDebug>

ViewManager::ViewManager(QStackedWidget *container, QObject *parent)
    : QObject(parent)
    , m_container(container)
    , m_currentViewType(CompetitionSelection)
    , m_isTransitioning(false)
{
    if (!m_container) {
        qWarning() << "ViewManager: Container is null";
        return;
    }

    qDebug() << "ViewManager: Initialized with container";
}

ViewManager::~ViewManager()
{
    // Qt的父子对象系统会自动清理
}

bool ViewManager::switchToView(ViewType viewType)
{
    if (!m_container) {
        const QString error = tr("Container widget is null");
        qCritical() << "ViewManager::switchToView:" << error;
        emit viewChangeFailed(viewType, error);
        return false;
    }

    if (m_isTransitioning) {
        const QString error = tr("View transition already in progress");
        qWarning() << "ViewManager::switchToView:" << error;
        emit viewChangeFailed(viewType, error);
        return false;
    }

    if (!m_views.contains(viewType)) {
        const QString error = tr("View type %1 is not registered").arg(viewTypeToString(viewType));
        qWarning() << "ViewManager::switchToView:" << error;
        emit viewChangeFailed(viewType, error);
        return false;
    }

    QWidget *targetView = m_views[viewType];
    if (!targetView) {
        const QString error = tr("Target view widget is null");
        qCritical() << "ViewManager::switchToView:" << error;
        emit viewChangeFailed(viewType, error);
        return false;
    }

    // 如果已经是当前视图，直接返回成功
    if (m_currentViewType == viewType && m_container->currentWidget() == targetView) {
        qDebug() << "ViewManager::switchToView: Already on target view" << viewTypeToString(viewType);
        return true;
    }

    m_isTransitioning = true;
    ViewType previousViewType = m_currentViewType;

    emit viewChangeRequested(previousViewType, viewType);

    // 执行视图切换
    m_container->setCurrentWidget(targetView);
    m_currentViewType = viewType;

    qDebug() << "ViewManager::switchToView: Switched from" << viewTypeToString(previousViewType)
             << "to" << viewTypeToString(viewType);

    m_isTransitioning = false;
    emit viewChanged(viewType);

    return true;
}

bool ViewManager::registerView(ViewType viewType, QWidget *widget)
{
    if (!widget) {
        qWarning() << "ViewManager::registerView: Widget is null for view type" << viewTypeToString(viewType);
        return false;
    }

    if (!m_container) {
        qWarning() << "ViewManager::registerView: Container is null";
        return false;
    }

    // 如果视图已经注册，先移除旧的
    if (m_views.contains(viewType)) {
        QWidget *oldWidget = m_views[viewType];
        if (oldWidget && oldWidget != widget) {
            disconnectViewSignals(oldWidget);
            m_container->removeWidget(oldWidget);
            qDebug() << "ViewManager::registerView: Replaced existing view" << viewTypeToString(viewType);
        }
    }

    // 注册新视图
    m_views[viewType] = widget;
    m_container->addWidget(widget);
    connectViewSignals(widget);

    qDebug() << "ViewManager::registerView: Registered view" << viewTypeToString(viewType);
    return true;
}

ViewManager::ViewType ViewManager::currentViewType() const
{
    return m_currentViewType;
}

QWidget* ViewManager::getView(ViewType viewType) const
{
    return m_views.value(viewType, nullptr);
}

bool ViewManager::isViewRegistered(ViewType viewType) const
{
    return m_views.contains(viewType) && m_views[viewType] != nullptr;
}

void ViewManager::onViewWidgetDestroyed(QObject *obj)
{
    // 从哈希表中移除被销毁的视图
    for (auto it = m_views.begin(); it != m_views.end(); ++it) {
        if (it.value() == obj) {
            qDebug() << "ViewManager::onViewWidgetDestroyed: Removing destroyed view" 
                     << viewTypeToString(it.key());
            m_views.erase(it);
            break;
        }
    }
}

void ViewManager::connectViewSignals(QWidget *widget)
{
    if (!widget) {
        return;
    }

    // 连接widget销毁信号以自动清理
    connect(widget, &QObject::destroyed, this, &ViewManager::onViewWidgetDestroyed);
}

void ViewManager::disconnectViewSignals(QWidget *widget)
{
    if (!widget) {
        return;
    }

    // 断开所有相关信号
    disconnect(widget, &QObject::destroyed, this, &ViewManager::onViewWidgetDestroyed);
}

QString ViewManager::viewTypeToString(ViewType viewType) const
{
    switch (viewType) {
    case CompetitionSelection:
        return "CompetitionSelection";
    case MainScoring:
        return "MainScoring";
    case ResultsView:
        return "ResultsView";
    default:
        return QString("Unknown(%1)").arg(static_cast<int>(viewType));
    }
}