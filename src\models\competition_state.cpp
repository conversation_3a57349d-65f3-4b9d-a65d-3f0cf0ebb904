#include "competition_state.h"
#include "competition.h"
#include "ranking_calculator.h"
#include "persistence/database_manager.h"
#include "athlete.h"
#include <QDebug>
#include <QMutexLocker>
#include <algorithm>

CompetitionState::CompetitionState(QObject *parent)
    : QObject(parent)
    , m_competition(nullptr)
    , m_rankingCalculator(nullptr)
    , m_dbManager(nullptr)
    , m_currentPhase(NotStarted)
    , m_currentHeight(0)
    , m_currentRound(0)
    , m_currentAthleteId(-1)
    , m_totalAthletes(0)
    , m_activeAthletes(0)
    , m_completedAthletes(0)
    , m_retiredAthletes(0)
    , m_autoSaveEnabled(false)
    , m_autoSaveInterval(DEFAULT_AUTO_SAVE_INTERVAL)
    , m_autoSaveTimer(new QTimer(this))
    , m_stateUpdateTimer(new QTimer(this))
{
    setupTimers();
    initializeState();
    
    qDebug() << "CompetitionState: Initialized";
}

CompetitionState::~CompetitionState()
{
    // Save state before destruction if auto-save is enabled
    if (m_autoSaveEnabled && m_currentPhase == InProgress) {
        saveState();
    }
    
    qDebug() << "CompetitionState: Destroyed";
}

void CompetitionState::loadCompetition(Competition *competition)
{
    if (!competition) {
        qWarning() << "CompetitionState::loadCompetition: Null competition provided";
        return;
    }
    
    QMutexLocker locker(&m_stateMutex);
    
    m_competition = competition;
    
    // Initialize state from competition
    QList<int> heights = competition->getHeightProgression();
    if (!heights.isEmpty()) {
        m_currentHeight = heights.first();
    }
    
    m_totalAthletes = competition->getAthletes().size();
    m_currentRound = 1;
    m_currentPhase = NotStarted;
    
    // Initialize athlete statuses
    m_athleteStatuses.clear();
    m_athleteBestHeights.clear();
    m_athleteRanks.clear();
    
    for (Athlete *athlete : competition->getAthletes()) {
        if (athlete) {
            int athleteId = athlete->id();
            m_athleteStatuses[athleteId] = Waiting;
            m_athleteBestHeights[athleteId] = 0;
            m_athleteRanks[athleteId] = 0;
        }
    }
    
    calculateStatistics();
    
    qDebug() << "CompetitionState::loadCompetition: Loaded competition" << competition->name()
             << "with" << m_totalAthletes << "athletes";
}

void CompetitionState::startCompetition()
{
    if (!canStartCompetition()) {
        qWarning() << "CompetitionState::startCompetition: Cannot start competition";
        return;
    }
    
    QMutexLocker locker(&m_stateMutex);
    
    m_currentPhase = InProgress;
    m_startTime = QDateTime::currentDateTime();
    m_lastUpdateTime = m_startTime;
    
    // Update athlete statuses to active
    for (auto it = m_athleteStatuses.begin(); it != m_athleteStatuses.end(); ++it) {
        if (it.value() == Waiting) {
            it.value() = Active;
        }
    }
    
    calculateStatistics();
    
    // Start timers
    if (m_autoSaveEnabled) {
        m_autoSaveTimer->start();
    }
    m_stateUpdateTimer->start();
    
    emit phaseChanged(m_currentPhase);
    
    qDebug() << "CompetitionState::startCompetition: Competition started";
}

void CompetitionState::pauseCompetition()
{
    if (m_currentPhase != InProgress) {
        return;
    }
    
    QMutexLocker locker(&m_stateMutex);
    
    m_currentPhase = Paused;
    m_lastUpdateTime = QDateTime::currentDateTime();
    
    // Stop timers
    m_autoSaveTimer->stop();
    m_stateUpdateTimer->stop();
    
    emit phaseChanged(m_currentPhase);
    
    qDebug() << "CompetitionState::pauseCompetition: Competition paused";
}

void CompetitionState::resumeCompetition()
{
    if (m_currentPhase != Paused) {
        return;
    }
    
    QMutexLocker locker(&m_stateMutex);
    
    m_currentPhase = InProgress;
    m_lastUpdateTime = QDateTime::currentDateTime();
    
    // Restart timers
    if (m_autoSaveEnabled) {
        m_autoSaveTimer->start();
    }
    m_stateUpdateTimer->start();
    
    emit phaseChanged(m_currentPhase);
    
    qDebug() << "CompetitionState::resumeCompetition: Competition resumed";
}

void CompetitionState::finishCompetition()
{
    if (m_currentPhase == Finished || m_currentPhase == Cancelled) {
        return;
    }
    
    QMutexLocker locker(&m_stateMutex);
    
    m_currentPhase = Finished;
    m_lastUpdateTime = QDateTime::currentDateTime();
    
    // Update remaining active athletes to finished
    for (auto it = m_athleteStatuses.begin(); it != m_athleteStatuses.end(); ++it) {
        if (it.value() == Active) {
            it.value() = Finished;
        }
    }
    
    calculateStatistics();
    
    // Stop timers
    m_autoSaveTimer->stop();
    m_stateUpdateTimer->stop();
    
    // Final save
    saveState();
    
    emit phaseChanged(m_currentPhase);
    
    qDebug() << "CompetitionState::finishCompetition: Competition finished";
}

void CompetitionState::cancelCompetition()
{
    QMutexLocker locker(&m_stateMutex);
    
    m_currentPhase = Cancelled;
    m_lastUpdateTime = QDateTime::currentDateTime();
    
    // Stop timers
    m_autoSaveTimer->stop();
    m_stateUpdateTimer->stop();
    
    emit phaseChanged(m_currentPhase);
    
    qDebug() << "CompetitionState::cancelCompetition: Competition cancelled";
}

CompetitionState::CompetitionPhase CompetitionState::getCurrentPhase() const
{
    QMutexLocker locker(&m_stateMutex);
    return m_currentPhase;
}

int CompetitionState::getCurrentHeight() const
{
    QMutexLocker locker(&m_stateMutex);
    return m_currentHeight;
}

int CompetitionState::getCurrentRound() const
{
    QMutexLocker locker(&m_stateMutex);
    return m_currentRound;
}

int CompetitionState::getCurrentAthleteId() const
{
    QMutexLocker locker(&m_stateMutex);
    return m_currentAthleteId;
}

void CompetitionState::setCurrentAthleteId(int athleteId)
{
    QMutexLocker locker(&m_stateMutex);
    if (m_currentAthleteId != athleteId) {
        m_currentAthleteId = athleteId;
        emit currentAthleteChanged(athleteId);
        qDebug() << "CompetitionState::setCurrentAthleteId: Focus moved to athlete" << athleteId;
    }
}

int CompetitionState::moveToNextAthlete()
{
    QMutexLocker locker(&m_stateMutex);

    if (!m_competition) {
        qWarning() << "CompetitionState::moveToNextAthlete: No competition loaded";
        return -1;
    }

    int nextAthleteId = getNextAthleteId();
    if (nextAthleteId != -1) {
        m_currentAthleteId = nextAthleteId;
        emit currentAthleteChanged(nextAthleteId);
        qDebug() << "CompetitionState::moveToNextAthlete: Moved to athlete" << nextAthleteId;
    }

    return nextAthleteId;
}

int CompetitionState::getNextAthleteId() const
{
    if (!m_competition) {
        return -1;
    }

    // Get all active athletes
    QList<Athlete*> athletes = m_competition->getAthletes();
    QList<int> activeAthleteIds;

    for (Athlete *athlete : athletes) {
        if (athlete && m_athleteStatuses.value(athlete->id(), Active) == Active) {
            activeAthleteIds.append(athlete->id());
        }
    }

    if (activeAthleteIds.isEmpty()) {
        return -1; // No active athletes
    }

    // Sort athlete IDs for consistent ordering
    std::sort(activeAthleteIds.begin(), activeAthleteIds.end());

    // Find current athlete position
    int currentIndex = activeAthleteIds.indexOf(m_currentAthleteId);

    // If current athlete not found or is last, return first athlete
    if (currentIndex == -1 || currentIndex >= activeAthleteIds.size() - 1) {
        return activeAthleteIds.first();
    }

    // Return next athlete
    return activeAthleteIds.at(currentIndex + 1);
}

QDateTime CompetitionState::getStartTime() const
{
    QMutexLocker locker(&m_stateMutex);
    return m_startTime;
}

qint64 CompetitionState::getElapsedTime() const
{
    QMutexLocker locker(&m_stateMutex);
    
    if (!m_startTime.isValid()) {
        return 0;
    }
    
    QDateTime endTime = (m_currentPhase == Finished || m_currentPhase == Cancelled) 
                       ? m_lastUpdateTime : QDateTime::currentDateTime();
    
    return m_startTime.secsTo(endTime);
}

CompetitionState::AthleteStatus CompetitionState::getAthleteStatus(int athleteId) const
{
    QMutexLocker locker(&m_stateMutex);
    return m_athleteStatuses.value(athleteId, Waiting);
}

int CompetitionState::getActiveAthleteCount() const
{
    QMutexLocker locker(&m_stateMutex);
    return m_activeAthletes;
}

bool CompetitionState::canStartCompetition() const
{
    QMutexLocker locker(&m_stateMutex);
    return m_competition && m_currentPhase == NotStarted && m_totalAthletes > 0;
}

bool CompetitionState::shouldFinishCompetition() const
{
    QMutexLocker locker(&m_stateMutex);
    
    if (m_currentPhase != InProgress) {
        return false;
    }
    
    // Competition should finish if no athletes are active
    return m_activeAthletes == 0 && m_totalAthletes > 0;
}

void CompetitionState::setCurrentHeight(int height)
{
    QMutexLocker locker(&m_stateMutex);
    setCurrentHeightLocked(height);
}

void CompetitionState::setCurrentHeightLocked(int height)
{
    // Private locked version - assumes caller already holds the mutex
    if (m_currentHeight == height) {
        return;
    }

    m_currentHeight = height;
    m_lastUpdateTime = QDateTime::currentDateTime();

    // Emit signal using queued connection to avoid potential issues with signal emission while holding lock
    QMetaObject::invokeMethod(this, [this, height]() {
        emit currentHeightChanged(height);
    }, Qt::QueuedConnection);

    qDebug() << "CompetitionState::setCurrentHeightLocked: Height set to" << height;
}

bool CompetitionState::advanceToNextHeight()
{
    QMutexLocker locker(&m_stateMutex);

    if (!m_competition) {
        return false;
    }

    QList<int> heights = m_competition->getHeightProgression();
    int currentIndex = heights.indexOf(m_currentHeight);

    if (currentIndex < 0 || currentIndex >= heights.size() - 1) {
        // No more heights - check if competition should finish
        bool shouldFinish = (m_currentPhase == InProgress && m_activeAthletes == 0 && m_totalAthletes > 0);
        if (shouldFinish) {
            // Release lock before emitting signal to avoid potential deadlock
            locker.unlock();
            emit competitionShouldFinish();
            return false;
        }
        return false;
    }

    int nextHeight = heights[currentIndex + 1];
    setCurrentHeightLocked(nextHeight);  // Use locked version to avoid double locking
    m_currentRound++;

    qDebug() << "CompetitionState::advanceToNextHeight: Advanced to" << nextHeight << "round" << m_currentRound;

    return true;
}

int CompetitionState::getNextHeight() const
{
    if (!m_competition) {
        return -1;
    }
    
    QList<int> heights = m_competition->getHeightProgression();
    int currentIndex = heights.indexOf(m_currentHeight);
    
    if (currentIndex < 0 || currentIndex >= heights.size() - 1) {
        return -1;
    }
    
    return heights[currentIndex + 1];
}

void CompetitionState::updateAthleteStatus(int athleteId, AthleteStatus status)
{
    QMutexLocker locker(&m_stateMutex);

    AthleteStatus oldStatus = m_athleteStatuses.value(athleteId, Waiting);
    if (oldStatus == status) {
        return;
    }

    m_athleteStatuses[athleteId] = status;
    m_lastUpdateTime = QDateTime::currentDateTime();

    calculateStatistics();

    emit athleteStatusChanged(athleteId, status);

    qDebug() << "CompetitionState::updateAthleteStatus: Athlete" << athleteId
             << "status changed from" << oldStatus << "to" << status;
}

void CompetitionState::updateAthleteBestHeight(int athleteId, int height)
{
    QMutexLocker locker(&m_stateMutex);

    int oldHeight = m_athleteBestHeights.value(athleteId, 0);
    if (height > oldHeight) {
        m_athleteBestHeights[athleteId] = height;
        m_lastUpdateTime = QDateTime::currentDateTime();

        qDebug() << "CompetitionState::updateAthleteBestHeight: Athlete" << athleteId
                 << "best height updated to" << height;
    }
}

void CompetitionState::updateAthleteRank(int athleteId, int rank)
{
    QMutexLocker locker(&m_stateMutex);

    m_athleteRanks[athleteId] = rank;
    m_lastUpdateTime = QDateTime::currentDateTime();
}

bool CompetitionState::saveState()
{
    if (!m_dbManager) {
        qWarning() << "CompetitionState::saveState: No database manager set";
        return false;
    }

    bool success = saveStateToDatabase();
    emit stateSaved(success);

    if (success) {
        qDebug() << "CompetitionState::saveState: State saved successfully";
    } else {
        qWarning() << "CompetitionState::saveState: Failed to save state";
    }

    return success;
}

bool CompetitionState::loadState()
{
    if (!m_dbManager) {
        qWarning() << "CompetitionState::loadState: No database manager set";
        return false;
    }

    bool success = loadStateFromDatabase();
    emit stateLoaded(success);

    if (success) {
        qDebug() << "CompetitionState::loadState: State loaded successfully";
    } else {
        qWarning() << "CompetitionState::loadState: Failed to load state";
    }

    return success;
}

void CompetitionState::setAutoSave(bool enabled, int intervalSeconds)
{
    QMutexLocker locker(&m_stateMutex);

    m_autoSaveEnabled = enabled;
    m_autoSaveInterval = intervalSeconds;

    if (enabled) {
        m_autoSaveTimer->setInterval(intervalSeconds * 1000);
        if (m_currentPhase == InProgress) {
            m_autoSaveTimer->start();
        }
    } else {
        m_autoSaveTimer->stop();
    }

    qDebug() << "CompetitionState::setAutoSave:" << enabled << "interval:" << intervalSeconds;
}

CompetitionState::StateSnapshot CompetitionState::createSnapshot() const
{
    QMutexLocker locker(&m_stateMutex);

    StateSnapshot snapshot;
    snapshot.phase = m_currentPhase;
    snapshot.currentHeight = m_currentHeight;
    snapshot.currentRound = m_currentRound;
    snapshot.startTime = m_startTime;
    snapshot.lastUpdateTime = m_lastUpdateTime;
    snapshot.athleteStatuses = m_athleteStatuses;
    snapshot.athleteBestHeights = m_athleteBestHeights;
    snapshot.athleteRanks = m_athleteRanks;
    snapshot.totalAthletes = m_totalAthletes;
    snapshot.activeAthletes = m_activeAthletes;
    snapshot.completedAthletes = m_completedAthletes;
    snapshot.retiredAthletes = m_retiredAthletes;
    snapshot.isAutoSaveEnabled = m_autoSaveEnabled;
    snapshot.notes = QString(); // NOTE: Notes functionality planned for v1.1

    return snapshot;
}

void CompetitionState::restoreFromSnapshot(const StateSnapshot &snapshot)
{
    QMutexLocker locker(&m_stateMutex);

    m_currentPhase = snapshot.phase;
    m_currentHeight = snapshot.currentHeight;
    m_currentRound = snapshot.currentRound;
    m_startTime = snapshot.startTime;
    m_lastUpdateTime = snapshot.lastUpdateTime;
    m_athleteStatuses = snapshot.athleteStatuses;
    m_athleteBestHeights = snapshot.athleteBestHeights;
    m_athleteRanks = snapshot.athleteRanks;
    m_totalAthletes = snapshot.totalAthletes;
    m_activeAthletes = snapshot.activeAthletes;
    m_completedAthletes = snapshot.completedAthletes;
    m_retiredAthletes = snapshot.retiredAthletes;
    m_autoSaveEnabled = snapshot.isAutoSaveEnabled;

    // Emit signals for state changes
    emit phaseChanged(m_currentPhase);
    emit currentHeightChanged(m_currentHeight);
    emit statisticsUpdated(m_totalAthletes, m_activeAthletes, m_completedAthletes, m_retiredAthletes);

    qDebug() << "CompetitionState::restoreFromSnapshot: State restored from snapshot";
}

void CompetitionState::setRankingCalculator(RankingCalculator *calculator)
{
    m_rankingCalculator = calculator;

    if (calculator) {
        connect(calculator, &RankingCalculator::competitionStateChanged,
                this, [this](int activeAthletes, int completedAthletes, bool isFinished) {
                    Q_UNUSED(activeAthletes)
                    Q_UNUSED(completedAthletes)
                    if (isFinished && m_currentPhase == InProgress) {
                        emit competitionShouldFinish();
                    }
                });
    }

    qDebug() << "CompetitionState::setRankingCalculator: Ranking calculator set";
}

void CompetitionState::setDatabaseManager(DatabaseManager *dbManager)
{
    m_dbManager = dbManager;
    qDebug() << "CompetitionState::setDatabaseManager: Database manager set";
}

void CompetitionState::refreshState()
{
    QMutexLocker locker(&m_stateMutex);

    calculateAthleteStatuses();
    calculateStatistics();
    checkCompetitionEnd();

    m_lastUpdateTime = QDateTime::currentDateTime();
}

void CompetitionState::onAttemptResultUpdated(int athleteId, int height, int result)
{
    Q_UNUSED(result)

    // Update athlete's best height if this was a successful attempt
    if (result == 0) { // Assuming 0 = success
        updateAthleteBestHeight(athleteId, height);
    }

    // Refresh state to recalculate statuses
    refreshState();
}

void CompetitionState::onRankingsUpdated()
{
    // Refresh state when rankings are updated
    refreshState();
}

void CompetitionState::onAutoSaveTimer()
{
    if (m_currentPhase == InProgress) {
        saveState();
    }
}

void CompetitionState::onStateUpdateTimer()
{
    refreshState();
}

void CompetitionState::initializeState()
{
    QMutexLocker locker(&m_stateMutex);

    m_currentPhase = NotStarted;
    m_currentHeight = 0;
    m_currentRound = 0;
    m_totalAthletes = 0;
    m_activeAthletes = 0;
    m_completedAthletes = 0;
    m_retiredAthletes = 0;
    m_autoSaveEnabled = false;

    m_athleteStatuses.clear();
    m_athleteBestHeights.clear();
    m_athleteRanks.clear();
}

void CompetitionState::setupTimers()
{
    // Setup auto-save timer
    m_autoSaveTimer->setSingleShot(false);
    m_autoSaveTimer->setInterval(m_autoSaveInterval * 1000);
    connect(m_autoSaveTimer, &QTimer::timeout, this, &CompetitionState::onAutoSaveTimer);

    // Setup state update timer
    m_stateUpdateTimer->setSingleShot(false);
    m_stateUpdateTimer->setInterval(STATE_UPDATE_INTERVAL);
    connect(m_stateUpdateTimer, &QTimer::timeout, this, &CompetitionState::onStateUpdateTimer);
}

void CompetitionState::calculateAthleteStatuses()
{
    // TODO: Implement athlete status calculation based on attempt results
    // This would analyze each athlete's attempts to determine their current status

    // For now, just maintain existing statuses
}

void CompetitionState::calculateStatistics()
{
    m_activeAthletes = 0;
    m_completedAthletes = 0;
    m_retiredAthletes = 0;

    for (auto it = m_athleteStatuses.begin(); it != m_athleteStatuses.end(); ++it) {
        switch (it.value()) {
        case Active:
        case Passed:
            m_activeAthletes++;
            break;
        case Finished:
            m_completedAthletes++;
            break;
        case Retired:
            m_retiredAthletes++;
            break;
        default:
            break;
        }
    }

    emit statisticsUpdated(m_totalAthletes, m_activeAthletes, m_completedAthletes, m_retiredAthletes);
}

void CompetitionState::checkCompetitionEnd()
{
    if (m_currentPhase == InProgress && shouldFinishCompetition()) {
        emit competitionShouldFinish();
    }
}

bool CompetitionState::saveStateToDatabase()
{
    if (!m_dbManager || !m_competition) {
        return false;
    }

    // NOTE: Database integration planned for v1.1
    // Currently using in-memory storage only for v1.0
    // This would save the current state to the database in future version

    qDebug() << "CompetitionState::saveStateToDatabase: Database save not implemented in v1.0";
    return true; // Placeholder - returns true to maintain compatibility
}

bool CompetitionState::loadStateFromDatabase()
{
    if (!m_dbManager || !m_competition) {
        return false;
    }

    // NOTE: Database integration planned for v1.1
    // Currently using in-memory storage only for v1.0
    // This would load the state from the database in future version

    qDebug() << "CompetitionState::loadStateFromDatabase: Database load not implemented in v1.0";
    return true; // Placeholder - returns true to maintain compatibility
}
