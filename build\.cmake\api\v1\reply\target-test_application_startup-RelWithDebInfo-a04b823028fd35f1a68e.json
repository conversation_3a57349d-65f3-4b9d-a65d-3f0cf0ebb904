{"artifacts": [{"path": "bin/RelWithDebInfo/test_application_startup.exe"}, {"path": "bin/RelWithDebInfo/test_application_startup.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "include_directories"], "files": ["C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "tests/CMakeLists.txt", "C:/Qt/install-x64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Config.cmake", "CMakeLists.txt", "C:/Qt/install-x64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "C:/Qt/install-x64/lib/cmake/Qt6PrintSupport/Qt6PrintSupportTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6PrintSupport/Qt6PrintSupportConfig.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 161, "parent": 0}, {"command": 2, "file": 0, "line": 935, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 690, "parent": 3}, {"command": 4, "file": 1, "line": 187, "parent": 0}, {"file": 5}, {"command": 7, "file": 5, "line": 14, "parent": 6}, {"file": 4, "parent": 7}, {"command": 7, "file": 4, "line": 218, "parent": 8}, {"file": 3, "parent": 9}, {"command": 6, "file": 3, "line": 55, "parent": 10}, {"file": 2, "parent": 11}, {"command": 5, "file": 2, "line": 61, "parent": 12}, {"command": 7, "file": 4, "line": 218, "parent": 8}, {"file": 7, "parent": 14}, {"command": 6, "file": 7, "line": 55, "parent": 15}, {"file": 6, "parent": 16}, {"command": 5, "file": 6, "line": 61, "parent": 17}, {"command": 4, "file": 0, "line": 640, "parent": 2}, {"command": 7, "file": 4, "line": 218, "parent": 8}, {"file": 9, "parent": 20}, {"command": 6, "file": 9, "line": 57, "parent": 21}, {"file": 8, "parent": 22}, {"command": 5, "file": 8, "line": 61, "parent": 23}, {"command": 6, "file": 7, "line": 43, "parent": 15}, {"file": 14, "parent": 25}, {"command": 9, "file": 14, "line": 45, "parent": 26}, {"command": 8, "file": 13, "line": 137, "parent": 27}, {"command": 7, "file": 12, "line": 76, "parent": 28}, {"file": 11, "parent": 29}, {"command": 6, "file": 11, "line": 55, "parent": 30}, {"file": 10, "parent": 31}, {"command": 5, "file": 10, "line": 61, "parent": 32}, {"command": 7, "file": 4, "line": 218, "parent": 8}, {"file": 16, "parent": 34}, {"command": 6, "file": 16, "line": 55, "parent": 35}, {"file": 15, "parent": 36}, {"command": 5, "file": 15, "line": 61, "parent": 37}, {"command": 10, "file": 5, "line": 27, "parent": 6}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /Zi /O2 /Ob1 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 19, "fragment": "-Zc:__cplusplus"}, {"backtrace": 19, "fragment": "-permissive-"}, {"backtrace": 19, "fragment": "-utf-8"}], "defines": [{"backtrace": 19, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_NETWORK_LIB"}, {"backtrace": 19, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_PRINTSUPPORT_LIB"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 5, "define": "QT_TESTCASE_BUILDDIR=\"C:/PROJECT/HighJump/build/tests\""}, {"backtrace": 5, "define": "QT_TESTCASE_SOURCEDIR=\"C:/PROJECT/HighJump/tests\""}, {"backtrace": 5, "define": "QT_TESTLIB_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 19, "define": "UNICODE"}, {"backtrace": 19, "define": "WIN32"}, {"backtrace": 19, "define": "WIN64"}, {"backtrace": 19, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 19, "define": "_UNICODE"}, {"backtrace": 19, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/PROJECT/HighJump/build/tests/test_application_startup_autogen/include_RelWithDebInfo"}, {"backtrace": 39, "path": "C:/PROJECT/HighJump/src"}, {"backtrace": 19, "isSystem": true, "path": "C:/Qt/install-x64/include/QtCore"}, {"backtrace": 19, "isSystem": true, "path": "C:/Qt/install-x64/include"}, {"backtrace": 19, "isSystem": true, "path": "C:/Qt/install-x64/mkspecs/win32-arm64-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtWidgets"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtNetwork"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtTest"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtPrintSupport"}], "language": "CXX", "languageStandard": {"backtraces": [19, 19], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "test_application_startup::@a44f0ac069e85531cdee", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /Zi /O2 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Sql.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Network.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Test.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6PrintSupport.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Widgets.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Gui.lib", "role": "libraries"}, {"backtrace": 19, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Core.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "d3d12.lib", "role": "libraries"}, {"backtrace": 38, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 38, "fragment": "winspool.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "test_application_startup", "nameOnDisk": "test_application_startup.exe", "paths": {"build": "tests", "source": "tests"}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/tests/test_application_startup_autogen/mocs_compilation_RelWithDebInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "tests/integration/test_application_startup.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/main_window.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/competition_selection_view.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/view_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/scoring_view.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/models/competition.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/models/athlete.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/models/jump_attempt.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/api/api_client.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/api/competition_dto.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/persistence/database_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/utils/config_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/core/jump_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/athlete_dialog.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/report_dialog.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/theme_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/utils/performance_monitor.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/utils/report_generator.cpp", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}