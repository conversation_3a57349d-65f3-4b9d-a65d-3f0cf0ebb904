#include "api_client.h"
#include "utils/config_manager.h"

#include <QCoreApplication>
#include <QNetworkProxy>
#include <QSslConfiguration>
#include <QJsonDocument>
#include <QDebug>
#include <QMutexLocker>

// Static member initialization
ApiClient* ApiClient::s_instance = nullptr;
const int ApiClient::STATUS_CHECK_INTERVAL_MS = 30000; // 30 seconds
const QString ApiClient::USER_AGENT = "HighJumpScorer/1.0.0";

ApiClient* ApiClient::instance()
{
    if (s_instance == nullptr) {
        s_instance = new ApiClient();
    }
    return s_instance;
}

ApiClient::ApiClient(QObject *parent)
    : QObject(parent)
    , m_networkManager(nullptr)
    , m_configManager(nullptr)
    , m_statusCheckTimer(nullptr)
    , m_isOnline(false)
    , m_isInitialized(false)
{
    // Constructor implementation
}

ApiClient::~ApiClient()
{
    if (m_statusCheckTimer) {
        m_statusCheckTimer->stop();
    }
}

bool ApiClient::initialize()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_isInitialized) {
        return true;
    }
    
    qDebug() << "Initializing ApiClient...";
    
    // Get ConfigManager instance
    m_configManager = ConfigManager::instance();
    if (!m_configManager || !m_configManager->isInitialized()) {
        m_lastError = "ConfigManager not initialized";
        qCritical() << m_lastError;
        return false;
    }
    
    // Load configuration
    loadConfiguration();
    
    // Setup network manager
    if (!setupNetworkManager()) {
        qCritical() << "Failed to setup network manager";
        return false;
    }
    
    // Setup timers
    setupTimers();
    
    // Connect to configuration changes
    connectConfigManager();
    
    // Initial network status check
    checkNetworkStatus();
    
    m_isInitialized = true;
    qDebug() << "ApiClient initialized successfully";
    qDebug() << "API Base URL:" << m_baseUrl;
    qDebug() << "API Timeout:" << m_timeout << "ms";
    
    return true;
}

bool ApiClient::setupNetworkManager()
{
    m_networkManager = new QNetworkAccessManager(this);
    
    // Configure proxy settings (use system proxy)
    m_networkManager->setProxy(QNetworkProxy::applicationProxy());
    
    // Connect network manager signals
    connect(m_networkManager, &QNetworkAccessManager::finished,
            this, &ApiClient::onNetworkReplyFinished);
    
    // Configure SSL
    QSslConfiguration sslConfig = QSslConfiguration::defaultConfiguration();
    sslConfig.setProtocol(QSsl::TlsV1_2OrLater);
    QSslConfiguration::setDefaultConfiguration(sslConfig);
    
    return true;
}

void ApiClient::setupTimers()
{
    // Network status check timer
    m_statusCheckTimer = new QTimer(this);
    m_statusCheckTimer->setSingleShot(false);
    m_statusCheckTimer->setInterval(STATUS_CHECK_INTERVAL_MS);
    
    connect(m_statusCheckTimer, &QTimer::timeout,
            this, &ApiClient::onNetworkStatusCheckTimeout);
    
    // Start periodic status checks
    m_statusCheckTimer->start();
}

void ApiClient::connectConfigManager()
{
    connect(m_configManager, &ConfigManager::apiConfigurationChanged,
            this, &ApiClient::onConfigurationChanged);
}

void ApiClient::loadConfiguration()
{
    m_baseUrl = m_configManager->getApiBaseUrl();
    m_timeout = m_configManager->getApiTimeout();
    m_maxRetries = m_configManager->getApiMaxRetries();
    m_apiVersion = m_configManager->getApiVersion();
    
    qDebug() << "Loaded API configuration:";
    qDebug() << "  Base URL:" << m_baseUrl;
    qDebug() << "  Timeout:" << m_timeout << "ms";
    qDebug() << "  Max Retries:" << m_maxRetries;
    qDebug() << "  API Version:" << m_apiVersion;
}

bool ApiClient::isInitialized() const
{
    return m_isInitialized;
}

bool ApiClient::isOnline() const
{
    return m_isOnline;
}

void ApiClient::checkNetworkStatus()
{
    if (!m_networkManager) {
        updateNetworkStatus(false);
        return;
    }
    
    // Test connection with a simple health check
    testConnection();
}

void ApiClient::testConnection()
{
    if (!m_networkManager) {
        emit connectionTestFailed("Network manager not initialized");
        return;
    }
    
    // Create a simple test request
    QString testEndpoint = "/api/" + m_apiVersion + "/health";
    QNetworkRequest request = buildRequest(testEndpoint);
    
    QNetworkReply* reply = m_networkManager->get(request);
    
    // Set a shorter timeout for connection tests
    QTimer::singleShot(5000, reply, [reply]() {
        if (reply && reply->isRunning()) {
            reply->abort();
        }
    });
    
    // Handle the test response specifically
    connect(reply, &QNetworkReply::finished, [this, reply]() {
        bool success = false;
        QString error;
        
        if (reply->error() == QNetworkReply::NoError) {
            success = true;
            updateNetworkStatus(true);
        } else {
            error = reply->errorString();
            updateNetworkStatus(false);
        }
        
        emit connectionTestCompleted(success);
        if (!success) {
            emit connectionTestFailed(error);
        }
        
        reply->deleteLater();
    });
}

void ApiClient::fetchCompetitions()
{
    if (!m_networkManager) {
        qWarning() << "ApiClient::fetchCompetitions: Network manager not initialized";
        emit networkError("Network manager not initialized");
        return;
    }
    
    if (!m_isOnline) {
        qWarning() << "ApiClient::fetchCompetitions: System is offline";
        emit networkError("System is offline");
        return;
    }
    
    // Build request to competitions endpoint
    QString endpoint = "/api/" + m_apiVersion + "/competitions";
    QNetworkRequest request = buildRequest(endpoint);
    
    qDebug() << "ApiClient::fetchCompetitions: Requesting" << request.url();
    
    QNetworkReply* reply = m_networkManager->get(request);
    
    // Set timeout for the request
    QTimer::singleShot(m_timeout, reply, [reply]() {
        if (reply && reply->isRunning()) {
            qWarning() << "ApiClient::fetchCompetitions: Request timeout, aborting";
            reply->abort();
        }
    });
    
    // Handle the response
    connect(reply, &QNetworkReply::finished, [this, reply]() {
        if (reply->error() == QNetworkReply::NoError) {
            QJsonDocument document = QJsonDocument::fromJson(reply->readAll());
            
            if (document.isObject()) {
                QJsonObject response = document.object();
                
                if (response.contains("status") && response["status"].toString() == "success") {
                    if (response.contains("data") && response["data"].isArray()) {
                        QJsonArray competitions = response["data"].toArray();
                        qDebug() << "ApiClient::fetchCompetitions: Received" << competitions.size() << "competitions";
                        emit competitionsReceived(competitions);
                    } else {
                        qWarning() << "ApiClient::fetchCompetitions: Invalid response format - missing data array";
                        emit networkError("Invalid response format");
                    }
                } else {
                    QString errorMsg = response.contains("message") ? 
                        response["message"].toString() : "API returned error status";
                    qWarning() << "ApiClient::fetchCompetitions: API error:" << errorMsg;
                    emit networkError(errorMsg);
                }
            } else {
                qWarning() << "ApiClient::fetchCompetitions: Invalid JSON response";
                emit networkError("Invalid JSON response");
            }
        } else {
            QString error = reply->errorString();
            qWarning() << "ApiClient::fetchCompetitions: Network error:" << error;
            emit networkError(error);
            updateNetworkStatus(false);
        }
        
        reply->deleteLater();
    });
}

QNetworkRequest ApiClient::buildRequest(const QString &endpoint)
{
    QUrl url(m_baseUrl + endpoint);
    QNetworkRequest request(url);
    
    setCommonHeaders(request);
    setAuthHeaders(request);
    
    // Set timeout
    request.setTransferTimeout(m_timeout);
    
    return request;
}

void ApiClient::setCommonHeaders(QNetworkRequest &request)
{
    request.setHeader(QNetworkRequest::UserAgentHeader, USER_AGENT);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    request.setRawHeader("Accept", "application/json");
    request.setRawHeader("Cache-Control", "no-cache");
}

void ApiClient::setAuthHeaders(QNetworkRequest &request)
{
    // Add API version header
    request.setRawHeader("API-Version", m_apiVersion.toUtf8());
    
    // Add authentication token if available
    if (!m_authToken.isEmpty()) {
        request.setRawHeader("Authorization", QString("Bearer %1").arg(m_authToken).toUtf8());
    }
    
    // Add API key if available
    if (!m_apiKey.isEmpty()) {
        request.setRawHeader("X-API-Key", m_apiKey.toUtf8());
    }
    
    // Add user agent
    request.setRawHeader("User-Agent", "HighJumpScorer/1.0.0");
}

QNetworkReply* ApiClient::get(const QString &endpoint)
{
    if (!m_networkManager) {
        m_lastError = "Network manager not initialized";
        return nullptr;
    }
    
    QNetworkRequest request = buildRequest(endpoint);
    QNetworkReply* reply = m_networkManager->get(request);
    
    // Connect error handling
    connect(reply, &QNetworkReply::errorOccurred,
            this, &ApiClient::onNetworkError);
    connect(reply, &QNetworkReply::sslErrors,
            this, &ApiClient::onSslErrors);
    
    return reply;
}

QNetworkReply* ApiClient::post(const QString &endpoint, const QJsonObject &data)
{
    if (!m_networkManager) {
        m_lastError = "Network manager not initialized";
        return nullptr;
    }
    
    QNetworkRequest request = buildRequest(endpoint);
    QJsonDocument doc(data);
    QByteArray postData = doc.toJson(QJsonDocument::Compact);
    
    QNetworkReply* reply = m_networkManager->post(request, postData);
    
    // Connect error handling
    connect(reply, &QNetworkReply::errorOccurred,
            this, &ApiClient::onNetworkError);
    connect(reply, &QNetworkReply::sslErrors,
            this, &ApiClient::onSslErrors);
    
    return reply;
}

QNetworkReply* ApiClient::put(const QString &endpoint, const QJsonObject &data)
{
    if (!m_networkManager) {
        m_lastError = "Network manager not initialized";
        return nullptr;
    }
    
    QNetworkRequest request = buildRequest(endpoint);
    QJsonDocument doc(data);
    QByteArray putData = doc.toJson(QJsonDocument::Compact);
    
    QNetworkReply* reply = m_networkManager->put(request, putData);
    
    // Connect error handling
    connect(reply, &QNetworkReply::errorOccurred,
            this, &ApiClient::onNetworkError);
    connect(reply, &QNetworkReply::sslErrors,
            this, &ApiClient::onSslErrors);
    
    return reply;
}

QNetworkReply* ApiClient::deleteResource(const QString &endpoint)
{
    if (!m_networkManager) {
        m_lastError = "Network manager not initialized";
        return nullptr;
    }
    
    QNetworkRequest request = buildRequest(endpoint);
    QNetworkReply* reply = m_networkManager->deleteResource(request);
    
    // Connect error handling
    connect(reply, &QNetworkReply::errorOccurred,
            this, &ApiClient::onNetworkError);
    connect(reply, &QNetworkReply::sslErrors,
            this, &ApiClient::onSslErrors);
    
    return reply;
}

void ApiClient::handleResponse(QNetworkReply *reply)
{
    if (!reply) {
        return;
    }
    
    if (isSuccessResponse(reply)) {
        emit requestCompleted(reply);
    } else {
        QString error = QString("HTTP %1: %2")
                       .arg(reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt())
                       .arg(reply->errorString());
        emit requestFailed(reply, error);
    }
}

bool ApiClient::isSuccessResponse(QNetworkReply *reply)
{
    if (!reply) {
        return false;
    }
    
    int statusCode = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
    return (statusCode >= 200 && statusCode < 300);
}

QJsonObject ApiClient::parseJsonResponse(QNetworkReply *reply)
{
    if (!reply) {
        return QJsonObject();
    }
    
    QByteArray responseData = reply->readAll();
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(responseData, &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error:" << parseError.errorString();
        return QJsonObject();
    }
    
    return doc.object();
}

void ApiClient::updateNetworkStatus(bool isOnline)
{
    if (m_isOnline != isOnline) {
        bool wasOnline = m_isOnline;
        m_isOnline = isOnline;
        
        emit networkStatusChanged(isOnline);
        
        if (isOnline && !wasOnline) {
            qDebug() << "Network connection restored";
            emit connectionRestored();
        } else if (!isOnline && wasOnline) {
            qDebug() << "Network connection lost";
            emit connectionLost();
        }
    }
}

QString ApiClient::lastError() const
{
    return m_lastError;
}

void ApiClient::onNetworkReplyFinished()
{
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        return;
    }
    
    handleResponse(reply);
    reply->deleteLater();
}

void ApiClient::onNetworkError(QNetworkReply::NetworkError error)
{
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        return;
    }
    
    logNetworkError("Network error", reply);
    
    // Update network status based on error type
    if (error == QNetworkReply::UnknownNetworkError ||
        error == QNetworkReply::NetworkSessionFailedError ||
        error == QNetworkReply::TemporaryNetworkFailureError) {
        updateNetworkStatus(false);
    }
}

void ApiClient::onSslErrors(const QList<QSslError> &errors)
{
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        return;
    }
    
    for (const QSslError &error : errors) {
        qWarning() << "SSL Error:" << error.errorString();
    }
    
    // For development, we might want to ignore SSL errors
    // In production, this should be handled more carefully
    // reply->ignoreSslErrors();
}

void ApiClient::onNetworkStatusCheckTimeout()
{
    // Periodic network status check
    checkNetworkStatus();
}

void ApiClient::onConfigurationChanged()
{
    qDebug() << "API configuration changed, reloading...";
    loadConfiguration();
}

void ApiClient::logNetworkError(const QString &operation, QNetworkReply *reply)
{
    if (!reply) {
        return;
    }
    
    QString errorMsg = QString("%1 failed: %2 (HTTP %3)")
                      .arg(operation)
                      .arg(reply->errorString())
                      .arg(reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt());
    
    m_lastError = errorMsg;
    qWarning() << errorMsg;
}

QString ApiClient::getNetworkErrorString(QNetworkReply::NetworkError error)
{
    switch (error) {
    case QNetworkReply::NoError:
        return "No error";
    case QNetworkReply::ConnectionRefusedError:
        return "Connection refused";
    case QNetworkReply::RemoteHostClosedError:
        return "Remote host closed connection";
    case QNetworkReply::HostNotFoundError:
        return "Host not found";
    case QNetworkReply::TimeoutError:
        return "Timeout";
    case QNetworkReply::OperationCanceledError:
        return "Operation canceled";
    case QNetworkReply::SslHandshakeFailedError:
        return "SSL handshake failed";
    case QNetworkReply::TemporaryNetworkFailureError:
        return "Temporary network failure";
    case QNetworkReply::NetworkSessionFailedError:
        return "Network session failed";
    case QNetworkReply::UnknownNetworkError:
        return "Unknown network error";
    default:
        return QString("Network error %1").arg(static_cast<int>(error));
    }
}

// Authentication methods
void ApiClient::setAuthToken(const QString &token)
{
    QMutexLocker locker(&m_mutex);
    m_authToken = token;
    qDebug() << "Auth token set";
}

void ApiClient::setApiKey(const QString &key)
{
    QMutexLocker locker(&m_mutex);
    m_apiKey = key;
    qDebug() << "API key set";
}

QString ApiClient::getAuthToken() const
{
    QMutexLocker locker(&m_mutex);
    return m_authToken;
}

QString ApiClient::getApiKey() const
{
    QMutexLocker locker(&m_mutex);
    return m_apiKey;
}

void ApiClient::clearAuth()
{
    QMutexLocker locker(&m_mutex);
    m_authToken.clear();
    m_apiKey.clear();
    qDebug() << "Authentication cleared";
}