#include "competition_selection_view.h"
#include <QMessageBox>
#include <QDateTime>
#include <QDebug>

CompetitionSelectionView::CompetitionSelectionView(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_title<PERSON>abel(nullptr)
    , m_competitionsList(nullptr)
    , m_buttonLayout(nullptr)
    , m_selectButton(nullptr)
    , m_cancelButton(nullptr)
    , m_retryButton(nullptr)
    , m_progressBar(nullptr)
    , m_statusLabel(nullptr)
    , m_errorLabel(nullptr)
    , m_selectedId(-1)
    , m_isLoading(false)
{
    setupUI();
    connectSignals();
    updateButtonStates();
}

CompetitionSelectionView::~CompetitionSelectionView()
{
    // Qt父子对象系统会自动清理子控件
}

void CompetitionSelectionView::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);

    // 标题标签
    m_titleLabel = new QLabel(tr("选择比赛"), this);
    m_titleLabel->setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;");
    m_titleLabel->setAlignment(Qt::AlignCenter);

    // 状态标签
    m_statusLabel = new QLabel(tr("正在加载比赛列表..."), this);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    m_statusLabel->setStyleSheet("color: #666; font-size: 14px;");

    // 进度条
    m_progressBar = new QProgressBar(this);
    m_progressBar->setRange(0, 0); // 无限进度条
    m_progressBar->setVisible(false);

    // 错误标签
    m_errorLabel = new QLabel(this);
    m_errorLabel->setAlignment(Qt::AlignCenter);
    m_errorLabel->setStyleSheet("color: red; font-size: 14px; margin: 10px;");
    m_errorLabel->setWordWrap(true);
    m_errorLabel->setVisible(false);

    // 比赛列表
    m_competitionsList = new QListWidget(this);
    m_competitionsList->setSelectionMode(QAbstractItemView::SingleSelection);
    m_competitionsList->setMinimumHeight(300);
    m_competitionsList->setAlternatingRowColors(true);

    // 按钮布局
    m_buttonLayout = new QHBoxLayout();
    m_selectButton = new QPushButton(tr("选择比赛"), this);
    m_cancelButton = new QPushButton(tr("取消"), this);
    m_retryButton = new QPushButton(tr("重试"), this);

    m_selectButton->setEnabled(false);
    m_selectButton->setMinimumWidth(100);
    m_cancelButton->setMinimumWidth(100);
    m_retryButton->setMinimumWidth(100);
    m_retryButton->setVisible(false);

    m_buttonLayout->addStretch();
    m_buttonLayout->addWidget(m_retryButton);
    m_buttonLayout->addSpacing(10);
    m_buttonLayout->addWidget(m_selectButton);
    m_buttonLayout->addSpacing(10);
    m_buttonLayout->addWidget(m_cancelButton);
    m_buttonLayout->addStretch();

    // 主布局组装
    m_mainLayout->addWidget(m_titleLabel);
    m_mainLayout->addWidget(m_statusLabel);
    m_mainLayout->addWidget(m_progressBar);
    m_mainLayout->addWidget(m_errorLabel);
    m_mainLayout->addWidget(m_competitionsList);
    m_mainLayout->addLayout(m_buttonLayout);
    m_mainLayout->setContentsMargins(20, 20, 20, 20);

    setLayout(m_mainLayout);
}

void CompetitionSelectionView::connectSignals()
{
    connect(m_selectButton, &QPushButton::clicked,
            this, &CompetitionSelectionView::onSelectButtonClicked);

    connect(m_cancelButton, &QPushButton::clicked,
            this, &CompetitionSelectionView::onCancelButtonClicked);

    connect(m_retryButton, &QPushButton::clicked,
            this, &CompetitionSelectionView::onRetryButtonClicked);

    connect(m_competitionsList, &QListWidget::itemDoubleClicked,
            this, &CompetitionSelectionView::onListItemDoubleClicked);

    connect(m_competitionsList, &QListWidget::itemSelectionChanged,
            this, &CompetitionSelectionView::onListSelectionChanged);
}

void CompetitionSelectionView::loadCompetitions(const QJsonArray &competitions)
{
    m_competitions = competitions;
    m_competitionsList->clear();
    m_selectedId = -1;
    
    for (int i = 0; i < competitions.size(); ++i) {
        const QJsonObject competition = competitions[i].toObject();
        const QString displayText = formatCompetitionItem(competition);
        
        QListWidgetItem *item = new QListWidgetItem(displayText, m_competitionsList);
        item->setData(Qt::UserRole, competition.value("id").toInt());
        
        // 根据比赛状态设置不同的背景色
        const QString status = competition.value("status").toString();
        if (status == "in_progress") {
            item->setBackground(QColor(200, 255, 200)); // 浅绿色
        } else if (status == "finished") {
            item->setBackground(QColor(200, 200, 200)); // 浅灰色
        }
        
        m_competitionsList->addItem(item);
    }
    
    updateButtonStates();
}

int CompetitionSelectionView::selectedCompetitionId() const
{
    return m_selectedId;
}

void CompetitionSelectionView::clearCompetitions()
{
    m_competitionsList->clear();
    m_competitions = QJsonArray();
    m_selectedId = -1;
    updateButtonStates();
}

void CompetitionSelectionView::showLoadingState(bool loading, const QString &message)
{
    m_isLoading = loading;

    if (loading) {
        m_statusLabel->setText(message.isEmpty() ? tr("正在加载比赛列表...") : message);
        m_statusLabel->setVisible(true);
        m_progressBar->setVisible(true);
        m_errorLabel->setVisible(false);
        m_retryButton->setVisible(false);
        m_competitionsList->setEnabled(false);
    } else {
        m_statusLabel->setVisible(false);
        m_progressBar->setVisible(false);
        m_competitionsList->setEnabled(true);
    }

    updateButtonStates();
}

void CompetitionSelectionView::showError(const QString &errorMessage, bool allowRetry)
{
    m_isLoading = false;

    m_statusLabel->setVisible(false);
    m_progressBar->setVisible(false);
    m_errorLabel->setText(errorMessage);
    m_errorLabel->setVisible(true);
    m_retryButton->setVisible(allowRetry);
    m_competitionsList->setEnabled(false);

    updateButtonStates();
}

void CompetitionSelectionView::showOfflineMode()
{
    showError(tr("网络连接不可用，正在使用离线模式。\n某些功能可能受限。"), true);
}

void CompetitionSelectionView::onSelectButtonClicked()
{
    if (m_selectedId != -1) {
        emit competitionSelected(m_selectedId);
    }
}

void CompetitionSelectionView::onCancelButtonClicked()
{
    emit selectionCancelled();
}

void CompetitionSelectionView::onListItemDoubleClicked()
{
    if (m_selectedId != -1) {
        emit competitionSelected(m_selectedId);
    }
}

void CompetitionSelectionView::onListSelectionChanged()
{
    QListWidgetItem *currentItem = m_competitionsList->currentItem();
    if (currentItem) {
        m_selectedId = currentItem->data(Qt::UserRole).toInt();
    } else {
        m_selectedId = -1;
    }

    updateButtonStates();
}

void CompetitionSelectionView::onRetryButtonClicked()
{
    qDebug() << "CompetitionSelectionView::onRetryButtonClicked: User requested retry";
    emit retryRequested();
}

void CompetitionSelectionView::updateButtonStates()
{
    const bool hasSelection = (m_selectedId != -1) && !m_isLoading;
    m_selectButton->setEnabled(hasSelection);
}

QString CompetitionSelectionView::formatCompetitionItem(const QJsonObject &competition) const
{
    const QString name = competition.value("name").toString();
    const QString venue = competition.value("venue").toString();
    const QString dateStr = competition.value("date").toString();
    const QString status = competition.value("status").toString();
    
    // 格式化日期显示
    QString formattedDate;
    if (!dateStr.isEmpty()) {
        QDateTime dateTime = QDateTime::fromString(dateStr, Qt::ISODate);
        if (dateTime.isValid()) {
            formattedDate = dateTime.toString("yyyy-MM-dd hh:mm");
        } else {
            formattedDate = dateStr;
        }
    }
    
    // 状态显示转换
    QString statusDisplay;
    if (status == "not_started") {
        statusDisplay = tr("未开始");
    } else if (status == "in_progress") {
        statusDisplay = tr("进行中");
    } else if (status == "paused") {
        statusDisplay = tr("暂停");
    } else if (status == "finished") {
        statusDisplay = tr("已结束");
    } else {
        statusDisplay = status;
    }
    
    return QString("%1\n%2 | %3 | %4")
            .arg(name)
            .arg(formattedDate)
            .arg(venue)
            .arg(statusDisplay);
}