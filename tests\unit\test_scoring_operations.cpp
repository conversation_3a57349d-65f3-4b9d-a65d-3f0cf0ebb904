#include <QtTest>
#include <QApplication>
#include <QTemporaryDir>
#include <QJsonObject>
#include "persistence/database_manager.h"
#include "persistence/sync_queue_manager.h"

class TestScoringOperations : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Database recording tests
    void testRecordAttemptSuccess();
    void testRecordAttemptFailure();
    void testRecordAttemptSkip();
    void testRecordAttemptRetire();
    void testRecordAttemptInvalidData();

    // Sync queue tests
    void testSyncQueueAddOperation();
    void testSyncQueuePendingOperations();
    void testSyncQueueMarkCompleted();
    void testSyncQueueMarkFailed();

    // Integration tests
    void testScoringWorkflow();
    void testFocusAdvancement();

private:
    QTemporaryDir *m_tempDir;
    DatabaseManager *m_dbManager;
    SyncQueueManager *m_syncManager;
};

void TestScoringOperations::initTestCase()
{
    // Create temporary directory for test database
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());

    // Set up test database path
    QString dbPath = m_tempDir->path() + "/test_scoring.db";
    qputenv("TEST_DATABASE_PATH", dbPath.toUtf8());

    // Initialize database manager
    m_dbManager = DatabaseManager::instance();
    QVERIFY(m_dbManager->initialize());

    // Initialize sync queue manager
    m_syncManager = SyncQueueManager::instance();
}

void TestScoringOperations::cleanupTestCase()
{
    if (m_dbManager) {
        m_dbManager->close();
    }
    delete m_tempDir;
}

void TestScoringOperations::init()
{
    // Clean up any existing data before each test
    QVERIFY(m_dbManager->isConnected());
}

void TestScoringOperations::cleanup()
{
    // Clean up after each test if needed
}

void TestScoringOperations::testRecordAttemptSuccess()
{
    // Test recording a successful attempt
    int athleteId = 1;
    int height = 180;
    int attemptNumber = 1;
    QString result = "success";

    bool success = m_dbManager->recordAttempt(athleteId, height, attemptNumber, result);
    QVERIFY(success);

    // Verify the attempt was recorded (would need additional query methods)
    qDebug() << "Recorded successful attempt for athlete" << athleteId;
}

void TestScoringOperations::testRecordAttemptFailure()
{
    // Test recording a failed attempt
    int athleteId = 2;
    int height = 185;
    int attemptNumber = 1;
    QString result = "failure";

    bool success = m_dbManager->recordAttempt(athleteId, height, attemptNumber, result);
    QVERIFY(success);

    qDebug() << "Recorded failed attempt for athlete" << athleteId;
}

void TestScoringOperations::testRecordAttemptSkip()
{
    // Test recording a skip attempt
    int athleteId = 3;
    int height = 190;
    int attemptNumber = 1;
    QString result = "pass";

    bool success = m_dbManager->recordAttempt(athleteId, height, attemptNumber, result);
    QVERIFY(success);

    qDebug() << "Recorded skip attempt for athlete" << athleteId;
}

void TestScoringOperations::testRecordAttemptRetire()
{
    // Test recording a retirement
    int athleteId = 4;
    int height = 195;
    int attemptNumber = 1;
    QString result = "retire";

    bool success = m_dbManager->recordAttempt(athleteId, height, attemptNumber, result);
    QVERIFY(success);

    qDebug() << "Recorded retirement for athlete" << athleteId;
}

void TestScoringOperations::testRecordAttemptInvalidData()
{
    // Test recording with invalid data
    int athleteId = -1; // Invalid athlete ID
    int height = 180;
    int attemptNumber = 1;
    QString result = "success";

    // This should still succeed as we're not validating athlete existence yet
    bool success = m_dbManager->recordAttempt(athleteId, height, attemptNumber, result);
    QVERIFY(success); // For now, we accept any athlete ID
}

void TestScoringOperations::testSyncQueueAddOperation()
{
    // Test adding operation to sync queue
    QJsonObject data;
    data["athlete_id"] = 1;
    data["height"] = 180;
    data["attempt_number"] = 1;
    data["result"] = "success";

    int operationId = m_syncManager->addOperation(SyncQueueManager::CreateAttempt, data);
    QVERIFY(operationId > 0);

    qDebug() << "Added operation to sync queue with ID" << operationId;
}

void TestScoringOperations::testSyncQueuePendingOperations()
{
    // Add some operations first
    QJsonObject data1;
    data1["athlete_id"] = 1;
    data1["result"] = "success";

    QJsonObject data2;
    data2["athlete_id"] = 2;
    data2["result"] = "failure";

    m_syncManager->addOperation(SyncQueueManager::CreateAttempt, data1);
    m_syncManager->addOperation(SyncQueueManager::CreateAttempt, data2);

    // Get pending operations
    QList<SyncQueueManager::SyncQueueEntry> pending = m_syncManager->getPendingOperations();
    QVERIFY(pending.size() >= 2);

    qDebug() << "Found" << pending.size() << "pending operations";
}

void TestScoringOperations::testSyncQueueMarkCompleted()
{
    // Add an operation
    QJsonObject data;
    data["test"] = "completion";

    int operationId = m_syncManager->addOperation(SyncQueueManager::CreateAttempt, data);
    QVERIFY(operationId > 0);

    // Mark as completed
    bool success = m_syncManager->markAsCompleted(operationId);
    QVERIFY(success);

    qDebug() << "Marked operation" << operationId << "as completed";
}

void TestScoringOperations::testSyncQueueMarkFailed()
{
    // Add an operation
    QJsonObject data;
    data["test"] = "failure";

    int operationId = m_syncManager->addOperation(SyncQueueManager::CreateAttempt, data);
    QVERIFY(operationId > 0);

    // Mark as failed
    bool success = m_syncManager->markAsFailed(operationId, "Test error message");
    QVERIFY(success);

    qDebug() << "Marked operation" << operationId << "as failed";
}

void TestScoringOperations::testScoringWorkflow()
{
    // Test complete scoring workflow: record attempt + add to sync queue
    int athleteId = 10;
    int height = 200;
    int attemptNumber = 1;
    QString result = "success";

    // Record in database
    bool dbSuccess = m_dbManager->recordAttempt(athleteId, height, attemptNumber, result);
    QVERIFY(dbSuccess);

    // Add to sync queue
    QJsonObject syncData;
    syncData["athlete_id"] = athleteId;
    syncData["height"] = height;
    syncData["attempt_number"] = attemptNumber;
    syncData["result"] = result;

    int syncId = m_syncManager->addOperation(SyncQueueManager::CreateAttempt, syncData);
    QVERIFY(syncId > 0);

    // Verify pending count increased
    int pendingCount = m_syncManager->pendingCount();
    QVERIFY(pendingCount > 0);

    qDebug() << "Complete scoring workflow test passed";
}

void TestScoringOperations::testFocusAdvancement()
{
    // This test would require a more complex setup with Competition and CompetitionState
    // For now, we'll test the basic concept

    // Test that focus advancement logic works conceptually
    QList<int> athleteIds = {1, 2, 3, 4, 5};
    int currentAthleteId = 2;

    // Find next athlete (should be 3)
    int currentIndex = athleteIds.indexOf(currentAthleteId);
    int nextAthleteId = (currentIndex >= 0 && currentIndex < athleteIds.size() - 1)
                        ? athleteIds.at(currentIndex + 1)
                        : athleteIds.first();

    QCOMPARE(nextAthleteId, 3);

    // Test wrap-around (last athlete should go to first)
    currentAthleteId = 5;
    currentIndex = athleteIds.indexOf(currentAthleteId);
    nextAthleteId = (currentIndex >= 0 && currentIndex < athleteIds.size() - 1)
                    ? athleteIds.at(currentIndex + 1)
                    : athleteIds.first();

    QCOMPARE(nextAthleteId, 1);

    qDebug() << "Focus advancement logic test passed";
}

QTEST_MAIN(TestScoringOperations)
#include "test_scoring_operations.moc"
