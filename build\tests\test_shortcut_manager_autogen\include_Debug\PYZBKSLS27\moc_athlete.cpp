/****************************************************************************
** Meta object code from reading C++ file 'athlete.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../src/models/athlete.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'athlete.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN7AthleteE_t {};
} // unnamed namespace

template <> constexpr inline auto Athlete::qt_create_metaobjectdata<qt_meta_tag_ZN7AthleteE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "Athlete",
        "idChanged",
        "",
        "firstNameChanged",
        "lastNameChanged",
        "countryChanged",
        "clubChanged",
        "startNumberChanged",
        "personalBestChanged",
        "seasonBestChanged",
        "dateOfBirthChanged",
        "id",
        "firstName",
        "lastName",
        "country",
        "club",
        "startNumber",
        "personalBest",
        "seasonBest",
        "dateOfBirth"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'idChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'firstNameChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'lastNameChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'countryChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'clubChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'startNumberChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'personalBestChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'seasonBestChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'dateOfBirthChanged'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'id'
        QtMocHelpers::PropertyData<int>(11, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 0),
        // property 'firstName'
        QtMocHelpers::PropertyData<QString>(12, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 1),
        // property 'lastName'
        QtMocHelpers::PropertyData<QString>(13, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 2),
        // property 'country'
        QtMocHelpers::PropertyData<QString>(14, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 3),
        // property 'club'
        QtMocHelpers::PropertyData<QString>(15, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 4),
        // property 'startNumber'
        QtMocHelpers::PropertyData<int>(16, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 5),
        // property 'personalBest'
        QtMocHelpers::PropertyData<int>(17, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 6),
        // property 'seasonBest'
        QtMocHelpers::PropertyData<int>(18, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 7),
        // property 'dateOfBirth'
        QtMocHelpers::PropertyData<QDateTime>(19, QMetaType::QDateTime, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 8),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<Athlete, qt_meta_tag_ZN7AthleteE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject Athlete::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN7AthleteE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN7AthleteE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN7AthleteE_t>.metaTypes,
    nullptr
} };

void Athlete::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<Athlete *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->idChanged(); break;
        case 1: _t->firstNameChanged(); break;
        case 2: _t->lastNameChanged(); break;
        case 3: _t->countryChanged(); break;
        case 4: _t->clubChanged(); break;
        case 5: _t->startNumberChanged(); break;
        case 6: _t->personalBestChanged(); break;
        case 7: _t->seasonBestChanged(); break;
        case 8: _t->dateOfBirthChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (Athlete::*)()>(_a, &Athlete::idChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (Athlete::*)()>(_a, &Athlete::firstNameChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (Athlete::*)()>(_a, &Athlete::lastNameChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (Athlete::*)()>(_a, &Athlete::countryChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (Athlete::*)()>(_a, &Athlete::clubChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (Athlete::*)()>(_a, &Athlete::startNumberChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (Athlete::*)()>(_a, &Athlete::personalBestChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (Athlete::*)()>(_a, &Athlete::seasonBestChanged, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (Athlete::*)()>(_a, &Athlete::dateOfBirthChanged, 8))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->id(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->firstName(); break;
        case 2: *reinterpret_cast<QString*>(_v) = _t->lastName(); break;
        case 3: *reinterpret_cast<QString*>(_v) = _t->country(); break;
        case 4: *reinterpret_cast<QString*>(_v) = _t->club(); break;
        case 5: *reinterpret_cast<int*>(_v) = _t->startNumber(); break;
        case 6: *reinterpret_cast<int*>(_v) = _t->personalBest(); break;
        case 7: *reinterpret_cast<int*>(_v) = _t->seasonBest(); break;
        case 8: *reinterpret_cast<QDateTime*>(_v) = _t->dateOfBirth(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setId(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setFirstName(*reinterpret_cast<QString*>(_v)); break;
        case 2: _t->setLastName(*reinterpret_cast<QString*>(_v)); break;
        case 3: _t->setCountry(*reinterpret_cast<QString*>(_v)); break;
        case 4: _t->setClub(*reinterpret_cast<QString*>(_v)); break;
        case 5: _t->setStartNumber(*reinterpret_cast<int*>(_v)); break;
        case 6: _t->setPersonalBest(*reinterpret_cast<int*>(_v)); break;
        case 7: _t->setSeasonBest(*reinterpret_cast<int*>(_v)); break;
        case 8: _t->setDateOfBirth(*reinterpret_cast<QDateTime*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *Athlete::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *Athlete::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN7AthleteE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int Athlete::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 9)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 9)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 9;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 9;
    }
    return _id;
}

// SIGNAL 0
void Athlete::idChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void Athlete::firstNameChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void Athlete::lastNameChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void Athlete::countryChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void Athlete::clubChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void Athlete::startNumberChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void Athlete::personalBestChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void Athlete::seasonBestChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void Athlete::dateOfBirthChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}
QT_WARNING_POP
