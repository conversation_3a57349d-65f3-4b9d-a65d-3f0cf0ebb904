{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-Debug-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-d0d60282db94b45200c4.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-Debug-cbe96617fb1ec53bec82.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-Debug-3c1ad7f2127ca7ee836d.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_application_startup::@a44f0ac069e85531cdee", "jsonFile": "target-test_application_startup-Debug-6c4b1d48224bde9a511c.json", "name": "test_application_startup", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_delegate::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_delegate-Debug-4285ef9172f60c4708dd.json", "name": "test_athlete_delegate", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_table_model::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_table_model-Debug-cfdb1497c1177f4a102c.json", "name": "test_athlete_table_model", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_competition_selection_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_competition_selection_view-Debug-f6a901c49d724e46f0e3.json", "name": "test_competition_selection_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-Debug-1926016304c502624bdb.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-Debug-0826a6927e549d439953.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_simple::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_simple-Debug-8bb8d50711b476c6479c.json", "name": "test_e2e_simple", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_large_scale_performance::@a44f0ac069e85531cdee", "jsonFile": "target-test_large_scale_performance-Debug-7919461778be859207eb.json", "name": "test_large_scale_performance", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_scoring_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_scoring_view-Debug-5471ba5358547e904fbc.json", "name": "test_scoring_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_shortcut_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_shortcut_manager-Debug-24da2b2947fc45d37647.json", "name": "test_shortcut_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_ui_ux_usability::@a44f0ac069e85531cdee", "jsonFile": "target-test_ui_ux_usability-Debug-04e96178803fccffa647.json", "name": "test_ui_ux_usability", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-Release-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}], "name": "Release", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-d0d60282db94b45200c4.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-Release-a173d6c5301be0b98c5f.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-Release-8066c29e6d5db33a2d41.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_application_startup::@a44f0ac069e85531cdee", "jsonFile": "target-test_application_startup-Release-7089545aad5e2e882b78.json", "name": "test_application_startup", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_delegate::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_delegate-Release-ac351e5ef42791a7ea3e.json", "name": "test_athlete_delegate", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_table_model::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_table_model-Release-bd846fc9b9fad46b2a61.json", "name": "test_athlete_table_model", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_competition_selection_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_competition_selection_view-Release-af24b68acfefdbaba6fc.json", "name": "test_competition_selection_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-Release-7b06ba37ab50ed31aba3.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-Release-0608fcbc15d70ab13bee.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_simple::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_simple-Release-ac970917e2f2fb0fbaa1.json", "name": "test_e2e_simple", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_large_scale_performance::@a44f0ac069e85531cdee", "jsonFile": "target-test_large_scale_performance-Release-ea7750680846f780e46f.json", "name": "test_large_scale_performance", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_scoring_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_scoring_view-Release-2888066fea36c717612c.json", "name": "test_scoring_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_shortcut_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_shortcut_manager-Release-bc5c753f582e137457bd.json", "name": "test_shortcut_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_ui_ux_usability::@a44f0ac069e85531cdee", "jsonFile": "target-test_ui_ux_usability-Release-3dd4f4a56ddd38f095ee.json", "name": "test_ui_ux_usability", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-MinSizeRel-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-d0d60282db94b45200c4.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-MinSizeRel-83c2ea2529ebb0e2aa2c.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-MinSizeRel-d77914718bb915c0bb75.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_application_startup::@a44f0ac069e85531cdee", "jsonFile": "target-test_application_startup-MinSizeRel-1db365a4dccd8a8f9e41.json", "name": "test_application_startup", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_delegate::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_delegate-MinSizeRel-47a83a09bcc9416bcf30.json", "name": "test_athlete_delegate", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_table_model::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_table_model-MinSizeRel-2078a0ace1ad345bef9c.json", "name": "test_athlete_table_model", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_competition_selection_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_competition_selection_view-MinSizeRel-211f94dd507d920d1043.json", "name": "test_competition_selection_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-MinSizeRel-e419e6ab11c908211780.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-MinSizeRel-b728566ec9f2311308ee.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_simple::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_simple-MinSizeRel-8839f2d2c627a1ada977.json", "name": "test_e2e_simple", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_large_scale_performance::@a44f0ac069e85531cdee", "jsonFile": "target-test_large_scale_performance-MinSizeRel-7f3afe0785cb9e093a05.json", "name": "test_large_scale_performance", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_scoring_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_scoring_view-MinSizeRel-fba238e6de0452034c13.json", "name": "test_scoring_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_shortcut_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_shortcut_manager-MinSizeRel-17401dcc45c872ae3cf4.json", "name": "test_shortcut_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_ui_ux_usability::@a44f0ac069e85531cdee", "jsonFile": "target-test_ui_ux_usability-MinSizeRel-bda6ab490c39ccf7ef41.json", "name": "test_ui_ux_usability", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-RelWithDebInfo-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-d0d60282db94b45200c4.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-RelWithDebInfo-9e0314e41e671d988331.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-RelWithDebInfo-e2974f4d505336ba11b8.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_application_startup::@a44f0ac069e85531cdee", "jsonFile": "target-test_application_startup-RelWithDebInfo-a04b823028fd35f1a68e.json", "name": "test_application_startup", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_delegate::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_delegate-RelWithDebInfo-cb94da5d399368114d0f.json", "name": "test_athlete_delegate", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_table_model::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_table_model-RelWithDebInfo-c24f712589f9976b8d6c.json", "name": "test_athlete_table_model", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_competition_selection_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_competition_selection_view-RelWithDebInfo-691181a0a78a2558a8e0.json", "name": "test_competition_selection_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-RelWithDebInfo-cbd479182b2fec2dfffa.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-RelWithDebInfo-aac9cb976d69cbda5e7c.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_simple::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_simple-RelWithDebInfo-de288df03acb82c144ff.json", "name": "test_e2e_simple", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_large_scale_performance::@a44f0ac069e85531cdee", "jsonFile": "target-test_large_scale_performance-RelWithDebInfo-5133be264522c97c2319.json", "name": "test_large_scale_performance", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_scoring_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_scoring_view-RelWithDebInfo-a532267d39d537ba8033.json", "name": "test_scoring_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_shortcut_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_shortcut_manager-RelWithDebInfo-c2ebbc1825712ed1ee42.json", "name": "test_shortcut_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_ui_ux_usability::@a44f0ac069e85531cdee", "jsonFile": "target-test_ui_ux_usability-RelWithDebInfo-f7c5167d4abbbd35a287.json", "name": "test_ui_ux_usability", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/PROJECT/HighJump/build", "source": "C:/PROJECT/HighJump"}, "version": {"major": 2, "minor": 8}}