/****************************************************************************
** Meta object code from reading C++ file 'view_manager.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../src/ui/view_manager.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'view_manager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN11ViewManagerE_t {};
} // unnamed namespace

template <> constexpr inline auto ViewManager::qt_create_metaobjectdata<qt_meta_tag_ZN11ViewManagerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ViewManager",
        "viewChangeRequested",
        "",
        "ViewType",
        "fromType",
        "toType",
        "viewChanged",
        "viewType",
        "viewChangeFailed",
        "errorMessage",
        "onViewWidgetDestroyed",
        "obj"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'viewChangeRequested'
        QtMocHelpers::SignalData<void(ViewType, ViewType)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 }, { 0x80000000 | 3, 5 },
        }}),
        // Signal 'viewChanged'
        QtMocHelpers::SignalData<void(ViewType)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 7 },
        }}),
        // Signal 'viewChangeFailed'
        QtMocHelpers::SignalData<void(ViewType, const QString &)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 7 }, { QMetaType::QString, 9 },
        }}),
        // Slot 'onViewWidgetDestroyed'
        QtMocHelpers::SlotData<void(QObject *)>(10, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QObjectStar, 11 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ViewManager, qt_meta_tag_ZN11ViewManagerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ViewManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11ViewManagerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11ViewManagerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN11ViewManagerE_t>.metaTypes,
    nullptr
} };

void ViewManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ViewManager *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->viewChangeRequested((*reinterpret_cast< std::add_pointer_t<ViewType>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<ViewType>>(_a[2]))); break;
        case 1: _t->viewChanged((*reinterpret_cast< std::add_pointer_t<ViewType>>(_a[1]))); break;
        case 2: _t->viewChangeFailed((*reinterpret_cast< std::add_pointer_t<ViewType>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 3: _t->onViewWidgetDestroyed((*reinterpret_cast< std::add_pointer_t<QObject*>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ViewManager::*)(ViewType , ViewType )>(_a, &ViewManager::viewChangeRequested, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ViewManager::*)(ViewType )>(_a, &ViewManager::viewChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ViewManager::*)(ViewType , const QString & )>(_a, &ViewManager::viewChangeFailed, 2))
            return;
    }
}

const QMetaObject *ViewManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ViewManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11ViewManagerE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ViewManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void ViewManager::viewChangeRequested(ViewType _t1, ViewType _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2);
}

// SIGNAL 1
void ViewManager::viewChanged(ViewType _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void ViewManager::viewChangeFailed(ViewType _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1, _t2);
}
QT_WARNING_POP
